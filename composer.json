{"name": "bricklayer/lay", "description": "A lite PHP builder meta-framework to get your projects up and running quickly", "keywords": ["bricklayer", "lay", "php bricklayer", "lite php framework", "metaframework", "fast php framework"], "type": "library", "license": "MIT", "autoload": {"psr-4": {"Bricks\\": "bricks/", "Utils\\": "utils/", "Web\\": "web/domains/"}}, "repositories": [{"type": "vcs", "url": "**************:osaitech/elevator-php-frontend.git"}, {"type": "vcs", "url": "**************:osaitech/elevator.git"}], "scripts": {"post-autoload-dump": ["@php bob project:create --refresh-links"], "post-create-project-cmd": ["@php bob project:create --fresh-project"]}, "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">8.1", "bricklayer/structure": "*", "aws/aws-sdk-php": "^3.324", "osaitech/elevator": "0.3.*-dev", "osaitech/elevator-frontend": "dev-main", "robmorgan/phinx": "^0.16.9", "spatie/browsershot": "^5.0"}}