<?php

declare(strict_types=1);

final class FormApplicationTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table() : string
    {
        return \Bricks\Business\Model\FormApplication::$table;
    }

    protected function exclude_columns() : array
    {
        return ['client_id'];
    }

    protected function model_columns() : array
    {
        return [
            "form_id" => [
                "type" => "string",
                "null" => false,
                "unique" => true
            ],
            "form_type" => [
                "type" => "string",
                "null" => false,
            ],
            "props" => [
                "type" => "jsonb",
                "null" => false,
            ],
            "status" => [
                "type" => "string",
                "null" => false,
                "default" => \Bricks\Business\Enums\FormStatus::NOT_STARTED->name
            ],
            "pdf_path" => [
                "type" => "text",
                "null" => true,
            ],
            "assigned_to" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
            "assisted_by" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
