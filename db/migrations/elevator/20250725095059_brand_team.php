<?php

declare(strict_types=1);

final class BrandTeam extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\Brand\Model\ElevatorBrandTeamModel::$table;
    }

    protected function pre_create(\Phinx\Db\Table $migration): void
    {
        $migration->addIndex(['name', 'client_id'], ['unique' => true]);
    }

    protected function model_columns(): array
    {
        return [
            "brand_id" => [
                "type" => "char",
                "length" => 36,
                "null" => false
            ],
            "name" => [
                "type" => "string",
                "limit" => 150,
                "null" => false,
            ],
            "slug" => [
                "type" => "text",
                "null" => false,
            ],
            "position" => [
                "type" => "string",
                "length" => 150,
                "null" => false,
            ],
            "dp" => [
                "type" => "char",
                "limit" => 36,
                "null" => true,
            ],
            "intro" => [
                "type" => "string",
                "length" => 255,
                "null" => true,
            ],
            "about" => [
                "type" => "text",
                "null" => true,
            ],
            "social" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "order" => [
                "type" => "integer",
                "null" => false,
            ],
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
