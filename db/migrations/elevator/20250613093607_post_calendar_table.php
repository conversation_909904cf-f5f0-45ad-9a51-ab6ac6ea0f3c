<?php

declare(strict_types=1);

use Elevator\Blog\Enums\PostStatus;

final class PostCalendarTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\Blog\Model\ElevatorPostCalendar::$table;
    }

    protected function model_columns(): array
    {
        return [
            "post_id" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
            "post_title" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
                "unique" => true,
            ],
            "post_categories" => [
                "type" => "jsonb",
                "null" => false,
            ],
            "post_summary" => [
                "type" => "string",
                "length" => 300,
                "null" => true,
            ],
            "audience" => [
                "type" => "string",
                "length" => 200,
                "null" => true,
            ],
            "goals" => [
                "type" => "string",
                "length" => 300,
                "null" => true,
            ],
            "search_intents" => [
                "type" => "jsonb",
                "null" => false,
            ],
            "pry_keyword" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
            ],
            "sec_keywords" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "post_type" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],
            "author" => [
                "type" => "char",
                "length" => 36,
                "null" => true,
            ],
            "status" => [
                "type" => "string",
                "length" => 100,
                "null" => false,
                "default" => PostStatus::NOT_STARTED->name
            ],
            "publish_date" => [
                "type" => "date",
                "null" => true,
            ],
            "word_count" => [
                "type" => "integer",
                "null" => true,
            ],
            "cta" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],
            "internal_links" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "external_links" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "assets_needed" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "social_channels" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "kpis" => [
                "type" => "jsonb",
                "null" => true,
            ],

        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
