<?php

declare(strict_types=1);


final class UserProfileTable extends \Elevator\Utils\ElevatorBaseMigration
{
    protected function model_table(): string
    {
        return \Elevator\User\Model\ElevatorProfileModel::$table;
    }

    protected function model_columns(): array
    {
        return [
            "profile_id" => [
                "type" => "string",
                "null" => false,
            ],
            "auth_id" => [
                "type" => "char",
                "null" => false,
                "length" => 36,
            ],
            "first_name" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],
            "middle_name" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],
            "last_name" => [
                "type" => "string",
                "length" => 100,
                "null" => true,
            ],
            "refresh_session" => [
                "type" => "integer",
                "null" => false,
                "length" => 1,
                "default" => 0,
            ],
            "x_permissions" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "role" => [
                "type" => "char",
                "null" => true,
                "length" => 36,
            ],
            "position" => [
                "type" => "char",
                "null" => true,
                "length" => 36,
            ],
            "about" => [
                "type" => "text",
                "null" => true,
            ],
            "dp" => [
                "type" => "text",
                "null" => true,
            ],
            "slug" => [
                "type" => "text",
                "null" => true,
            ],
            "social" => [
                "type" => "jsonb",
                "null" => true,
            ],
            "dataset" => [
                "type" => "jsonb",
                "null" => true,
            ]
        ];
    }

    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * https://book.cakephp.org/phinx/0/en/migrations.html#the-change-method
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change(): void
    {
        $this->migrate_model();
    }
}
