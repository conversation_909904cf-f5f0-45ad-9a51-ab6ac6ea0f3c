<?php

declare(strict_types=1);

use BrickLayer\Lay\Core\LayConfig;
use Phinx\Seed\AbstractSeed;

class StateSeeder extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeders is available here:
     * https://book.cakephp.org/phinx/0/en/seeding.html
     */
    public function run(): void
    {
        LayConfig::connect();

        (new \Elevator\Country\Model\ElevatorStateModel())->add_batch(
            json_decode(file_get_contents(__DIR__ . "/data/country_states.json"), true)
        );
    }
}
