<?php

declare(strict_types=1);

use <PERSON><PERSON><PERSON>er\Lay\Core\LayConfig;
use BrickLayer\Lay\Libs\FileUpload\Enums\FileUploadExtension;
use Elevator\Country\Model\ElevatorCountryModel;
use Phinx\Seed\AbstractSeed;

class FileStoreTypeSeeder extends AbstractSeed
{
    /**
     * Run Method.
     *
     * Write your database seeder using this method.
     *
     * More information on writing seeders is available here:
     * https://book.cakephp.org/phinx/0/en/seeding.html
     */
    public function run(): void
    {
        LayConfig::connect();

        (new \Elevator\FileStore\Model\ElevatorFileStoreTypeModel)->add_batch([
            [
                "name" => "Blog Thumbnail",
                "slug" => "blog-thumbnail", // Used in various parts of the project. Do not change!
                "max_size" => 3e6, // 3MB
                "max_dimension" => ["width" => 1080, "height" => 1080],
                "allowed_types" => [ FileUploadExtension::PICTURE->name ],
                "can_delete" => '0',
            ],
            [
                "name" => "User DP",
                "slug" => "user-dp", // Used in various part of the project. Do not change!
                "max_size" => 3e6, // 3MB
                "max_dimension" => ["width" => 800, "height" => 800],
                "allowed_types" => [ FileUploadExtension::PICTURE->name ],
                "can_delete" => '0',
            ],
            [
                "name" => "Brand Logo",
                "slug" => "brand-logo", // Used in various part of the project. Do not change!
                "max_size" => 3e6, // 3MB
                "max_dimension" => ["width" => 800, "height" => 800],
                "allowed_types" => [ FileUploadExtension::PICTURE->name ],
                "can_delete" => '0',
            ],
            [
                "name" => "Brand Cover Photo",
                "slug" => "brand-cover-photo", // Used in various part of the project. Do not change!
                "max_size" => 4e6, // 4MB
                "max_dimension" => ["width" => 1584, "height" => 396],
                "allowed_types" => [ FileUploadExtension::PICTURE->name ],
                "can_delete" => '0',
            ],

        ]);

    }
}
