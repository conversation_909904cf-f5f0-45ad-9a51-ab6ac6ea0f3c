<?php

namespace Bricks\Business\Controller;

use BrickLayer\Lay\Core\Api\Enums\ApiStatus;
use BrickLayer\Lay\Core\LayConfig;
use BrickLayer\Lay\Core\LayException;
use BrickLayer\Lay\Libs\Dir\LayDir;
use BrickLayer\Lay\Libs\Primitives\Traits\ControllerHelper;
use BrickLayer\Lay\Libs\Primitives\Traits\IsSingleton;
use BrickLayer\Lay\Orm\SQL;
use Bricks\Business\DTO\DocumentPreviewModel;
use Bricks\Business\Enums\FormStatus;
use Bricks\Business\Enums\FormType;
use Bricks\Business\Model\FormApplication;
use Bricks\Business\Request\AssignStaffRequest;
use Bricks\Business\Request\StudentApplicationRequest;
use Bricks\Business\Request\VisaApplicationRequest;
use Bricks\Business\Resource\FormApplicantResource;
use Elevator\Utils\Enums\LogActivityTypes;
use Elevator\Utils\Interface\PermissionInterface;
use Elevator\Utils\Traits\ActivityLog;
use Spatie\Browsershot\Browsershot;
use Utils\Enums\AppPermission;
use Utils\Traits\HasAppFoundation;

class FormApplicationController
{
    use IsSingleton, ControllerHelper;
    use HasAppFoundation, ActivityLog;

    public function model() : FormApplication
    {
        return new FormApplication();
    }

    public function module_id() : PermissionInterface
    {
        return AppPermission::FormApp;
    }

    public function search() : array
    {
        $query = $_GET['query'] ?? null;

        if(empty($query))
            return self::res_warning("Search query is empty");

        $query = self::clean("$query", strict: false);

        return FormApplicantResource::collect($this->model()->search(trim($query)));
    }

    public function add(string $slug): array
    {
        if($slug == "visa-assistance") {
            $request = new VisaApplicationRequest();
            $admin_email = LayConfig::site_data()->mail->{1};
        }

        if($slug == "intl-admission") {
            $request = new StudentApplicationRequest();
            $admin_email = LayConfig::site_data()->mail->{2};
        }

        if(!isset($request))
            return self::res_warning("An invalid form has been submitted, ensure you submit the proper way");

        if ($request->error)
            return self::res_warning($request->error);

        $model = $this->model();

        if($model->is_duplicate($request))
            return self::res_warning(
                "You have submitted this request already. 
                If you want to update your details, contact us via our contact channels with this ref number: "
                . "<b>" . $model->form_id . "</b>."
                . " Or click the 'New Application' button to start a new application"
            );

        $cols = [
            "id" => $request->id,
            "form_id" => $model->gen_id($request->form_type),
            "form_type" => $request->form_type,
            "created_by" => "website-applicant"
        ];

        $request->unset("id", "form_type");

        $cols['props'] = json_encode($request->props());

        $model->add($cols);

        if ($model->is_empty())
            return self::res_warning("Could not submit the form at the moment, please try again later");

        $type = $model->form_type->stringify();
        $mailer = self::foundation()->mailer();
        $preview = $this->build_preview($model);

        try {
            LayException::in_try();
            $pdf = $this->make_pdf($preview);

            $model->edit_self([ "pdf_path" => $pdf ]);

            $mailer->confirm_applicant_submission([
                "ref_num" => $model->form_id,
                "type" => $type,
                "email" => $model->props['email'],
                "name" => $model->props['last_name'],
            ]);

            $mailer->notify_admin_of_applicant_submission([
                "email" => $admin_email,
                "name" => "Admin",
                "preview" => $preview,
                "pdf" => $pdf,
                "type" => $type,
            ]);
        } catch (\Throwable $e) {
            return self::res_error(
                "Server error, please contact support with the Ref Number: <b>" . $cols['form_id'] . "</b>",
                code: ApiStatus::INTERNAL_SERVER_ERROR,
                exception: $e,
            );
        }

        return self::res_success("Congratulations! form application submitted successfully", [
            'ref_num' => $cols['form_id']
        ]);
    }

    public function build_preview(FormApplication $model) : string
    {
        $preview = "";
        $props = $model->props;

        foreach (DocumentPreviewModel::$map as $map) {
            foreach ($props as $name => $prop) {
                if(in_array($name, $map['keys'])){
                    $label = $map['label'];
                    $type = $map['type'] ?? "text";

                    if(is_array($prop)) {
                        if($type == 'country') {
                            $prop = self::foundation()->country()->model()->each(fn($x) => $x->name)->get_by_agr($prop, "iso3");
                        }

                        if($type == 'file') {
                            $prop = self::foundation()->file_store()->model()->each(fn($x) => "<a target='_blank' href='$x->url'>" . $x->name . "</a>")->get_by_agr($prop, "id");
                        }

                        $prop = implode(", ", $prop);
                    } else {
                        if($type == 'country') {
                            $prop = self::foundation()->country()->by_iso3($prop)->name;
                        }

                        if($type == 'file') {
                            $x = self::foundation()->file_store()->model()->fill($prop);
                            $prop = "<a target='_blank' href='$x->url'>" . $x->name . "</a>";
                        }
                    }


                    $preview .= "<div style='display: flex; margin: 15px auto; padding: 5px 0; border-bottom: solid 1px #cecece; width: 95%;'><div style='margin-right: 15px; width: 30%; font-weight: 600'>$label:</div> <div>$prop</div></div>";
                    unset($props[$name]);
                    break;
                }
            }
        }

        return $preview;
    }

    public function view_details(string $id) : ?string
    {
        $model = $this->model()->fill(self::clean($id));

        if($model->is_empty())
            return null;

        return $this->build_preview($model);
    }

    public function make_pdf(string $preview) : string
    {
        $r_path = SQL::new()->uuid() . ".pdf";
        $path = LayConfig::server_data()->temp . "pdf/";

        LayDir::make($path);

        Browsershot::html($preview)
            ->format("A4")
            ->save($path . $r_path);

        return "pdf/" . $r_path;
    }


    private function notify_staff_of_role(AssignStaffRequest $request, string $role) : void
    {
        self::foundation()->mailer()->notify_staff_of_assignment([
            "name" => $request->staff->concat_name(),
            "email" => self::foundation()->user_auth()->model()->fill($request->staff->auth_id)->login_name,
            "role" => $role,
            "form" => $request->form->form_id,
            "applicant" => $request->form->props['last_name'],
        ]);

    }

    public function assign_to_staff() : array
    {
        $request = new AssignStaffRequest();

        if($request->error)
            return self::res_warning($request->error);

        if($request->form->assisted_by == $request->staff->id)
            return self::res_warning(
                "Staff is already serving as an assistant. If you don't want this, remove the staff as an assistant and make them the assigned staff"
            );

        $edited = $request->form->edit_self([
            "assigned_to" => $request->staff->id
        ]);

        if(!$edited)
            return self::res_warning("Could not update assigned staff at the moment, please try again later");

        if($request->notify_staff)
            $this->notify_staff_of_role($request, "Staff-in-Charge");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Updated assigned staff of applicant [{$request->form->form_id}] from {$request->form->assigned_to} to {$request->staff->id}"
        );

        return self::res_success("Assigned staff has been updated");
    }

    public function assist_to_staff() : array
    {
        $request = new AssignStaffRequest();

        if($request->error)
            return self::res_warning($request->error);

        if($request->form->assigned_to == $request->staff->id)
            return self::res_warning(
                "Staff is the staff-in-charge already!"
            );

        if(!$request->form->assigned_to)
            return self::res_warning(
                "There is no staff-in-charge for this applicant. You need to first assign a staff-in-charge before assigning an assistant"
            );

        $edited = $request->form->edit_self([
            "assisted_by" => $request->staff->id
        ]);

        if(!$edited)
            return self::res_warning("Could not update assistant staff at the moment, please try again later");

        if($request->notify_staff)
            $this->notify_staff_of_role($request, "Staff Assistant");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Updated staff assistant of applicant [{$request->form->form_id}] from {$request->form->assisted_by} to {$request->staff->id}"
        );

        return self::res_success("Assistant staff has been updated");
    }

    public function remove_staff(string $id) : array
    {
        $model = $this->model()->fill($id);

        if($model->is_empty())
            return self::res_warning("Applicant does not exist");

        $edited = $model->edit_self([
            "assigned_to" => null
        ]);

        if(!$edited)
            return self::res_warning("Could not remove Staff-in-Charge at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Staff-in-Charge of applicant [$model->form_id] who was $model->assigned_to has been removed"
        );

        return self::res_success("Staff-in-Charge has been removed successfully");
    }

    public function remove_assistant(string $id) : array
    {
        $model = $this->model()->fill($id);

        if($model->is_empty())
            return self::res_warning("Applicant does not exist");

        $edited = $model->edit_self([
            "assisted_by" => null
        ]);

        if(!$edited)
            return self::res_warning("Could not remove assistant staff at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Assistant Staff of applicant [$model->form_id] who was $model->assisted_by has been removed"
        );

        return self::res_success("Assistant Staff has been removed successfully");
    }

    public function update_status(string $status) : array
    {
        $id = self::request(false)->id;
        self::cleanse($id);

        $model = $this->model()->fill($id);

        if($model->is_empty())
            return self::res_warning("Invalid form applicant received!");

        $edited = $model->edit_self([
            "status" => $status,
        ]);

        if(!$edited)
            self::res_warning("Could not update status at the moment, please try again later");

        $this->activity_log(
            LogActivityTypes::UPDATE,
            "Applicant with Form ID: $model->form_id status was updated from {$model->status->name} to $status"
        );

        return self::res_success("Status updated successfully");
    }

    public function list(string|FormType $type, ?FormStatus $status) : array
    {
        $range = @self::request(false)->range;

        if($range)
            self::cleanse($range);

        if(is_string($type))
            $type = FormType::to_enum($type);

        if(!$type)
            return self::res_warning("Invalid application type received!");

        return FormApplicantResource::collect($this->model()->list($type, $status, $range));
    }

    public function not_started(string|FormType $form_type) : array
    {
        return $this->list($form_type, FormStatus::NOT_STARTED);
    }

    public function in_progress(string|FormType $form_type) : array
    {
        return $this->list($form_type, FormStatus::IN_PROGRESS);
    }

    public function completed(string|FormType $form_type) : array
    {
        return $this->list($form_type, FormStatus::COMPLETED);
    }

}