<?php

namespace Bricks\Business\Request;

use BrickLayer\Lay\Libs\Primitives\Abstracts\RequestHelper;
use Bricks\Business\Controller\Prospects;
use Bricks\Business\Model\FormApplication;
use Elevator\User\Model\ElevatorProfileModel;
use Utils\Traits\HasAppFoundation;

/**
 * @property FormApplication $form
 * @property ElevatorProfileModel $staff
 * @property string $notify_staff
 */
class AssignStaffRequest extends RequestHelper
{
    use HasAppFoundation;

    protected function rules(): void
    {
        $this->vcm([
            'field' => 'id', 'alias' => 'form', 'is_uuid' => true,
            'must_validate' => [
                "fun" => fn($v) => (new FormApplication($v))->exists(),
                "message" => "Applicant does not exist, please try again"
            ]
        ]);

        $this->vcm([
            'field' => 'staff', 'is_uuid' => true,
            'must_validate' => [
                "fun" => fn($v) => self::foundation()->user_profile()->model()->fill($v),
                "message" => "Invalid staff ID"
            ]
        ]);

        $this->vcm([ 'field' => 'notify_staff', 'is_bool' => true, 'required' => false ]);
    }

    protected function post_validate(array $data): array
    {
        $data['staff'] = self::foundation()->user_profile()->model()->fill($data['staff']);
        $data['form'] = new FormApplication($data['form']);

        return $data;
    }
}