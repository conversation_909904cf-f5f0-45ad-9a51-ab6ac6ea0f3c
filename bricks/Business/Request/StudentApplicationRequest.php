<?php

namespace Bricks\Business\Request;

use BrickLayer\Lay\Libs\FileUpload\Enums\FileUploadExtension;
use Bricks\Business\Enums\BusinessFileProvider;
use Bricks\Business\Enums\HigherSchoolQualification;
use Bricks\Business\Enums\MaritalStatus;
use Bricks\Business\Enums\PersonGender;
use Bricks\Business\Enums\PersonTitle;
use Bricks\Business\Enums\SchoolEntryPeriod;
use Bricks\Business\Enums\SchoolQualification;
use Bricks\Business\Enums\VisaSponsor;

/**
 * @property string last_name
 * @property string first_name
 * @property string middle_name
 * @property string gender
 * @property string dob
 * @property string marital_status
 * @property string email
 * @property string phone
 * @property string residential_address
 * @property string city
 * @property string state
 * @property string nationality
 * @property array other_nationalities
 * @property string program_interest
 * @property string preferred_entry_period
 * @property string preferred_destination
 * @property string secondary_school_name
 * @property string secondary_school_program
 * @property string secondary_school_admission_date
 * @property string secondary_school_graduation_date
 * @property string secondary_school_qualification
 * @property array higher_institution
 * @property array higher_institution_program
 * @property array higher_institution_admission_date
 * @property array higher_institution_graduation_date
 * @property array higher_institution_qualification
 * @property array additional_exams
 * @property array activities
 * @property array college_activities
 * @property array travel_history_country
 * @property array travel_history_date
 * @property array travel_history_duration
 * @property array travel_history_purpose
 * @property array travel_history_visa_type
 * @property array refusal_country
 * @property array refusal_visa_type
 * @property array refusal_date
 * @property array refusal_reason
 * @property string residence_in_uk_eu
 * @property string father_title
 * @property string father_first_name
 * @property string father_last_name
 * @property string father_dob
 * @property string father_citizenship
 * @property string father_employer
 * @property string father_email
 * @property string father_phone
 * @property string mother_title
 * @property string mother_first_name
 * @property string mother_last_name
 * @property string mother_dob
 * @property string mother_citizenship
 * @property string mother_employer
 * @property string mother_email
 * @property string mother_phone
 * @property string sponsor_type
 * @property string want_scholarship
 *
 * @property string secondary_school_transcript
 * @property string secondary_school_certificate
 * @property string higher_institution_transcript
 * @property string higher_institution_certificate
 * @property string passport_photo
 * @property string passport_biodata
 * @property string resume
 * @property string personal_statement
 * @property string references
 */
class StudentApplicationRequest extends NewFormApplicationRequest
{
    protected function pre_validate(): void
    {
        parent::pre_validate();
    }

    protected function rules(): void
    {
        parent::rules();

        // Files
        $this->vcm([
            'field' => 'secondary_school_transcript', 'is_file' => true,
            "allowed_types" => [FileUploadExtension::PICTURE, FileUploadExtension::PDF],
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('form_id') . " Secondary School Transcript",
                    "provider" => BusinessFileProvider::FORM_APPLICATION,
                    "is_public" => false
                ])
        ]);
        $this->vcm([
            'field' => 'secondary_school_certificate', 'is_file' => true, "required" => false,
            "allowed_types" => [FileUploadExtension::PICTURE, FileUploadExtension::PDF],
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('form_id') . " Secondary School Certificate",
                    "provider" => BusinessFileProvider::FORM_APPLICATION,
                    "is_public" => false
                ])
        ]);

        $this->vcm([
            'field' => 'higher_institution_transcript', 'is_file' => true, "required" => false,
            "allowed_types" => [FileUploadExtension::PICTURE, FileUploadExtension::PDF],
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('form_id') . " Higher Institution Transcript",
                    "provider" => BusinessFileProvider::FORM_APPLICATION,
                    "is_public" => false
                ])
        ]);
        $this->vcm([
            'field' => 'higher_institution_certificate', 'is_file' => true, "required" => false,
            "allowed_types" => [FileUploadExtension::PICTURE, FileUploadExtension::PDF],
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('form_id') . " Higher Institution Certificate",
                    "provider" => BusinessFileProvider::FORM_APPLICATION,
                    "is_public" => false
                ])
        ]);

        $this->vcm([
            'field' => 'passport_photo', 'is_file' => true,
            "allowed_types" => [FileUploadExtension::PICTURE],
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('form_id') . " Passport Photo",
                    "provider" => BusinessFileProvider::FORM_APPLICATION,
                    "is_public" => false
                ])
        ]);
        $this->vcm([
            'field' => 'passport_biodata', 'is_file' => true,
            "allowed_types" => [FileUploadExtension::PICTURE, FileUploadExtension::PDF],
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('form_id') . " Bio-Data Page",
                    "provider" => BusinessFileProvider::FORM_APPLICATION,
                    "is_public" => false
                ])
        ]);
        $this->vcm([
            'field' => 'resume', 'is_file' => true,
            "allowed_types" => [FileUploadExtension::MS_DOCX,FileUploadExtension::MS_DOC, FileUploadExtension::PDF],
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('form_id') . " Resume",
                    "provider" => BusinessFileProvider::FORM_APPLICATION,
                    "is_public" => false
                ])
        ]);
        $this->vcm([
            'field' => 'personal_statement', 'is_file' => true,
            "allowed_types" => [FileUploadExtension::MS_DOCX,FileUploadExtension::MS_DOC, FileUploadExtension::PDF],
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('form_id') . " Personal Statement",
                    "provider" => BusinessFileProvider::FORM_APPLICATION,
                    "is_public" => false
                ])
        ]);
        $this->vcm([
            'field' => 'references', 'is_file' => true,
            "allowed_types" => [FileUploadExtension::MS_DOCX,FileUploadExtension::MS_DOC, FileUploadExtension::PDF],
            "post_upload" => fn (array $response) => self::foundation()->file_store()
                ->new_file($response, [
                    "name" => $this->get('form_id') . " References",
                    "provider" => BusinessFileProvider::FORM_APPLICATION,
                    "is_public" => false
                ])
        ]);

        // Personal Information
        $this->vcm(['field' => 'last_name', 'is_name' => true]);
        $this->vcm(['field' => 'first_name', 'is_name' => true]);
        $this->vcm(['field' => 'middle_name', 'required' => false]);
        $this->vcm(['field' => 'gender', "must_validate" => fn($v) => PersonGender::is_enum($v)]);
        $this->vcm(['field' => 'dob', 'is_date' => true]);
        $this->vcm([
            'field' => 'marital_status',
            "must_validate" => fn($v) => MaritalStatus::is_enum($v),
        ]);
        $this->vcm(['field' => 'email', 'is_email' => true]);
        $this->vcm(['field' => 'phone', 'is_num' => true, 'min_length' => 7, 'max_length' => 15]);

        // Address
        $this->vcm(['field' => 'residential_address']);
        $this->vcm(['field' => 'city']);
        $this->vcm(['field' => 'state']);
        $this->vcm([ 'field' => 'nationality', "must_validate" => fn($v) => $this->validate_country($v),]);
        $this->vcm([ 'field' => 'other_nationalities[]', "required" => false, "must_validate" => fn($v) => $this->validate_country($v),]);

        // Program Interest
        $this->vcm(['field' => 'program_interest']);
        $this->vcm([
            'field' => 'preferred_entry_period',
            "must_validate" => fn($v) => SchoolEntryPeriod::is_enum($v, false),
        ]);
        $this->vcm(['field' => 'preferred_destination', 'required' => false ]);

        // Secondary School Education
        $this->vcm(['field' => 'secondary_school_name']);
        $this->vcm(['field' => 'secondary_school_program']);
        $this->vcm(['field' => 'secondary_school_admission_date', 'is_date' => true]);
        $this->vcm(['field' => 'secondary_school_graduation_date', 'is_date' => true]);
        $this->vcm(['field' => 'secondary_school_qualification', 'must_validate' => fn($v) => SchoolQualification::is_enum($v) ]);

        // Higher Institution Education (Arrays)
        $this->vcm(['field' => 'higher_institution[]', 'required' => false]);
        $this->vcm(['field' => 'higher_institution_program[]', 'required' => false]);
        $this->vcm(['field' => 'higher_institution_admission_date[]', 'is_date' => true, 'required' => false]);
        $this->vcm(['field' => 'higher_institution_graduation_date[]', 'is_date' => true, 'required' => false]);
        $this->vcm(['field' => 'higher_institution_qualification[]', 'required' => false, 'must_validate' => fn($v) => HigherSchoolQualification::is_enum($v) ]);

        // Exams and Activities (Arrays)
        $this->vcm(['field' => 'additional_exams[]', 'required' => false]);
        $this->vcm(['field' => 'activities[]', 'required' => false]);
        $this->vcm(['field' => 'college_activities[]', 'required' => false]);

        // Travel History (Arrays)
        $this->vcm([
            'field' => 'travel_history_country[]',
            'required' => false,
            "must_validate" => fn($v) => $this->validate_country($v),
        ]);
        $this->vcm(['field' => 'travel_history_date[]', 'is_date' => true, 'required' => false]);
        $this->vcm(['field' => 'travel_history_duration[]', 'required' => false]);
        $this->vcm(['field' => 'travel_history_purpose[]', 'required' => false]);
        $this->vcm(['field' => 'travel_history_visa_type[]', 'required' => false]);

        // Refusal History (Arrays)
        $this->vcm([
            'field' => 'refusal_country[]',
            "must_validate" => fn($v) => $this->validate_country($v),
            'required' => false,
        ]);
        $this->vcm(['field' => 'refusal_visa_type[]', 'required' => false]);
        $this->vcm(['field' => 'refusal_date[]', 'is_date' => true, 'required' => false]);
        $this->vcm(['field' => 'refusal_reason[]', 'required' => false]);

        $this->vcm(['field' => 'residence_in_uk_eu', 'is_bool' => true]);

        // Father's Information
        $this->vcm(['field' => 'father_title', "must_validate" => fn($v) => PersonTitle::is_enum($v), 'required' => false ]);
        $this->vcm(['field' => 'father_first_name', 'is_name' => true, 'required' => false ]);
        $this->vcm(['field' => 'father_last_name', 'is_name' => true, 'required' => false ]);
        $this->vcm(['field' => 'father_dob', 'is_date' => true, 'required' => false ]);
        $this->vcm(['field' => 'father_citizenship', "must_validate" => fn($v) => $this->validate_country($v), 'required' => false]);
        $this->vcm(['field' => 'father_employer', 'required' => false ]);
        $this->vcm(['field' => 'father_email', 'is_email' => true, 'required' => false]);
        $this->vcm(['field' => 'father_phone', 'is_num' => true, 'min_length' => 7, 'max_length' => 15, 'required' => false]);

        // Mother's Information
        $this->vcm(['field' => 'mother_title', "must_validate" => fn($v) => PersonTitle::is_enum($v), 'required' => false ]);
        $this->vcm(['field' => 'mother_first_name', 'is_name' => true, 'required' => false ]);
        $this->vcm(['field' => 'mother_last_name', 'is_name' => true, 'required' => false ]);
        $this->vcm(['field' => 'mother_dob', 'is_date' => true, 'required' => false ]);
        $this->vcm(['field' => 'mother_citizenship', "must_validate" => fn($v) => $this->validate_country($v), 'required' => false ]);
        $this->vcm(['field' => 'mother_employer', 'required' => false]);
        $this->vcm(['field' => 'mother_email', 'is_email' => true, 'required' => false]);
        $this->vcm(['field' => 'mother_phone', 'is_num' => true, 'min_length' => 7, 'max_length' => 15, 'required' => false]);

        // Financial & Declaration
        $this->vcm([
            'field' => 'sponsor_type',
            "must_validate" => fn($v) => VisaSponsor::is_enum($v),
        ]);
        $this->vcm(['field' => 'want_scholarship', 'is_bool' => true, 'required' =>  false, 'default_value' => false ]); // "yes" maps to true
    }
}