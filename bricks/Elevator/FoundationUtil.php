<?php
declare(strict_types=1);

namespace Bricks\Elevator;

use Bricks\Elevator\Blog\Controller\BlogController;
use Utils\Email\AppEmails;
use Utils\Email\Email;

class FoundationUtil extends \Elevator\Utils\ElevatorFoundation
{
    public function domain() : string
    {
        return "https://www.inside-edgeconsulting.com/";
    }

    public function mailer(): AppEmails
    {
        return new AppEmails(new Email());
    }

    public function post_ctrl() : BlogController
    {
        return BlogController::new();
    }
}
