<?php

namespace Bricks\Elevator\Blog\Controller;

use <PERSON><PERSON><PERSON>er\Lay\Core\View\DomainResource;
use Bricks\Elevator\Blog\Resource\BlogResource;
use Bricks\Elevator\Blog\Resource\RelatedPostResource;
use Elevator\Blog\Controller\ElevatorPostController;
use Elevator\Blog\Enums\PostStatus;

class BlogController extends ElevatorPostController
{
    public function list_by_status(PostStatus $status, int $page, int $limit, bool $as_array = true) : array
    {
        return BlogResource::collect(
            $this->model()->all_by_status($status, $page, $limit),
            as_array: $as_array
        );
    }

    public function by_category(string $id_or_slug, int $page = 1, int $limit = 4, bool $as_array = true) : array
    {
        return BlogResource::collect(
            $this->model()->all_by_category(self::clean($id_or_slug), $page, $limit),
            as_array: $as_array
        );
    }

    public function by_author(string $id_or_slug, int $page = 1, int $limit = 4, bool $as_array = true) : array
    {
        return BlogResource::collect(
            $this->model()->all_by_author(self::clean($id_or_slug), $page, $limit),
            as_array: $as_array
        );
    }

    public function by_slug(string $slug) : ?BlogResource
    {
        $post = $this->model()->by_slug($slug);

        if($post->is_empty())
            return null;

        return new BlogResource($post);
    }

    public function related_articles(string $id) : array
    {
        return RelatedPostResource::collect($this->model()->related_articles(self::clean($id), 3));
    }

    public function search(string $query): array
    {
        self::cleanse($query);

        return BlogResource::collect(
            $this->model()->all_by_search_query($query, true),
            as_array: false
        );
    }


    public function more_posts(int $page, string $type = "blog", ?string $arg = null) : string
    {
        $out = "";

        $data = [];

        if($type == "blog")
            $data = $this->published($page, 10, false);

        if($type == "category")
            $data = $this->by_category(self::clean($arg), $page, 10, false);

        if($type == "author")
            $data = $this->by_author(self::clean($arg), $page, 10, false);

        foreach ($data as $i => $post) {
            $out .= DomainResource::include_file("__partials/blog-page-list.inc", option: [
                'as_string' => true,
                'once' => false,
                "local" => [
                    "post" => $post,
                    "post_index" => $i,
                ]
            ]);
        }

        return $out;
    }

}