<?php

namespace Bricks\Elevator\Blog\Controller;

use Elevator\Blog\Utils\MarkdownAnalyzer;
use League\CommonMark\Environment\Environment;
use League\CommonMark\Extension\CommonMark\CommonMarkCoreExtension;
use League\CommonMark\MarkdownConverter;
use League\CommonMark\Node\Node;
use League\CommonMark\Renderer\ChildNodeRendererInterface;
use League\CommonMark\Renderer\NodeRendererInterface;
use League\CommonMark\Util\HtmlElement;
use League\CommonMark\Extension\CommonMark\Node\Block\Heading;
use League\CommonMark\Extension\CommonMark\Node\Block\ListBlock;
use League\CommonMark\Extension\CommonMark\Node\Block\ListItem;

class CustomMarkdownMap
{
    protected MarkdownConverter $converter;

    public function __construct()
    {
        $environment = new Environment([
            'html_input' => 'strip',
            'allow_unsafe_links' => false,
            'max_nesting_level' => 5
        ]);
        $environment->addExtension(new CommonMarkCoreExtension());

        // Register custom renderers
        $environment->addRenderer(Heading::class, new class implements NodeRendererInterface {
            public function render(Node $node, ChildNodeRendererInterface $childRenderer): HtmlElement|string|null
            {
                if (!($node instanceof Heading)) {
                    throw new \InvalidArgumentException('Expected Heading node');
                }

                $content = $childRenderer->renderNodes($node->children());
                $tag = 'h' . $node->getLevel();

//                if ($node->getLevel() === 2) {
//                    return new HtmlElement($tag, ['class' => 'title-anim'], $content);
//                }
                return new HtmlElement($tag, ['class' => 'title-anim', 'id' => MarkdownAnalyzer::slugify($content)], $content);

//                return new HtmlElement($tag, [], $content);
            }
        });

        $environment->addRenderer(ListBlock::class, new class implements NodeRendererInterface {
            public function render(Node $node, ChildNodeRendererInterface $childRenderer): HtmlElement|string|null
            {
                if (!($node instanceof ListBlock)) {
                    throw new \InvalidArgumentException('Expected ListBlock node');
                }

                // You can add class here if needed
                return new HtmlElement('ul', [], $childRenderer->renderNodes($node->children()));
            }
        });

        $environment->addRenderer(ListItem::class, new class implements NodeRendererInterface {
            public function render(Node $node, ChildNodeRendererInterface $childRenderer): HtmlElement|string|null
            {
                if (!($node instanceof ListItem)) {
                    throw new \InvalidArgumentException('Expected ListItem node');
                }

                $content = $childRenderer->renderNodes($node->children());
                return new HtmlElement('li', ['class' => 'blog-list'], '<span class="icon"><i class="tji-check"></i></span> <div class="content">' . $content . '</div>');
            }
        });

        $this->converter = new MarkdownConverter($environment);
    }

    public function render(string $markdown): string
    {
        return $this->converter->convert($markdown);
    }
}