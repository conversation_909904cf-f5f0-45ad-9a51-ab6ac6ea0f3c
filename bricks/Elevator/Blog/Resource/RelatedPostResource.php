<?php

namespace Bricks\Elevator\Blog\Resource;

use BrickLayer\Lay\Core\View\ViewSrc;
use Elevator\Blog\Resource\PostResource;

/**
 * @property string thumbnail
 * @property string date
 */
class RelatedPostResource extends PostResource
{
    protected function schema(object $data): array
    {
        $data = parent::schema($data);

        return [
            "slug" => $data['slug'],
            "thumbnail" => ViewSrc::gen($data['photo']['thumbnail'] ?? "@img/placeholder.svg"),
            "title" => $data['title'],
            "date" => $data['dateUpdated'] ?? $data['dateCreated']
        ];

    }
}