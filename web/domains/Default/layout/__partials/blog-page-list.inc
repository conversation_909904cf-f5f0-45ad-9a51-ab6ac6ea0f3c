<?php

use <PERSON><PERSON><PERSON>er\Lay\Core\View\DomainResource;
use BrickLayer\Lay\Core\View\Tags\Img;
use BrickLayer\Lay\Libs\LayDate;

/**
 * @var \Bricks\Elevator\Blog\Resource\BlogResource $post
 */
$post = DomainResource::plaster()->local->post;
$i = DomainResource::plaster()->local->post_index;

$url = "blog/$post->slug";
?>
<article class="blog-item wow fadeInUp" data-wow-delay=".<?= $i + 1 ?>s">
    <div class="blog-thumb">
        <a href="<?= $url ?>">
            <?= Img::new()->alt($post->title)->src($post->photo['thumbnail']) ?>
        </a>
        <div class="blog-date">
            <span class="date"><?= LayDate::date($post->dateUpdatedUnix ?? $post->dateCreated, "d") ?></span>
            <span class="month"><?= LayDate::date($post->dateUpdatedUnix ?? $post->dateCreated, "M") ?></span>
        </div>
    </div>
    <div class="blog-content">
        <div class="blog-meta">
            <span class="categories"><a href="blog/category/<?= $post->category['slug'] ?>"><?= $post->category['name'] ?></a></span>
            <span>By <a href="blog/author/<?= $post->author['slug'] ?>"><?= $post->author['name'] ?></a></span>
        </div>
        <h3 class="title">
            <a href="<?= $url ?>"><?= $post->title ?></a>
        </h3>
        <p class="desc"><?= $post->metaContent ?></p>
        <a class="text-btn" href="<?= $url ?>">
            <span class="btn-text"><span>Read More</span></span>
            <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
        </a>
    </div>
</article>