<?php

use <PERSON>Layer\Lay\Core\View\DomainResource;
use BrickLayer\Lay\Core\View\SrcFilter;
use BrickLayer\Lay\Core\View\Tags\Link;
use BrickLayer\Lay\Libs\LayArray;
use Web\Default\Plaster;

if(DomainResource::plaster()->local->section == "form") {
    echo DomainResource::plaster()->head;
    return;
}

$site_data = \BrickLayer\Lay\Core\LayConfig::site_data();
$link = Link::new()->rel("stylesheet");

$link->href("@ui/assets/css/bootstrap.min.css");
$link->href("@ui/assets/css/font-awesome-pro.min.css");
$link->href("@ui/assets/css/animate.min.css");
$link->href("@ui/assets/css/bexon-icons.css");
$link->href("@ui/assets/css/nice-select.css");
$link->href("@ui/assets/css/swiper.min.css");
$link->href("@ui/assets/css/venobox.min.css");
$link->href("@ui/assets/css/odometer-theme-default.css");
$link->href("@ui/assets/css/meanmenu.css");
$link->href("@ui/assets/css/main.css");

$services = (new Plaster)->services_menu()['json_ld'];

echo DomainResource::plaster()->head; ?>

<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "EducationalOrganization",
        "name": "Inside Edge Consulting Limited",
        "alternateName": "Inside Edge",
        "url": "https://www.inside-edgeconsulting.com",
        "logo" : "<?= SrcFilter::go('@shared_img/logo.png') ?>",
        "founder" : "<?= $site_data->others->founder ?>",
        "foundingDate": "2005",
        "description": "<?= $site_data->others->desc ?>",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "Suite D26, Dolphin Plaza, Dolphin Estate",
            "addressLocality": "Ikoyi",
            "addressRegion": "Lagos",
            "addressCountry": "NG"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "<?= $site_data->tel->{0} ?>",
            "contactType": "Customer Support",
            "email": "<?= $site_data->mail->{0} ?>",
            "areaServed": "Worldwide"
        },
        "sameAs": [<?= implode(",", LayArray::map($site_data->others->social, fn($v) => '"' . $v['url'] . '"')) ?>],
        "hasOfferCatalog": {
          "@type": "OfferCatalog",
          "name": "Educational Services",
          "itemListElement": [ <?= $services ?> ]
        }
    }
</script>
