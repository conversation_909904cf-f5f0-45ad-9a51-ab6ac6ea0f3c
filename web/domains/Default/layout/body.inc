<?php

use BrickLayer\Lay\Core\View\DomainResource;
use BrickLayer\Lay\Core\View\Tags\Img;
use Web\Default\Plaster;

$site_data = \BrickLayer\Lay\Core\LayConfig::site_data();

if(DomainResource::plaster()->local->section == "form") {
    echo DomainResource::plaster()->body;
    return;
}

$social_str = "";

foreach ($site_data->others->social as $soc) {
    $social_str .= '<li><a href="' . $soc['url'] . '" target="_blank"><i class="fa-brands ' . $soc['img'] . '"></i></a>';
}

$social_str = "<ul>$social_str</ul>";
$show_cta = DomainResource::plaster()->local->show_cta ?? true;
$is_active = function(string $url) {
    $url = str_replace("./", "", $url);

    return DomainResource::plaster()->page->route == ($url == '' ? 'index' : $url);
};

$x = (new Plaster)->services_menu();

$services_footer = $x['services_footer'];
$student_services = $x['student_services'];
$nigeria_services = $x['nigeria_services'];
$international_services = $x['international_services'];

$menu = function (string $menu_id, array ...$items) use($is_active) {
    $link = function($url, $text, &$found = null) use ($is_active) {
        $active = $is_active($url);

        if($active) $found = true;

        return '<li class="' . ($active ? "current-menu-item" : "") . '"><a href="' . $url . '">' . $text . '</a></li>';
    };

    $all = "";
    
    foreach ($items as $item) {
        if(isset($item['sub'])) {
            $sub = "";
            $found = false;

            foreach ($item['sub'] as $it) {
                if(isset($it['sub'])) {
                    $sub_sub = "";
                    $sub_found = false;

                    foreach ($it['sub'] as $t) {
                        $sub_sub .= $link($t['url'], $t['name'], $sub_found);

                        if($sub_found)
                            $found = true;
                    }

                    $sub .= "<li class='has-dropdown " . ($sub_found ? 'current-menu-ancestor' : '') . "'>
                        <a href='javascript:void(0)'>{$it['name']}</a>
                        <ul class='sub-menu'>
                            $sub_sub
                        </ul>
                    </li>";
                }
                else
                    $sub .= $link($it['url'], $it['name'], $found);
            }

            $all .= "<li class='has-dropdown " . ($found ? 'current-menu-ancestor' : '') . "'>
                <a href='javascript:void(0)'>{$item['name']}</a>
                <ul class='sub-menu'>
                    $sub
                </ul>
            </li>";

            continue;
        }

        $all .= $link($item['url'], $item['name']);
    }
    
    return <<<ALL
    <nav id="mobile-menu$menu_id" class="mainmenu">
        <ul>$all</ul>
    </nav>
    ALL;

};
?>

<div class="body-overlay"></div>

<!-- Preloader Start -->
<div class="preloader">
    <div class="loading-container">
        <div class="loading"></div>
        <div id="loading-icon">
            <?= Img::new()->src("@shared_img/logo.svg") ?>
        </div>
    </div>
</div>
<!-- Preloader end -->

<!-- back to top start -->
<div class="back-to-top-wrapper">
    <button id="back_to_top" type="button" class="back-to-top-btn">
        <span><i class="tji-arrow-up-long"></i></span>
    </button>
</div>
<!-- back to top end -->

<!-- start: Search Popup -->
<div class="search-popup-overlay"></div>
<!-- end: Search Popup -->

<!-- start: Offcanvas Menu -->
<div class="tj-offcanvas-area d-none d-lg-block">
    <div class="hamburger_bg"></div>
    <div class="hamburger_wrapper">
        <div class="hamburger_inner">
            <div class="hamburger_top d-flex align-items-center justify-content-between">
                <div class="hamburger_logo">
                    <a href="./" class="mobile_logo">
                        <?= Img::new()->height("75px")->alt("logo")->src("@shared_img/logo.svg") ?>
                    </a>
                </div>
                <div class="hamburger_close">
                    <button class="hamburger_close_btn"><i class="fa-thin fa-times"></i></button>
                </div>
            </div>
            <div class="offcanvas-text">
                <p>
                    <?= $site_data->others->desc ?>
                </p>
            </div>

            <div class="hamburger-infos">
                <h5 class="hamburger-title">Contact Info</h5>
                <div class="contact-info">
                    <div class="contact-item">
                        <span class="subtitle">Phone</span>
                        <a class="contact-link" href="tel:<?= $site_data->tel->{0} ?>"><?= $site_data->tel->{1} ?></a>
                    </div>
                    <div class="contact-item">
                        <span class="subtitle">Email</span>
                        <a class="contact-link" href="mailto:<?= $site_data->mail->{0} ?>"><?= $site_data->mail->{0} ?></a>
                    </div>
                    <div class="contact-item">
                        <span class="subtitle">Location</span>
                        <a href="<?= $site_data->others->map_link ?>" target="_blank" class="contact-link"><?= $site_data->others->addr ?></a>
                    </div>
                </div>
            </div>
        </div>
        <div class="hamburger-socials">
            <h5 class="hamburger-title">Follow Us</h5>
            <div class="social-links style-3">
                <?= $social_str ?>
            </div>
        </div>
    </div>
</div>
<!-- end: Offcanvas Menu -->

<!-- start: Hamburger Menu -->
<div class="hamburger-area d-lg-none">
    <div class="hamburger_bg"></div>
    <div class="hamburger_wrapper">
        <div class="hamburger_inner">
            <div class="hamburger_top d-flex align-items-center justify-content-between">
                <div class="hamburger_logo">
                    <a href="./" class="mobile_logo">
                        <?= Img::new()->height("75px")->alt("logo")->src("@shared_img/logo.svg") ?>
                    </a>
                </div>
                <div class="hamburger_close">
                    <button class="hamburger_close_btn"><i class="fa-thin fa-times"></i></button>
                </div>
            </div>
            <div class="hamburger_menu">
                <div class="mobile_menu"></div>
            </div>
            <div class="hamburger-infos">
                <h5 class="hamburger-title">Contact Info</h5>
                <div class="contact-info">
                    <div class="contact-item">
                        <span class="subtitle">Phone</span>
                        <a class="contact-link" href="tel:<?= $site_data->tel->{0} ?>"><?= $site_data->tel->{1} ?></a>
                    </div>
                    <div class="contact-item">
                        <span class="subtitle">Email</span>
                        <a class="contact-link" href="mailto:<?= $site_data->mail->{0} ?>"><?= $site_data->mail->{0} ?></a>
                    </div>
                    <div class="contact-item">
                        <span class="subtitle">Location</span>
                        <a href="<?= $site_data->others->map_link ?>" target="_blank" class="contact-link"><?= $site_data->others->addr ?></a>
                    </div>
                </div>
            </div>
        </div>
        <div class="hamburger-socials">
            <h5 class="hamburger-title">Follow Us</h5>
            <div class="social-links style-3">
                <?= $social_str ?>
            </div>
        </div>
    </div>
</div>
<!-- end: Hamburger Menu -->

<?php for ($i = 0; $i < 2; $i++) : ?>
<header class="header-area header-1 section-gap-x <?= $i > 0 ? 'header-duplicate header-sticky' : '' ?>">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="header-wrapper">
                    <!-- site logo -->
                    <div class="site_logo">
                        <a class="logo" href="./">
                            <?= Img::new()->alt($site_data->name->short)->src("@shared_img/logo.png") ?>
                        </a>
                    </div>

                    <!-- navigation -->
                    <div class="menu-area d-none d-lg-inline-flex align-items-center">
                        <?= $menu(
                            $i == 0 ? '' : "$i",
                            [
                                "name" => "Company",
                                "sub" => [
                                    [
                                        "name" => "About Us",
                                        "url" => "company",
                                    ],
                                    [
                                        "name" => "Our Team",
                                        "url" => "company/team",
                                    ],
//                                    [
//                                        "name" => "Recognitions",
//                                        "url" => "company/recognitions",
//                                    ],
                                    [
                                        "name" => "Contact Us",
                                        "url" => "company/contact-us",
                                    ],
                                ]
                            ],
                            [
                                "name" => "Students & Parents",
                                "sub" => $student_services
                            ],
                            [
                                "name" => "Institution",
                                "sub" => [
                                    [
                                        "name" => "Nigerian",
                                        "sub" => $nigeria_services,
                                    ],
                                    [
                                        "name" => "International",
                                        "sub" => $international_services,
                                    ],

                                ]
                            ],

                            [
                                "name" => "Coaching",
                                "url" => "coaching",
                            ],

                        ) ?>
                    </div>

                    <!-- header right info -->
                    <div class="header-right-item d-none d-lg-inline-flex">
                        <div class="header-search">
                            <button class="search">
                                <i class="tji-search"></i>
                            </button>
                            <button type="button" class="search_close_btn">
                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M17 1L1 17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                          stroke-linejoin="round" />
                                    <path d="M1 1L17 17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"
                                          stroke-linejoin="round" />
                                </svg>
                            </button>
                        </div>
                        <div class="header-button">
                            <a class="tj-primary-btn" href="company/enquiry">
                                <span class="btn-text"><span>Make Enquiry</span></span>
                                <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
                            </a>
                        </div>
                        <div class="menu_bar menu_offcanvas d-none d-lg-inline-flex">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>

                    <!-- menu bar -->
                    <div class="menu_bar mobile_menu_bar d-lg-none">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Popup -->
    <div class="search_popup">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-8">
                    <div class="tj_search_wrapper">
                        <div class="search_form">
                            <form action="blog/search">
                                <div class="search_input">
                                    <div class="search-box">
                                        <input name="query" class="search-form-input" type="text" placeholder="Type Words and Hit Enter" required>
                                        <button type="submit">
                                            <i class="tji-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
<?php endfor; ?>


<main id="primary" class="site-main">
    <?= DomainResource::plaster()->body ?>

    <?php if ($show_cta) : ?>
        <section class="tj-cta-section pb-5">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="cta-area">
                            <div class="cta-content">
                                <h2 class="title title-anim">Let’s Build The Future Together.</h2>
                                <div class="cta-btn wow fadeInUp" data-wow-delay=".6s">
                                    <a class="tj-primary-btn btn-dark" href="company/enquiry">
                                        <span class="btn-text"><span>Make Enquiry Now</span></span>
                                        <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
                                    </a>
                                </div>
                            </div>
                            <div class="cta-img">
                                <?= Img::new()->src("@img/cta.webp") ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>

    <!-- start: Footer Section -->
    <footer class="tj-footer-section footer-2 <?= $show_cta ? 'section-gap-x' : 'pt-2' ?>">
        <div class="footer-main-area">
            <div class="container">
                <div class="row justify-content-between">
                    <div class="col-xl-4 col-md-6">
                        <div class="footer-widget">
                            <h3 class="title">Get Insights Directly To Your Inbox.</h3>
                            <div class="subscribe-form">
                                <form>
                                    <input tabindex="1" type="email" name="email" placeholder="Your Email" required autocomplete="on">
                                    <button class="submit-newsletter" tabindex="3" type="submit"><i class="tji-plane"></i></button>
                                    <div class="d-flex mt-1">
                                        <?= DomainResource::include_file("__partials/captcha.inc", option: [
                                            'as_string' => true,
                                            'once' => false
                                        ]) ?>
                                        <input type="text" tabindex="2" class="rounded-0" name="captcha" placeholder="Verify Code">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <div class="footer-widget widget-nav-menu footer-col-2">
                            <h5 class="title">Core Services</h5>
                            <ul><?= $services_footer ?></ul>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-6">
                        <div class="footer-widget widget-nav-menu footer-col-3">
                            <h5 class="title">Resource</h5>
                            <ul>
                                <li><a href="services">All Services</a></li>
                                <li><a href="company/contact-us">Contact us</a></li>
                                <li><a href="blog">Blog</a></li>
                                <li><a href="faqs">FAQs</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6">
                        <div class="footer-widget widget-contact">
                            <h5 class="title">Our Office</h5>
                            <div class="footer-contact-info">
                                <div class="contact-item">
                                    <a href="<?= $site_data->others->map_link ?>" target="_blank">
                                        <span><?= $site_data->others->addr ?></span>
                                    </a>
                                </div>
                                <div class="contact-item">
                                    P <a href="tel:<?= $site_data->tel->{0} ?>">
                                        <span><?= $site_data->tel->{1} ?></span>
                                    </a>
                                </div>
                                <div class="contact-item">
                                    E <a href="mailto:<?= $site_data->mail->{0} ?>">
                                        <span><?= $site_data->mail->{0} ?></span>
                                    </a>
                                </div>
                                <div class="contact-item">
                                    <span>
                                        <i class="tji-clock"></i>
                                        Mon-Fri 9am-5pm
                                    </span>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="h6-footer-logo-area" style="padding-bottom: 50px">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center">
                        <div class="h6-footer-logo">
                            <a href="./" class="wow bounceInLeft" data-wow-delay=".3s">
                                <?= Img::new()->alt("logo")->src("@shared_img/logo-light.png") ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tj-copyright-area-2 h5-footer-copyright h6-footer-copyright">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <div class="copyright-content-area">
                            <div class="copyright-text">
                                <p>
                                    <?= DomainResource::get()->copyright ?>
                                </p>
                            </div>

                            <div class="social-links">
                                <?= $social_str ?>
                            </div>
                            <div class="copyright-menu">
                                <ul>
                                    <li><a href="company/policy">Privacy Policy</a></li>
                                    <li><a href="sitemap.xml">Sitemap</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-shape-1">
            <?= Img::new()->src("@ui/assets/images/shape/pattern-2.svg") ?>
        </div>
        <div class="bg-shape-2">
            <?= Img::new()->src("@ui/assets/images/shape/pattern-3.svg") ?>
        </div>
        <div class="bg-shape-3" style="right: 0; margin: auto">
            <?= Img::new()->src("@img/shape-blur.svg") ?>
        </div>
    </footer>
    <!-- end: Footer Section -->
</main>
