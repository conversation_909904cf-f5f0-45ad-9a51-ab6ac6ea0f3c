@use "../utilities" as *;

/**----------------------------------------
START: Theme Search CSS
----------------------------------------*/
.search {
	&_popup {
		position: absolute;
		top: 101%;
		left: 0;
		width: 100%;
		height: auto;
		background-color: var(--tj-color-common-white);
		padding: 80px 0;
		@include transform(translateY(calc(-100% - 200px)));
		transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
		transition-delay: 0.2s;
		z-index: 10;
		border-radius: 12px;
		.search_close {
			position: absolute;
			top: 36px;
			right: 36px;
			.search_close_btn {
				font-size: 24px;
				color: var(--tj-color-theme-dark);
				svg {
					width: 24px;
					height: 24px;
					@include transition(all 0.4s ease-in-out 0s);
					path {
						fill: var(--tj-color-theme-dark);
					}
					&:hover {
						transform: rotate(90deg);
						path {
							fill: var(--tj-color-theme-primary);
						}
					}
				}
			}
		}
		&.search-opened {
			@include transform(translateY(0%));
			transition-delay: 0s;
			.search_form {
				.search_input {
					@include transform(translateY(0px));
					opacity: 1;
					transition-delay: 0.3s;
					&::after {
						width: 100%;
						transition-delay: 0.5s;
					}
				}
			}
		}
		.logo {
			max-width: 150px;
			width: 100%;
			position: absolute;
			left: 36px;
			top: 36px;
		}

		@media #{$md, $sm, $xs} {
			display: none;
		}
	}
}

.tj_search_wrapper {
	min-height: 100px;
	display: flex;
	align-items: center;
	.search_form {
		width: 100%;
		form {
			.search_input {
				position: relative;
				z-index: 1;
				.search-box {
					position: relative;
					z-index: 1;
					input:not([type="submit"]):not([type="radio"]):not(
							[type="checkbox"]
						):not([type="search"]),
					input[type="text"] {
						width: 100%;
						height: 65px;
						font-size: 20px;
						font-family: var(--tj-ff-body);
						color: var(--tj-color-text-body);
						border-color: var(--tj-color-border-1);
						background: transparent;
						padding: 16px 90px 16px 24px;
						border-radius: 10px;
						&::placeholder {
							font-size: 20px;
							color: var(--tj-color-text-body);
							@media #{$lg} {
								font-size: 20px;
							}
						}
						&:focus {
							border-color: var(--tj-color-theme-primary);
							+ button {
								border-color: var(--tj-color-theme-primary);
							}
						}
						@media #{$lg} {
							font-size: 20px;
						}
					}
					button {
						display: inline-flex;
						align-items: center;
						justify-content: center;
						position: absolute;
						right: 0;
						top: 50%;
						transform: translateY(-50%);
						max-width: 60px;
						font-size: 25px;
						color: var(--tj-color-theme-primary);
						width: 100%;
						height: 100%;
						&::before {
							content: "";
							position: absolute;
							left: 0;
							top: 18px;
							height: 30px;
							border-left: 1px solid var(--tj-color-border-1);
						}
					}
				}
			}
		}
	}
}

.search-popup-overlay {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(12, 30, 33, 0.01);
	z-index: 99;
	@include transform(translateY(calc(-100% - 80px)));
	transition: all 0.3s ease-in-out 0s;
	transition-delay: 0.1s;
	&.search-popup-overlay-open {
		@include transform(translateY(0));
	}
}
/* !END: Theme Search CSS */
