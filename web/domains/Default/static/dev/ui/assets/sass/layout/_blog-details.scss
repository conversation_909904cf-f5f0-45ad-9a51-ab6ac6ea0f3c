@use "../utilities" as *;

/**----------------------------------------
START: Blog Details CSS
----------------------------------------*/
.post-details-wrapper {
	.blog-images {
		margin-bottom: 30px;
		border-radius: 12px;
		overflow: hidden;
	}
	.title {
		line-height: 1.1;
		margin-bottom: 22px;
	}
	p {
		margin-bottom: 30px;
		&:last-child {
			margin-bottom: 0;
		}
	}
	.images-wrap {
		margin-bottom: 10px;
	}
	.image-box {
		margin-bottom: 25px;
		border-radius: 12px;
		overflow: hidden;
		img {
			width: 100%;
		}
	}
	.blog-text {
		h2 {
			font-size: var(--tj-fs-h3);
			line-height: 1.25;

			@media #{$md} {
				font-size: 28px;
			}
			@media #{$sm, $xs} {
				font-size: 25px;
			}
		}

		h3 {
			font-size: var(--tj-fs-h4);
			line-height: 1.333;
			margin-bottom: 25px;

			@media #{$md} {
				font-size: 22px;
			}
			@media #{$sm, $xs} {
				font-size: 20px;
			}
		}

		ul {
			margin-bottom: 50px;
			list-style: none;
			//columns: 2;
			li {
				//display: flex;
				//align-items: start;
				column-gap: 10px;
				color: var(--tj-color-heading-primary);
				font-weight: var(--tj-fw-medium);
				margin-bottom: 16px;
				&:last-child {
					margin-bottom: 0;
				}
				span {
					display: inline-flex;
					align-items: center;
					justify-content: center;
					width: 20px;
					height: 20px;
					flex: 0 0 auto;
					font-size: 8px;
					line-height: 1;
					margin-right: 3px;
					color: var(--tj-color-common-white);
					background: var(--tj-color-theme-primary);
					border-radius: 50%;
				}
			}
			@media #{$xs} {
				margin-bottom: 30px;
				columns: 1;
			}
		}

		.blog-list {
			display: flex;
			align-items: center;

			.icon{
				margin-left: 5px;
				margin-right: 10px;
			}
		}

		.blog-video {
			position: relative;
			z-index: 1;
			border-radius: 12px;
			overflow: hidden;
			margin-top: 60px;
			margin-bottom: 50px;
			.video-btn {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				width: 90px;
				height: 90px;
				line-height: 1;
				color: var(--tj-color-heading-primary);
				background: var(--tj-color-common-white);
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				border-radius: 50%;
				span {
					font-size: 20px;
				}

				&::before {
					position: absolute;
					content: "";
					left: 50%;
					top: 50%;
					width: 90px;
					height: 90px;
					transform: translate(-50%, -50%);
					background-color: rgba(255, 255, 255, 0.2);
					animation: pulse2 2s linear infinite;
					z-index: -1;
					border-radius: 50%;
					@media #{$xs} {
						width: 70px;
						height: 70px;
					}
				}
				@media #{$xs} {
					width: 70px;
					height: 70px;
				}
			}
			@media #{$md, $sm, $xs} {
				margin-top: 40px;
				margin-bottom: 40px;
			}
		}
	}
}

.blog-category-two {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	border: 1px dashed var(--tj-color-border-1);
	border-radius: 10px;
	margin-bottom: 25px;
	.category-item {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		border-right: 1px dashed var(--tj-color-border-1);
		padding: 25px 30px;
		gap: 10px;
		width: 33.33%;
		&:last-child {
			border-right: none;
		}
		.cate-images {
			width: 52px;
			height: 52px;
			img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				border-radius: 50%;
			}
			@media #{$xl, $md, $sm, $xs} {
				width: 45px;
				height: 45px;
			}
			@media #{$lg} {
				width: 35px;
				height: 35px;
			}
		}
		.cate-text {
			.degination {
				display: block;
				color: var(--tj-color-text-body-3);
				font-size: 14px;
				line-height: 1;
				margin-bottom: 6px;
			}
			.title {
				font-weight: var(--tj-fw-sbold);
				margin-bottom: 0;
				@media #{$xl, $md, $sm, $xs} {
					font-size: 16px;
				}
				@media #{$lg} {
					font-size: 14px;
				}
			}
		}
		.cate-icons {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			font-size: 24px;
			width: 52px;
			height: 52px;
			line-height: 1;
			color: var(--tj-color-common-white);
			background-color: var(--tj-color-theme-primary);
			border-radius: 50%;
			@media #{$xl, $md, $sm, $xs} {
				width: 45px;
				height: 45px;
				font-size: 20px;
			}
			@media #{$lg} {
				width: 35px;
				height: 35px;
				font-size: 18px;
			}
		}
		.text {
			font-weight: var(--tj-fw-sbold);
			line-height: 1;
			margin-bottom: 0;
			@media #{$xl, $md, $sm, $xs} {
				font-size: 16px;
			}
			@media #{$lg} {
				font-size: 14px;
			}
		}
		@media #{$xl} {
			padding: 25px 20px;
		}
		@media #{$lg, $md} {
			padding: 20px 15px;
		}
		@media #{$sm, $xs} {
			width: 100%;
			padding: 0;
			border: 0;
		}
	}
	@media #{$sm, $xs} {
		padding: 20px;
		gap: 20px;
	}
}

blockquote {
	position: relative;
	padding: 40px 30px 30px;
	background: var(--tj-color-theme-bg);
	border: 2px solid var(--tj-color-theme-primary);
	border-radius: 12px;
	margin-top: 55px;
	margin-bottom: 50px;
	z-index: 2;
	&::before {
		content: "\e92e";
		font-size: 40px;
		color: var(--tj-color-theme-primary);
		font-family: "bexon-icons";
		display: inline-block;
		margin-bottom: 20px;
		line-height: 1;
	}
	p {
		color: var(--tj-color-heading-primary);
		font-family: var(--tj-ff-heading);
		font-weight: var(--tj-fw-sbold);
		font-size: 24px;
		margin-bottom: 20px !important;
		letter-spacing: -0.6px;
		line-height: 1.417;
		border-radius: 12px;
		@media #{$sm, $xs} {
			font-size: 20px;
			margin-bottom: 15px !important;
		}
	}
	cite {
		color: var(--tj-color-heading-primary);
		display: block;
		text-align: end;
		font-style: normal;
		position: relative;
		z-index: 1;
		&::before {
			content: "";
			display: inline-block;
			background: var(--tj-color-theme-primary);
			height: 1px;
			width: 50px;
			top: -4px;
			margin-right: 10px;
			position: relative;
		}
	}
	@media #{$sm, $xs} {
		padding: 25px 15px;
		margin-bottom: 40px;
	}
}

.tj-tags-post {
	display: flex;
	flex-wrap: wrap;
	row-gap: 20px;
	column-gap: 30px;
	align-items: center;
	justify-content: space-between;
	border-top: 1px dashed var(--tj-color-border-1);
	border-bottom: 1px dashed var(--tj-color-border-1);
	padding: 22px 0;
	margin-top: 30px;
	margin-bottom: 30px;
	.tagcloud {
		width: calc(100% - 200px);
		@media #{$sm, $xs} {
			width: 100%;
		}
	}
	.post-share {
		ul {
			margin: 0;
			padding: 0;
			list-style: none;
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			column-gap: 15px;
			row-gap: 10px;
			li {
				font-size: 16px;
				color: var(--tj-color-heading-primary);
				margin-bottom: 0;
				a {
					display: flex;
					font-size: 17px;
					line-height: 1;
					color: var(--tj-color-heading-primary);

					i {
						display: inline-flex;
						line-height: 1;
					}
					&:hover {
						i {
							color: var(--tj-color-theme-primary);
						}
					}
				}
			}
		}
	}
	&.no_socials {
		width: 100%;
	}
}

// post navigation css
.tj-post__navigation {
	background-color: var(--tj-color-common-white);
	padding: 20px 25px;
	border-radius: 10px;
	display: flex;
	gap: 25px;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 50px;
	.tj-nav-post__nav {
		a {
			display: inline-flex;
			align-items: center;
			justify-content: center;
			gap: 8px;
			color: var(--tj-color-heading-primary);
			font-weight: var(--tj-fw-sbold);
			span {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				width: 52px;
				height: 52px;
				background-color: var(--tj-color-common-white);
				border-radius: 50%;
				border: 1px solid var(--tj-color-border-1);
				font-size: 24px;
				line-height: 1;
				@include transition(all 0.3s ease-in-out 0s);
				i {
					color: var(--tj-color-theme-dark);
				}
				@media #{$xs} {
					width: 48px;
					height: 48px;
				}
			}
			&:hover {
				span {
					background-color: var(--tj-color-theme-primary);
					border-color: var(--tj-color-theme-primary);
					i {
						color: var(--tj-color-common-white);
					}
				}
			}
		}
	}
	.tj-nav-post__grid {
		font-size: 30px;
		line-height: 1;
		color: var(--tj-color-common-black);
		a {
			color: currentColor;
			&:hover {
				color: var(--tj-color-theme-primary);
			}
		}
	}
	@media #{$xs} {
		padding: 20px;
		gap: 20px;
	}
}

// post comments css
.tj-comments-wrap {
	.comments-title {
		.title {
			margin-bottom: 22px;
		}
	}
	@media #{$xs} {
		.comments-title {
			.title {
				margin-bottom: 15px;
			}
		}
	}
}

.tj-latest-comments {
	ul {
		margin: 0;
		padding: 0;
		list-style: none;
		.tj-comment {
			margin-bottom: 20px;
			&:last-child {
				margin-bottom: 0;
			}
			.comment-content {
				display: flex;
				gap: 12px;
				padding: 30px;
				background-color: var(--tj-color-common-white);
				border-radius: 10px;
				position: relative;
				.comment-avatar {
					max-width: 64px;
					height: 64px;
					width: 100%;
					border-radius: 50%;
					overflow: hidden;
					img {
						width: 100%;
					}
				}
				.comments-header {
					margin-top: 10px;
					.avatar-name {
						.title {
							font-size: 18px;
							font-weight: var(--tj-fw-sbold);
							margin-bottom: 6px;
							a {
								&:hover {
									color: var(--tj-color-theme-primary);
								}
							}
						}
					}
					.comment-text {
						display: flex;
						flex-wrap: wrap;
						align-items: center;
						gap: 20px;
						.reply {
							position: absolute;
							top: 35px;
							right: 30px;
							font-size: 16px;
							font-family: var(--tj-ff-heading);
							font-weight: var(--tj-fw-medium);
							color: var(--tj-color-theme-dark-3);
							&:hover {
								color: var(--tj-color-theme-primary);
							}
						}
						span {
							font-size: 14px;
							color: var(--tj-color-theme-dark-4);
							font-weight: var(--tj-fw-regular);
						}
					}
				}
				@media #{$xs} {
					flex-direction: column;
					.comment-avatar {
						margin-right: auto;
					}
				}
			}
			.desc {
				margin-top: 14px;
				p {
					&:last-child {
						margin-bottom: 0;
					}
				}
			}
			> .children {
				padding-left: 75px;
				@media #{$xs} {
					padding-left: 35px;
				}
			}
			&:last-child {
				.comment-content {
					.comments-header {
						padding-bottom: 0;
						margin-bottom: 0;
						border-bottom: none;
					}
				}
			}
		}
	}
}

.tj-comments__container {
	margin-top: 50px;
}

.comment-respond {
	.comment-reply-title {
		margin-bottom: 25px;
	}
	.form-input {
		margin-bottom: 30px;
		input:not([type="submit"]):not([type="radio"]):not([type="checkbox"]):not(
				[type="search"]
			),
		textarea,
		input[type="email"],
		input[type="text"] {
			background: var(--tj-color-common-white);
			height: 60px;
			padding: 18px 20px;
			border: 1px solid var(--tj-color-common-white);
			border-radius: 10px;
			&::placeholder {
				color: var(--tj-color-text-body);
			}
			&:focus {
				border-color: var(--tj-color-theme-primary);
			}
		}
		textarea {
			min-height: 180px;
			resize: none;
		}
		@media #{$sm, $xs} {
			margin-bottom: 20px;
		}
	}
}

// sidebar
.tj-main-sidebar {
	padding-left: 20px;
	&.sticky-lg-top {
		top: 120px;
		z-index: 1;
	}
	@media #{$lg, $md, $sm, $xs} {
		padding-left: 0;
	}
}
.tj-sidebar-widget {
	background-color: var(--tj-color-theme-bg);
	margin-bottom: 30px;
	padding: 30px;
	position: relative;
	z-index: 3;
	border-radius: 10px;
	overflow: hidden;
	&:last-child {
		margin-bottom: 0;
	}
	.widget-title {
		color: var(--tj-color-heading-primary);
		font-weight: var(--tj-fw-sbold);
		position: relative;
		margin-bottom: 25px;
		line-height: 1;
		z-index: 1;
	}
	&.widget-search {
		.search-box {
			form {
				position: relative;
				width: 100%;
				z-index: 1;
				input[type="search"] {
					font-size: 16px;
					background: var(--tj-color-common-white);
					width: 100%;
					height: 60px;
					border: 1px solid var(--tj-color-border-1);
					border-radius: 8px;
					padding: 18px 65px 18px 20px;
					line-height: 1;
					&::placeholder {
						color: var(--tj-color-text-body);
					}
				}
				:focus {
					outline: none;
					box-shadow: 0 0 0;
				}
				button {
					font-size: 22px;
					color: var(--tj-color-heading-primary);
					width: 54px;
					height: 60px;
					position: absolute;
					top: 50%;
					right: 0;
					transform: translateY(-50%);
					border-left: 1px solid var(--tj-color-border-1);
					@include transition(all 0.2s ease-in-out 0s);
					i {
						display: inline-flex;
						line-height: 1;
					}
				}
			}
		}
	}
	&.tj-recent-posts {
		ul {
			margin: 0;
			padding: 0;
			list-style: none;
			li {
				display: flex;
				align-items: center;
				column-gap: 15px;
				margin-bottom: 15px;
				&:last-child {
					margin-bottom: 0;
				}
				&:hover {
					.post-thumb {
						img {
							transform: scale(1.15);
						}
					}
				}
			}
		}
		.post-thumb {
			position: relative;
			width: 100px;
			height: 100px;
			flex: 0 0 auto;
			border-radius: 8px;
			overflow: hidden;
			a {
				display: inline-block;
				width: 100%;
				img {
					width: 100%;
					height: 100%;
					object-fit: cover;
					transition: 0.6s;
				}
			}
		}
		.post-content {
			.post-title {
				font-size: 18px;
				line-height: 1.444;
				font-weight: var(--tj-fw-sbold);
				margin-bottom: 10px;
				a {
					&:hover {
						color: var(--tj-color-theme-primary);
					}
				}
			}
			.blog-meta {
				margin-bottom: 0;
				ul {
					li {
						font-size: 14px;
						color: var(--tj-color-text-body);
					}
				}
			}
		}
	}
	&.widget-categories {
		ul {
			margin: 0;
			padding: 0;
			list-style: none;
			li {
				margin-bottom: 10px;
				&:last-child {
					margin-bottom: 0;
				}
				a {
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-weight: var(--tj-fw-sbold);
					color: var(--tj-color-heading-primary);
					background: var(--tj-color-common-white);
					padding: 20px 15px 17px 25px;
					border-radius: 10px;
					span {
						color: var(--tj-color-heading-primary);
					}
					.icon {
						font-size: 24px;
						line-height: 1;
						display: inline-flex;
					}
					&:hover {
						color: var(--tj-color-common-white);
						background-color: var(--tj-color-theme-primary);
						span {
							color: var(--tj-color-common-white);
						}
					}
				}
			}
		}
	}
	@media #{$lg, $xs} {
		padding: 25px 15px;
	}
}

.tagcloud {
	display: flex;
	flex-wrap: wrap;
	column-gap: 8px;
	row-gap: 12px;
	a {
		background-color: var(--tj-color-common-white);
		font-size: 14px;
		line-height: 1;
		color: var(--tj-color-text-body);
		font-weight: var(--tj-fw-medium);
		padding: 5px 10px;
		border-radius: 4px;
		display: inline-flex;
		overflow: hidden;
		&:hover {
			background-color: var(--tj-color-theme-primary);
			color: var(--tj-color-common-white);
		}
	}
}

.infos-item {
	display: flex;
	column-gap: 8px;
	padding: 20px 0;
	&:last-child {
		border-bottom: none;
		padding-bottom: 0;
	}
	.project-icons {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		width: 40px;
		height: 40px;
		flex: 0 0 auto;
		font-size: 20px;
		color: var(--tj-color-common-white);
		background: var(--tj-color-theme-primary);
		border-radius: 50%;
		position: relative;
		top: 2px;
	}
	.project-text {
		span {
			display: inline-block;
			font-size: 14px;
			line-height: 1;
			color: var(--tj-color-text-body);
			margin: 0;
		}
		.title {
			line-height: 1.1;
			font-weight: var(--tj-fw-sbold);
			margin-bottom: 0;
			@media #{$xl, $lg} {
				font-size: 16px;
			}
		}
	}
}
.service-categories {
	ul {
		margin: 0;
		padding: 0;
		list-style: none;
		li {
			margin-bottom: 10px;
			&:last-child {
				margin-bottom: 0;
			}
			a {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-weight: var(--tj-fw-sbold);
				color: var(--tj-color-heading-primary);
				background: var(--tj-color-common-white);
				padding: 20px 15px 17px 25px;
				border-radius: 10px;
				span {
					color: var(--tj-color-heading-primary);
				}
				.icon {
					font-size: 24px;
					line-height: 1;
					display: inline-flex;
				}
				&:hover,
				&.active {
					color: var(--tj-color-common-white);
					background-color: var(--tj-color-theme-primary);
					span {
						color: var(--tj-color-common-white);
					}
				}
			}
		}
	}
}

.widget-feature-item {
	padding: 20px;
	.feature-box {
		position: relative;
		background: var(--tj-color-theme-dark);
		border-radius: 10px;
		overflow: hidden;
		.feature-content {
			padding: 30px 28px 30px;
			.title {
				font-size: 74px;
				color: var(--tj-color-common-white);
				margin-bottom: 15px;
				line-height: 1;
				@media #{$xl} {
					font-size: 60px;
				}
				@media #{$lg} {
					font-size: 50px;
				}
				@media #{$sm, $xs} {
					font-size: 60px;
				}
			}
			> span {
				display: block;
				font-size: 20px;
				font-family: var(--tj-ff-heading);
				font-weight: var(--tj-fw-medium);
				color: var(--tj-color-common-white);
				line-height: 1;
				margin-bottom: 26px;
				@media #{$xs} {
					margin-bottom: 20px;
				}
			}
			.feature-contact {
				background-color: var(--tj-color-theme-primary);
				padding: 7px 12px;
				border-radius: 50px;
				line-height: 1;
				overflow: hidden;
				display: inline-flex;
				margin: 0;
				span {
					color: var(--tj-color-common-white);
					font-size: 18px;
					font-weight: var(--tj-fw-sbold);
					margin: 0;
					text-shadow: 0 30px 0;
					display: inline-flex;
					line-height: 1;
				}
				i {
					color: var(--tj-color-common-white);
					font-size: 20px;
					margin-right: 6px;
					margin-left: 0;
				}
				&:hover {
					span {
						transform: translateY(-30px);
					}
				}
			}
			@media #{$lg, $xs} {
				padding: 30px 20px;
			}
		}
		@media #{$xs} {
			max-width: 360px;
			margin: 0 auto;
		}
		@media (max-width: 400px) {
			max-width: 320px;
			margin: 0 auto;
		}
	}
	.feature-images {
		width: 370px;
		height: 370px;
		border-radius: 50%;
		overflow: hidden;
		margin-left: 60px;
		margin-bottom: -50px;
		border: 5px solid var(--tj-color-theme-primary);
		img {
			height: 100%;
			width: 100%;
			object-fit: cover;
		}
		@media #{$xl, $lg} {
			width: 320px;
			height: 320px;
			margin-left: 45px;
		}
		@media #{$md} {
			width: 500px;
			height: 500px;
			margin-left: 260px;
			margin-bottom: -70px;
		}
		@media #{$sm} {
			margin-left: 180px;
		}
		@media (max-width: 400px) {
			width: 320px;
			height: 320px;
			margin-left: 45px;
		}
	}
	@media #{$xs} {
		padding: 20px 15px;
	}
}

.details-content-box {
	display: flex;
	flex-wrap: wrap;
	column-gap: 15px;
	row-gap: 15px;
	align-items: center;
	margin-bottom: 50px;
}
.service-details-item {
	background-color: var(--tj-color-common-white);
	border-radius: 10px;
	padding: 30px 17px 25px 18px;
	max-width: 275px;
	width: 100%;
	.number {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		width: 33px;
		height: 33px;
		font-size: 16px;
		font-family: var(--tj-ff-heading);
		font-weight: var(--tj-fw-sbold);
		color: var(--tj-color-common-white);
		background-color: var(--tj-color-theme-primary);
		border-radius: 50%;
		margin-bottom: 20px;
	}
	.title {
		line-height: 1.444;
		font-weight: var(--tj-fw-sbold);
		margin-bottom: 18px;
	}
	.desc {
		margin-bottom: 0;
		p {
			&:last-child {
				margin-bottom: 0;
			}
		}
	}
	@media #{$xl} {
		max-width: 235px;
	}
	@media #{$md} {
		max-width: 220px;
	}
	@media #{$sm} {
		max-width: 250px;
		padding: 30px 15px 15px 15px;
	}
	@media #{$xs} {
		max-width: 100%;
	}
}
.blog-text {
	.tj-faq {
		margin-bottom: 40px;
		padding-top: 10px;
	}
}
.gallery {
	position: relative;
	display: block;
	&::before {
		content: "\e91c";
		font-family: "bexon-icons" !important;
		color: var(--tj-color-common-white);
		font-size: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(12, 30, 33, 0.4);
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		opacity: 0;
		visibility: hidden;
	}
	&:hover {
		&::before {
			opacity: 1;
			visibility: visible;
		}
	}
}

/* !END: Blog Details CSS */
