
:root {
    --primary-color: #747baf;
    --secondary-color: #5d6491;
    --accent-color: #747baf;
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --neutral-50: #f9fafb;
    --neutral-100: #f3f4f6;
    --neutral-200: #e5e7eb;
    --neutral-300: #d1d5db;
    --neutral-400: #9ca3af;
    --neutral-500: #6b7280;
    --neutral-600: #4b5563;
    --neutral-700: #374151;
    --neutral-800: #1f2937;
    --neutral-900: #111827;

    --border-radius: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--neutral-700);
    background: #f8fafc;
    min-height: 100vh;
    padding: 1rem;
}

.container {
    max-width: 900px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
    overflow: hidden;
}

.tooltip-wrapper {
    background: var(--neutral-200);
    border-radius: 100%;
    width: 25px;
    height: 25px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: help;
    position: relative;
}

.tooltip-text {
    visibility: hidden;
    background-color: black;
    color: #fff;
    text-align: center;
    padding: 5px 8px;
    border-radius: 4px;
    position: absolute;
    z-index: 1;
    bottom: 100%;
    left: 0;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    min-width: 300px;
}

.tooltip-wrapper.show .tooltip-text {
    visibility: visible;
    opacity: 1;
}

.tooltip-wrapper:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
    transition-delay: 0s; /* show immediately on hover */
}

.grid-container {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
    grid-column: 1/-1;
}

/* Medium screens (e.g. tablets) */
@media (min-width: 768px) {
    .grid-container {
        grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
    }
}

/* Large screens (e.g. desktops) */
@media (min-width: 1024px) {
    .grid-container {
        grid-template-columns: repeat(3, 1fr); /* max 3 columns */
    }
}

.grid-item.span-1 {
    grid-column: span 1;
}
.grid-item.span-2 {
    grid-column: span 2;
}
.grid-item.span-3 {
    grid-column: span 3;
}

.grid-item.w-100 {
    grid-column: span 100;
}

.form-header {
    background: linear-gradient(135deg, #747baf 0%, #5d6491 100%);
    color: white;
    padding: 3rem 2rem;
    text-align: center;
    position: relative;
}

.form-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.form-header > * {
    position: relative;
    z-index: 1;
}

.form-header h1 {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
}

.form-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    font-weight: 300;
}

.progress-container {
    margin-top: 2rem;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.progress-fill {
    height: 100%;
    background: white;
    width: 16.67%;
    transition: width 0.4s ease;
    border-radius: 3px;
}

.step-indicators {
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow-x: auto;
    scroll-behavior: smooth;
    padding: 0.5rem 0;
    gap: 1rem;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.step-indicators::-webkit-scrollbar {
    display: none;
}

.step-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
    flex-shrink: 0;
    min-width: 60px;
}

.step {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    color: white;
    transition: var(--transition);
    font-size: 0.9rem;
    border: 2px solid transparent;
}

.step.active {
    background: white;
    color: var(--primary-color);
    border-color: white;
    transform: scale(1.1);
}

.step.completed {
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
}

.step-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    transition: var(--transition);
    white-space: nowrap;
    line-height: 1.2;
}

.step-wrapper:hover .step-label,
.step-wrapper .step.active ~ .step-label,
.step-wrapper .step.completed ~ .step-label {
    color: white;
}

.step-wrapper .step.active ~ .step-label {
    color: white;
    font-weight: 600;
}

.apply-form {
    padding: 3rem 2rem;
}

.form-step {
    display: none;
    animation: slideIn 0.4s ease;
}

.form-step.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-step h2 {
    font-size: 1.8rem;
    font-weight: 400;
    color: var(--neutral-800);
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
}

.form-step h2::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: var(--primary-color);
    margin-top: 1rem;
    margin-bottom: 2rem;
    border-radius: 2px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    position: relative;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

label {
    font-weight: 400;
    color: var(--neutral-600);
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    letter-spacing: 0.025em;
}

input,
select,
textarea {
    padding: 1rem 0 0.75rem 0;
    border: none;
    border-bottom: 2px dotted var(--neutral-300);
    border-radius: 0;
    font-size: 1rem;
    transition: var(--transition);
    background: transparent;
    width: 100%;
    color: var(--neutral-700);
    font-family: inherit;
    accent-color: var(--accent-color);
}

textarea{
    min-height: 150px;
}

input::placeholder,
textarea::placeholder {
    color: var(--neutral-400);
    font-style: italic;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-bottom-color: var(--primary-color);
    border-bottom-style: solid;
    background: rgba(116, 123, 175, 0.02);
}

input.invalid,
select.invalid,
textarea.invalid {
    border-bottom-color: var(--error-color);
    border-bottom-style: solid;
}

select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

textarea {
    resize: vertical;
    min-height: 100px;
    border: 2px dotted var(--neutral-300);
    border-radius: var(--border-radius);
    padding: 1rem;
}

textarea:focus {
    border-color: var(--primary-color);
    border-style: solid;
}

.error-message {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    min-height: 1.2rem;
    font-style: italic;
}

.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2.5rem;
    margin-top: 2rem;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-height: 52px;
    min-width: 140px;
    justify-content: center;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(116, 123, 175, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(116, 123, 175, 0.4);
}

.btn-secondary {
    background: var(--neutral-100);
    color: var(--neutral-600);
    border: 1px solid var(--neutral-300);
}

.btn-secondary:hover {
    background: var(--neutral-200);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(5, 150, 105, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(5, 150, 105, 0.4);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled::before {
    display: none;
}

.review-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.review-item {
    background: var(--neutral-50);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--neutral-200);
    position: relative;
}

.review-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px 0 0 2px;
}

.review-item h3 {
    color: var(--neutral-800);
    font-size: 1.2rem;
    font-weight: 500;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--neutral-300);
    letter-spacing: -0.025em;
}

.review-item div {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.review-item p {
    margin: 0.5rem 0;
    font-size: 0.95rem;
    line-height: 1.5;
}

.review-item strong {
    color: var(--neutral-800);
    font-weight: 500;
}

.declaration {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 2rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--neutral-300);
    margin-bottom: 1rem;
    position: relative;
}

.declaration::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.checkbox-container {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    cursor: pointer;
    line-height: 1.6;
    font-weight: 400;
    color: var(--neutral-700);
}

.checkbox-container input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
    cursor: pointer;
    accent-color: var(--primary-color);
}

.checkmark {
    flex-shrink: 0;
}

.success-message {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    padding: 3rem 2rem;
    border-radius: var(--border-radius);
    margin-top: 2rem;
    text-align: center;
    font-weight: 400;
    border: 1px solid #86efac;
}

.success-message h2 {
    font-size: 1.8rem;
    font-weight: 500;
    margin-bottom: 1rem;
    color: #047857;
}

.success-message p {
    margin: 1rem 0;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    body {
        padding: 0.5rem;
    }

    .form-header {
        padding: 2rem 1.5rem;
    }

    .form-header h1 {
        font-size: 2rem;
    }

    .apply-form {
        padding: 2rem 1.5rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .step-indicators {
        gap: 1.5rem;
        padding: 0.5rem 1rem;
    }

    .step-wrapper {
        min-width: 70px;
    }

    .step {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .step.active {
        transform: scale(1.15);
    }

    .step-label {
        font-size: 0.65rem;
        max-width: 60px;
        line-height: 1.1;
    }

    .form-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .btn {
        width: 100%;
        padding: 1.25rem 2rem;
    }

    input,
    select,
    textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .review-item {
        padding: 1.5rem;
    }

    .review-item div {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .form-header {
        padding: 1.5rem 1rem;
    }

    .form-header h1 {
        font-size: 1.75rem;
    }

    .apply-form {
        padding: 1.5rem 1rem;
    }

    .container {
        border-radius: 16px;
    }

    .step-indicators {
        gap: 2rem;
        padding: 0.5rem 1rem;
        margin: 0 -1rem;
    }

    .step-wrapper {
        min-width: 80px;
    }

    .step {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .step.active {
        transform: scale(1.2);
    }

    .step-label {
        font-size: 0.6rem;
        max-width: 70px;
        line-height: 1.1;
    }
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .step-indicators {
        scroll-behavior: auto;
    }
}

/* Focus indicators for keyboard navigation */
.btn:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #4c1d95;
        --secondary-color: #3730a3;
        --neutral-700: #000000;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--neutral-100);
}

::-webkit-scrollbar-thumb {
    background: var(--neutral-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--neutral-500);
}


/* Style for the "Add +" button */
.add-new-ele {
    margin-left: auto;
    width: auto;
    padding: 2px 5px;
}

/* Radio option label wrapper */
.radio-label {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin-right: 10px;
}
.radio-label input{
    width: 30px;
    height: 30px;
}

summary{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

details{
    padding: 0 10px;
    box-shadow: 0 2px 9px -2px var(--accent-color);
    border-radius: 10px;
    cursor: pointer;
    margin: 10px 0;
}

.delete-btn{
    background: var(--error-color);
    color: #fff;
    min-width: 50px;
    min-height: 30px;
    padding: 5px;
    width: auto;
}

.dyna-add-gp{
    border-top: solid var(--accent-color);
    margin-top: 5px;
    padding-top: 15px;
}

iframe img{
    height: 100%;
    margin: auto;
}