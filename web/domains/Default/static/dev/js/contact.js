(function (){
    $sel(".submit-contact-form").$on("click", (e, btn) => {
        e.preventDefault();
        $curl(
            getApi("get-started"),
            {
                form: btn,
                preload: $preloader,
            }
        )
            .finally(() => {
                $preloader("hide");
                reloadCaptcha(btn);
            })
            .then((res) => osModal({
                head: "Message sent",
                body: `<h4>${res.message}</h4>`,
                onClose: () => btn.closest("form").reset()
            }))
    })
})();