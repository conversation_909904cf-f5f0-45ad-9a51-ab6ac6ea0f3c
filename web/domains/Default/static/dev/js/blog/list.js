(function (){
    if($sel(".uh-oh"))
        return;

    let page = 2
    let stop = false;

    const listType = $field("list_type").value;

    const scrollHandler = () => {
        if(stop) return;

        const feedBottom  = $sel(".load-more-items")

        let api = getApi("blg/");

        if(listType === "category")
            api += "category/" + $end($lay.page.routeArray) + "/";

        if(listType === "author")
            api += "author/" + $end($lay.page.routeArray) + "/";

        if(listType === "search")
            api += "search?query=" + $get('query', true);

        if($view(feedBottom).inView)
            $win.requestAnimationFrame(() => {
                $curl(api + page, {
                    type: "text",
                    strict: false,
                    debounce: 1000,
                })
                    .catch(xhr => {
                        $on($win, "scroll", scrollHandler, "del")
                        osNote(xhr.statusText.message, "fail")
                    })
                    .then(res => {
                        if(res === "") {
                            stop = true;
                            feedBottom.$class('add', 'd-none');
                            return $on($win, "scroll", scrollHandler, "del")
                        }

                        feedBottom.$html("beforebegin", res)

                        page++
                    })

            })
    }

    $on($doc, "scroll", scrollHandler)
})();