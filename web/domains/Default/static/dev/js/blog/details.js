(function (){
    $curl(getApi("blg/related/" + $field('post_id').value))
        .then(resolve => {
            if(resolve.length === 0) return;

            let body = "";

            $loop(resolve, res => {
                const url = "blog/" + res.slug;
                body += (
                    `<li>
                        <div class="post-thumb">
                            <a href="${url}"> <img src="${res.thumbnail}" alt="${res.title}"></a>
                        </div>
                        <div class="post-content">
                            <h6 class="post-title">
                                <a href="${url}">${res.title}</a>
                            </h6>
                            <div class="blog-meta">
                                <ul>
                                    <li>${res.date}</li>
                                </ul>
                            </div>
                        </div>
                    </li>`
                )
            })

            $sel(".tj-recent-posts").$class('del', 'd-none');
            $sel(".related-post-container").$html(body);
        })

    // count page view after 10 seconds
    setTimeout(() => {
        $curl(getApi("blg/view/"), {
            data: {
                post_id: $field('post_id').value
            },
            method: 'PUT'
        }).catch(() => console.log("Error counting view"))
    }, 10000)
})();