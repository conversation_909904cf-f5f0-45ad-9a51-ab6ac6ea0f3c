!function(e){"use strict";e(window).on("load",(function(){e(".preloader").fadeOut(200)})),e("[data-bg-image]").each((function(){var t=e(this),r=t.data("bg-image");t.css("background-image","url("+r+")")}));var t,r,s="";if(e(window).on("scroll",(function(){var t,r,i;e(".header-area").length&&(t=e(".header-sticky"),r="sticky",i=e(window).scrollTop(),e(window).scrollTop()>500?i>s?t.removeClass(r):t.addClass(r):t.removeClass(r),s=i)})),e(".mobile_menu_bar").on("click",(function(){e(this).toggleClass("on")})),e("#mobile-menu").meanmenu({meanMenuContainer:".mobile_menu",meanScreenWidth:"991",meanExpand:['<i class="tji-arrow-down"></i>']}),e(".mobile_menu_bar").on("click",(function(){e(".hamburger-area").addClass("opened"),e(".body-overlay").addClass("opened"),e("body").toggleClass("overflow-hidden")})),e(".menu_offcanvas").on("click",(function(){e(".tj-offcanvas-area").toggleClass("opened"),e(".body-overlay").addClass("opened"),e("body").toggleClass("overflow-hidden")})),e(".hamburger_close_btn").on("click",(function(){e(".tj-offcanvas-area").removeClass("opened"),e(".hamburger-area").removeClass("opened"),e(".body-overlay").removeClass("opened"),e("body").toggleClass("overflow-hidden")})),e(".body-overlay").on("click",(function(){e(".tj-offcanvas-area").removeClass("opened"),e(".hamburger-area").removeClass("opened"),e(".body-overlay").removeClass("opened"),e("body").toggleClass("overflow-hidden")})),e(".header-search .search").on("click",(function(){e(this).addClass("search-hide"),e(".search_close_btn").addClass("close-show"),e(".search_popup").addClass("search-opened"),e(".search-popup-overlay").addClass("search-popup-overlay-open")})),e(".search_close_btn, .search-popup-overlay").on("click",(function(){e(".header-search .search").removeClass("search-hide"),e(".search_popup").removeClass("search-opened"),e(".search-popup-overlay").removeClass("search-popup-overlay-open"),e(".search_close_btn").removeClass("close-show")})),e(".fill-ratings span").length>0){var i=e(".fill-ratings span").width();e(".star-ratings").width(i)}if(e("select").length>0&&e("select").niceSelect(),e(".client-slider").length>0&&new Swiper(".client-slider",{slidesPerView:"auto",spaceBetween:0,freemode:!0,centeredSlides:!0,loop:!0,speed:5e3,allowTouchMove:!1,autoplay:{delay:1,disableOnInteraction:!0}}),e(".marquee-slider").length>0&&new Swiper(".marquee-slider",{slidesPerView:"auto",spaceBetween:0,freemode:!0,centeredSlides:!0,loop:!0,speed:7e3,allowTouchMove:!1,autoplay:{delay:1,disableOnInteraction:!0}}),e(".project-slider").length>0&&new Swiper(".project-slider",{slidesPerView:3,spaceBetween:30,centeredSlides:!0,loop:!0,autoplay:{delay:6e3},speed:1500,pagination:{el:".swiper-pagination-area",clickable:!0},breakpoints:{0:{slidesPerView:1.2,spaceBetween:15},576:{slidesPerView:1.7,spaceBetween:20},768:{slidesPerView:2,spaceBetween:20},992:{slidesPerView:2},1024:{slidesPerView:2.2},1400:{slidesPerView:2.31}}}),e(".project-slider-2").length>0&&new Swiper(".project-slider-2",{slidesPerView:4,spaceBetween:30,loop:!0,speed:1500,navigation:{nextEl:".slider-next",prevEl:".slider-prev"},pagination:{el:".swiper-pagination-area",clickable:!0},breakpoints:{0:{slidesPerView:1.2,spaceBetween:15},580:{slidesPerView:2,spaceBetween:20},991:{slidesPerView:3,spaceBetween:20},1024:{slidesPerView:3,spaceBetween:20},1400:{slidesPerView:4,spaceBetween:30}}}),e(".testimonial-slider").length>0)var o=new Swiper(".testimonial-slider",{slidesPerView:3,spaceBetween:28,centeredSlides:!0,loop:!0,speed:1500,autoplay:{delay:3e3},navigation:{nextEl:".slider-next",prevEl:".slider-prev"},pagination:{el:".swiper-pagination-area",clickable:!0},breakpoints:{0:{slidesPerView:1.2,spaceBetween:15,centeredSlides:!1},576:{slidesPerView:1.3,spaceBetween:20,centeredSlides:!1},768:{slidesPerView:1.4,spaceBetween:20,centeredSlides:!1},992:{slidesPerView:3},1200:{slidesPerView:3}}});if(e(".testimonial-slider-2").length>0)o=new Swiper(".testimonial-slider-2",{slidesPerView:1,spaceBetween:28,loop:!0,speed:1500,autoplay:{delay:3e3},navigation:{nextEl:".slider-next",prevEl:".slider-prev"},pagination:{el:".swiper-pagination-area",clickable:!0}});if(e(".client-thumb").length>0)var a=new Swiper(".client-thumb",{slidesPerView:3,spaceBetween:12,loop:!0,speed:1500,centeredSlides:!0,freeMode:!0,watchSlidesProgress:!0,slideToClickedSlide:!0});if(e(".testimonial-slider-3").length>0&&((o=new Swiper(".testimonial-slider-3",{slidesPerView:1,spaceBetween:28,loopedSlides:3,loop:!0,speed:1500,autoplay:{delay:3e3},navigation:{nextEl:".slider-next",prevEl:".slider-prev"},pagination:{el:".swiper-pagination-area",clickable:!0}})).controller.control=a,a.controller.control=o),e(".hero-thumb").length>0)var l=new Swiper(".hero-thumb",{loop:!1,spaceBetween:15,slidesPerView:3,freeMode:!0,watchSlidesProgress:!0});function n(){if(window.innerWidth>=992){ScrollTrigger.getAll().forEach((e=>e.kill()));const e=window.innerWidth>=992?100:120;gsap.to(".tj-sticky",{scrollTrigger:{trigger:".tj-sticky",start:`top ${e}`,end:`bottom ${e}`,pin:!0,scrub:1}})}}if(e(".hero-slider").length>0&&new Swiper(".hero-slider",{slidesPerView:1,spaceBetween:0,effect:"fade",loop:!0,speed:1400,autoplay:{delay:5e3},navigation:{nextEl:".slider-next",prevEl:".slider-prev"},thumbs:{swiper:l}}),e(".service-slider").length>0&&new Swiper(".service-slider",{slidesPerView:4.2,spaceBetween:28,centeredSlides:!0,loop:!0,speed:1500,autoplay:{delay:2e3},navigation:{nextEl:".slider-next",prevEl:".slider-prev"},pagination:{el:".swiper-pagination-area",clickable:!0},breakpoints:{0:{slidesPerView:1.3,spaceBetween:15},576:{slidesPerView:2,spaceBetween:15},768:{slidesPerView:2.6,spaceBetween:15},860:{slidesPerView:2.8,spaceBetween:15},1024:{slidesPerView:3.5,spaceBetween:15},1200:{slidesPerView:3.4},1400:{slidesPerView:4.2}}}),e(".blog-slider").length>0&&new Swiper(".blog-slider",{slidesPerView:2,spaceBetween:30,loop:!0,speed:1500,autoplay:{delay:2e3},navigation:{nextEl:".slider-next",prevEl:".slider-prev"},pagination:{el:".swiper-pagination-area",clickable:!0},breakpoints:{0:{slidesPerView:1,spaceBetween:15},576:{slidesPerView:1,spaceBetween:15},768:{slidesPerView:1,spaceBetween:20},992:{slidesPerView:2,spaceBetween:20},1200:{slidesPerView:2}}}),e(".accordion-item").length>0&&e(".accordion-item .faq-title").on("click",(function(){e(this).parent().hasClass("active")?e(this).parent().removeClass("active"):(e(this).parent().siblings().removeClass("active"),e(this).parent().addClass("active"))})),t=e("#back_to_top"),r=e(".back-to-top-wrapper"),e(window).on("scroll",(function(){e(window).scrollTop()>300?r.addClass("back-to-top-btn-show"):r.removeClass("back-to-top-btn-show")})),t.on("click",(function(t){t.preventDefault(),e("html, body").animate({scrollTop:0},"300")})),jQuery(".odometer").length>0&&jQuery(".odometer").each((function(){jQuery(this).appear((function(){var e=jQuery(this).attr("data-count");jQuery(this).html(e)}))})),e(".wow").length>0&&(new WOW).init(),e(".gallery").length>0&&new VenoBox({selector:".gallery",numeration:!0,spinner:"pulse"}),e(".video-popup").length>0&&new VenoBox({selector:".video-popup",numeration:!0,spinner:"pulse"}),e(".team-wrapper").length&&e(".team-item").on("mouseover",(function(){e(this).siblings(".team-item").removeClass("active"),e(this).addClass("active");const t=e(this).data("src"),r=e(".team-img img");r.fadeOut(300).css("transform","scale(1.1)").promise().done((function(){r.attr("src",t).fadeIn(300).css("transform","scale(1)")}))})),(()=>{const e=document.querySelectorAll(".tj-progress");e?.length&&e.forEach((e=>{const t=e.querySelector(".tj-progress-bar"),r=parseInt(t.getAttribute("data-percent",10))||0;console.log("Target progress:",r+"%"),new IntersectionObserver((s=>{s.forEach((s=>{if(s.isIntersecting){t.style.transition="width 2s ease-out",t.style.width=`${r}%`;const s=e.querySelector(".tj-progress-percent");if(s){let e=0;const t=setInterval((()=>{e++,s.textContent=`${e}%`,e>=r&&clearInterval(t)}),15)}}}))}),{root:null,threshold:[.3,.9]}).observe(e)}))})(),gsap.registerPlugin(ScrollTrigger,TweenMax,ScrollToPlugin),gsap.config({nullTargetWarn:!1}),n(),window.addEventListener("resize",(()=>{n()})),e(".text-anim").length){let e=.02,t=20,r=.1,s="power2.out";document.querySelectorAll(".text-anim").forEach((i=>{let o=new SplitText(i,{type:"chars, words"});gsap.from(o.chars,{duration:1,delay:r,x:t,autoAlpha:0,stagger:e,ease:s,scrollTrigger:{trigger:i,start:"top 85%"}})}))}if(e(".title-anim").length){let e=.01,t=.2,r="power1.inout";document.querySelectorAll(".title-anim").forEach((s=>{let i=new SplitText(s,{types:"lines, words"});gsap.from(i.chars,{y:"100%",duration:.5,delay:t,autoAlpha:0,stagger:e,ease:r,scrollTrigger:{trigger:s,start:"top 85%"}})}))}let c=window.innerWidth;const p=gsap.utils.toArray(".service-stack");p.length>0&&c>767&&p.forEach((e=>{gsap.to(e,{opacity:0,scale:.9,y:50,scrollTrigger:{trigger:e,scrub:!0,start:"top top",pin:!0,pinSpacing:!1,markers:!1}})}));const d=gsap.utils.toArray(".project-stack");d.length>0&&c>991&&d.forEach((e=>{gsap.to(e,{scrollTrigger:{trigger:e,scrub:!0,start:"top top",pin:!0,pinSpacing:!1,markers:!1}})})),gsap.to(".marquee-slider-wrapper-two",{scrollTrigger:{trigger:".tj-project-section-two",start:"top center-=200",pin:".marquee-slider-wrapper-two",end:"bottom bottom-=200",markers:!1,pinSpacing:!1,scrub:1}}),document.querySelectorAll(".rightSwipeWrap").forEach(((e,t)=>{gsap.set(e.querySelectorAll(".right-swipe"),{transformPerspective:1200,x:"10rem",rotateY:-20,opacity:0,transformOrigin:"right center"}),gsap.to(e.querySelectorAll(".right-swipe"),{transformPerspective:1200,x:0,rotateY:0,opacity:1,delay:.3,ease:"power3.out",scrollTrigger:{trigger:e,start:"top 80%",id:"rightSwipeWrap-"+t,toggleActions:"play none none none"}})})),document.querySelectorAll(".leftSwipeWrap").forEach(((e,t)=>{gsap.set(e.querySelectorAll(".left-swipe"),{transformPerspective:1200,x:"-10rem",rotateY:20,opacity:0,transformOrigin:"left center"}),gsap.to(e.querySelectorAll(".left-swipe"),{transformPerspective:1200,x:0,rotateY:0,opacity:1,delay:.4,ease:"power3.out",scrollTrigger:{trigger:e,start:"top 80%",id:"leftSwipeWrap-"+t,toggleActions:"play none none none"}})})),document.querySelectorAll(".tjParallaxSection").forEach(((e,t)=>{const r=e.querySelector(".tjParallaxImage");r&&gsap.to(r,{y:"-25%",ease:"none",scrollTrigger:{trigger:e,start:"top bottom",end:"bottom top",scrub:!0}})})),document.querySelectorAll(".itemScrollAnimate").forEach(((e,t)=>{const r=t%2==0;gsap.fromTo(e,{transform:r?"perspective(1000px) rotateX(50deg)":"perspective(1000px) rotateX(-50deg)"},{transform:"perspective(1000px) rotateX(0deg)",duration:2,ease:"power3.out",scrollTrigger:{id:`teamItemTrigger-${t}`,trigger:e,start:"top 100%",end:"top 40%",scrub:!0}})}));const g=e(".tj-cursor"),w=e(".project-slider-one");w.length>0&&(gsap.set(g,{xPercent:-50,yPercent:-50}),e(document).on("pointermove",(function(e){gsap.to(g,{duration:0,x:e.clientX,y:e.clientY})})),w.on("mouseenter",(function(){g.css({opacity:1,visibility:"visible"})})),w.on("mouseleave",(function(){g.css({opacity:0,visibility:"hidden"})}))),e(".tj-text-invert").length&&new SplitText(".tj-text-invert",{type:"lines"}).lines.forEach((e=>{gsap.to(e,{backgroundPositionX:0,ease:"none",scrollTrigger:{trigger:e,scrub:1,start:"top 75%",end:"bottom center"}})}))}(jQuery);