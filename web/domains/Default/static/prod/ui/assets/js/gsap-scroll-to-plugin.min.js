/*!
 * ScrollToPlugin 3.11.4
 * https://greensock.com
 * 
 * @license Copyright 2022, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,(function(e){"use strict";function t(){return"undefined"!=typeof window}function o(){return c||t()&&(c=window.gsap)&&c.registerPlugin&&c}function n(e){return"string"==typeof e}function l(e){return"function"==typeof e}function r(e,t){var o="x"===t?"Width":"Height",n="scroll"+o,l="client"+o;return e===a||e===y||e===d?Math.max(y[n],d[n])-(a["inner"+o]||y[l]||d[l]):e[n]-e["offset"+o]}function i(e,t){var o="scroll"+("x"===t?"Left":"Top");return e===a&&(null!=e.pageXOffset?o="page"+t.toUpperCase()+"Offset":e=null!=y[o]?y:d),function(){return e[o]}}function s(e,t){if(!(e=g(e)[0])||!e.getBoundingClientRect)return console.warn("scrollTo target doesn't exist. Using 0")||{x:0,y:0};var o=e.getBoundingClientRect(),n=!t||t===a||t===d,l=n?{top:y.clientTop-(a.pageYOffset||y.scrollTop||d.scrollTop||0),left:y.clientLeft-(a.pageXOffset||y.scrollLeft||d.scrollLeft||0)}:t.getBoundingClientRect(),r={x:o.left-l.left,y:o.top-l.top};return!n&&t&&(r.x+=i(t,"x")(),r.y+=i(t,"y")()),r}function p(e,t,o,l,i){return isNaN(e)||"object"==typeof e?n(e)&&"="===e.charAt(1)?parseFloat(e.substr(2))*("-"===e.charAt(0)?-1:1)+l-i:"max"===e?r(t,o)-i:Math.min(r(t,o),s(e,t)[o]-i):parseFloat(e)-i}function f(){c=o(),t()&&c&&"undefined"!=typeof document&&document.body&&(a=window,d=document.body,y=document.documentElement,g=c.utils.toArray,c.config({autoKillThreshold:7}),x=c.config(),u=1)}var c,u,a,y,d,g,x,T,v={version:"3.11.4",name:"scrollTo",rawVars:1,register:function(e){c=e,f()},init:function(e,t,o,r,s){u||f();var y=this,d=c.getProperty(e,"scrollSnapType");y.isWin=e===a,y.target=e,y.tween=o,t=function(e,t,o,r){if(l(e)&&(e=e(t,o,r)),"object"!=typeof e)return n(e)&&"max"!==e&&"="!==e.charAt(1)?{x:e,y:e}:{y:e};if(e.nodeType)return{y:e,x:e};var i,s={};for(i in e)s[i]="onAutoKill"!==i&&l(e[i])?e[i](t,o,r):e[i];return s}(t,r,e,s),y.vars=t,y.autoKill=!!t.autoKill,y.getX=i(e,"x"),y.getY=i(e,"y"),y.x=y.xPrev=y.getX(),y.y=y.yPrev=y.getY(),T=T||c.core.globals().ScrollTrigger,"smooth"===c.getProperty(e,"scrollBehavior")&&c.set(e,{scrollBehavior:"auto"}),d&&"none"!==d&&(y.snap=1,y.snapInline=e.style.scrollSnapType,e.style.scrollSnapType="none"),null!=t.x?(y.add(y,"x",y.x,p(t.x,e,"x",y.x,t.offsetX||0),r,s),y._props.push("scrollTo_x")):y.skipX=1,null!=t.y?(y.add(y,"y",y.y,p(t.y,e,"y",y.y,t.offsetY||0),r,s),y._props.push("scrollTo_y")):y.skipY=1},render:function(e,t){for(var o,n,l,i,s,p=t._pt,f=t.target,c=t.tween,u=t.autoKill,y=t.xPrev,d=t.yPrev,g=t.isWin,v=t.snap,h=t.snapInline;p;)p.r(e,p.d),p=p._next;o=g||!t.skipX?t.getX():y,l=(n=g||!t.skipY?t.getY():d)-d,i=o-y,s=x.autoKillThreshold,t.x<0&&(t.x=0),t.y<0&&(t.y=0),u&&(!t.skipX&&(s<i||i<-s)&&o<r(f,"x")&&(t.skipX=1),!t.skipY&&(s<l||l<-s)&&n<r(f,"y")&&(t.skipY=1),t.skipX&&t.skipY&&(c.kill(),t.vars.onAutoKill&&t.vars.onAutoKill.apply(c,t.vars.onAutoKillParams||[]))),g?a.scrollTo(t.skipX?o:t.x,t.skipY?n:t.y):(t.skipY||(f.scrollTop=t.y),t.skipX||(f.scrollLeft=t.x)),!v||1!==e&&0!==e||(n=f.scrollTop,o=f.scrollLeft,h?f.style.scrollSnapType=h:f.style.removeProperty("scroll-snap-type"),f.scrollTop=n+1,f.scrollLeft=o+1,f.scrollTop=n,f.scrollLeft=o),t.xPrev=t.x,t.yPrev=t.y,T&&T.update()},kill:function(e){var t="scrollTo"===e;!t&&"scrollTo_x"!==e||(this.skipX=1),!t&&"scrollTo_y"!==e||(this.skipY=1)}};v.max=r,v.getOffset=s,v.buildGetter=i,o()&&c.registerPlugin(v),e.ScrollToPlugin=v,e.default=v,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default}));