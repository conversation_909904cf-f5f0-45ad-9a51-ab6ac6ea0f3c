/*!
 * ScrollTrigger 3.11.4
 * https://greensock.com
 * 
 * @license Copyright 2022, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,(function(e){"use strict";function t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function n(){return v||"undefined"!=typeof window&&(v=window.gsap)&&v.registerPlugin&&v}function r(e,t){return~D.indexOf(e)&&D[D.indexOf(e)+1][t]}function o(e){return!!~E.indexOf(e)}function i(e,t,n,r,o){return e.addEventListener(t,n,{passive:!r,capture:!!o})}function a(e,t,n,r){return e.removeEventListener(t,n,!!r)}function s(){return T&&T.isPressed||A.cache++}function l(e,t){function n(r){if(r||0===r){C&&(y.history.scrollRestoration="manual");var o=T&&T.isPressed;r=n.v=Math.round(r)||(T&&T.iOS?1:0),e(r),n.cacheID=A.cache,o&&Y("ss",r)}else(t||A.cache!==n.cacheID||Y("ref"))&&(n.cacheID=A.cache,n.v=e());return n.v+n.offset}return n.offset=0,e&&n}function c(e){return v.utils.toArray(e)[0]||("string"==typeof e&&!1!==v.config().nullTargetWarn?console.warn("Element not found:",e):null)}function u(e,t){var n=t.s,i=t.sc;o(e)&&(e=b.scrollingElement||x);var a=A.indexOf(e),c=i===B.sc?1:2;~a||(a=A.push(e)-1),A[a+c]||e.addEventListener("scroll",s);var u=A[a+c],f=u||(A[a+c]=l(r(e,n),!0)||(o(e)?i:l((function(t){return arguments.length?e[n]=t:e[n]}))));return f.target=e,u||(f.smooth="smooth"===v.getProperty(e,"scrollBehavior")),f}function f(e,t,n){function r(e,t){var r=R();t||l<r-a?(i=o,o=e,s=a,a=r):n?o+=e:o=i+(e-i)/(r-s)*(a-s)}var o=e,i=e,a=R(),s=a,l=t||50,c=Math.max(500,3*l);return{update:r,reset:function(){i=o=n?0:o,s=a=0},getVelocity:function(e){var t=s,l=i,u=R();return!e&&0!==e||e===o||r(e),a===s||c<u-s?0:(o+(n?l:-l))/((n?u:a)-t)*1e3}}}function d(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e}function p(e){var t=Math.max.apply(Math,e),n=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(n)?t:n}function h(){(k=v.core.globals().ScrollTrigger)&&k.core&&function(){var e=k.core,t=e.bridge||{},n=e._scrollers,r=e._proxies;n.push.apply(n,A),r.push.apply(r,D),A=n,D=r,Y=function(e,n){return t[e](n)}}()}function g(e){return(v=e||n())&&"undefined"!=typeof document&&document.body&&(y=window,x=(b=document).documentElement,w=b.body,E=[y,b,x,w],v.utils.clamp,M=v.core.context||function(){},S="onpointerenter"in w?"pointer":"mouse",_=F.isTouch=y.matchMedia&&y.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in y||0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints?2:0,P=F.eventTypes=("ontouchstart"in x?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in x?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout((function(){return C=0}),500),h(),m=1),m}var v,m,y,b,x,w,_,S,k,E,T,P,M,C=1,O=[],A=[],D=[],R=Date.now,Y=function(e,t){return t},I="scrollLeft",X="scrollTop",z={s:I,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:l((function(e){return arguments.length?y.scrollTo(e,B.sc()):y.pageXOffset||b[I]||x[I]||w[I]||0}))},B={s:X,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:z,sc:l((function(e){return arguments.length?y.scrollTo(z.sc(),e):y.pageYOffset||b[X]||x[X]||w[X]||0}))};z.op=B,A.cache=0;var F=(L.prototype.init=function(e){m||g(v)||console.warn("Please gsap.registerPlugin(Observer)"),k||h();var t=e.tolerance,n=e.dragMinimum,r=e.type,l=e.target,E=e.lineHeight,C=e.debounce,A=e.preventDefault,D=e.onStop,Y=e.onStopDelay,I=e.ignore,X=e.wheelSpeed,F=e.event,L=e.onDragStart,N=e.onDragEnd,W=e.onDrag,H=e.onPress,q=e.onRelease,V=e.onRight,U=e.onLeft,j=e.onUp,G=e.onDown,K=e.onChangeX,Z=e.onChangeY,$=e.onChange,J=e.onToggleX,Q=e.onToggleY,ee=e.onHover,te=e.onHoverEnd,ne=e.onMove,re=e.ignoreCheck,oe=e.isNormalizer,ie=e.onGestureStart,ae=e.onGestureEnd,se=e.onWheel,le=e.onEnable,ce=e.onDisable,ue=e.onClick,fe=e.scrollSpeed,de=e.capture,pe=e.allowClicks,he=e.lockAxis,ge=e.onLockAxis;function ve(){return Ze=R()}function me(e,t){return(Be.event=e)&&I&&~I.indexOf(e.target)||t&&Ve&&"touch"!==e.pointerType||re&&re(e,t)}function ye(){var e=Be.deltaX=p(Ge),n=Be.deltaY=p(Ke),r=Math.abs(e)>=t,o=Math.abs(n)>=t;$&&(r||o)&&$(Be,e,n,Ge,Ke),r&&(V&&0<Be.deltaX&&V(Be),U&&Be.deltaX<0&&U(Be),K&&K(Be),J&&Be.deltaX<0!=Fe<0&&J(Be),Fe=Be.deltaX,Ge[0]=Ge[1]=Ge[2]=0),o&&(G&&0<Be.deltaY&&G(Be),j&&Be.deltaY<0&&j(Be),Z&&Z(Be),Q&&Be.deltaY<0!=Le<0&&Q(Be),Le=Be.deltaY,Ke[0]=Ke[1]=Ke[2]=0),(Ye||Re)&&(ne&&ne(Be),Re&&(W(Be),Re=!1),Ye=!1),Xe&&!(Xe=!1)&&ge&&ge(Be),Ie&&(se(Be),Ie=!1),Ae=0}function be(e,t,n){Ge[n]+=e,Ke[n]+=t,Be._vx.update(e),Be._vy.update(t),C?Ae=Ae||requestAnimationFrame(ye):ye()}function xe(e,t){he&&!ze&&(Be.axis=ze=Math.abs(e)>Math.abs(t)?"x":"y",Xe=!0),"y"!==ze&&(Ge[2]+=e,Be._vx.update(e,!0)),"x"!==ze&&(Ke[2]+=t,Be._vy.update(t,!0)),C?Ae=Ae||requestAnimationFrame(ye):ye()}function we(e){if(!me(e,1)){var t=(e=d(e,A)).clientX,r=e.clientY,o=t-Be.x,i=r-Be.y,a=Be.isDragging;Be.x=t,Be.y=r,(a||Math.abs(Be.startX-t)>=n||Math.abs(Be.startY-r)>=n)&&(W&&(Re=!0),a||(Be.isDragging=!0),xe(o,i),a||L&&L(Be))}}function _e(e){if(!me(e,1)){a(oe?l:je,P[1],we,!0);var t=!isNaN(Be.y-Be.startY),n=Be.isDragging&&(3<Math.abs(Be.x-Be.startX)||3<Math.abs(Be.y-Be.startY)),r=d(e);!n&&t&&(Be._vx.reset(),Be._vy.reset(),A&&pe&&v.delayedCall(.08,(function(){if(300<R()-Ze&&!e.defaultPrevented)if(e.target.click)e.target.click();else if(je.createEvent){var t=je.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,y,1,r.screenX,r.screenY,r.clientX,r.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(t)}}))),Be.isDragging=Be.isGesturing=Be.isPressed=!1,D&&!oe&&De.restart(!0),N&&n&&N(Be),q&&q(Be,n)}}function Se(e){return e.touches&&1<e.touches.length&&(Be.isGesturing=!0)&&ie(e,Be.isDragging)}function ke(){return(Be.isGesturing=!1)||ae(Be)}function Ee(e){if(!me(e)){var t=Ne(),n=We();be((t-He)*fe,(n-qe)*fe,1),He=t,qe=n,D&&De.restart(!0)}}function Te(e){if(!me(e)){e=d(e,A),se&&(Ie=!0);var t=(1===e.deltaMode?E:2===e.deltaMode?y.innerHeight:1)*X;be(e.deltaX*t,e.deltaY*t,0),D&&!oe&&De.restart(!0)}}function Pe(e){if(!me(e)){var t=e.clientX,n=e.clientY,r=t-Be.x,o=n-Be.y;Be.x=t,Be.y=n,Ye=!0,(r||o)&&xe(r,o)}}function Me(e){Be.event=e,ee(Be)}function Ce(e){Be.event=e,te(Be)}function Oe(e){return me(e)||d(e,A)&&ue(Be)}this.target=l=c(l)||x,this.vars=e,I=I&&v.utils.toArray(I),t=t||1e-9,n=n||0,X=X||1,fe=fe||1,r=r||"wheel,touch,pointer",C=!1!==C,E=E||parseFloat(y.getComputedStyle(w).lineHeight)||22;var Ae,De,Re,Ye,Ie,Xe,ze,Be=this,Fe=0,Le=0,Ne=u(l,z),We=u(l,B),He=Ne(),qe=We(),Ve=~r.indexOf("touch")&&!~r.indexOf("pointer")&&"pointerdown"===P[0],Ue=o(l),je=l.ownerDocument||b,Ge=[0,0,0],Ke=[0,0,0],Ze=0,$e=Be.onPress=function(e){me(e,1)||(Be.axis=ze=null,De.pause(),Be.isPressed=!0,e=d(e),Fe=Le=0,Be.startX=Be.x=e.clientX,Be.startY=Be.y=e.clientY,Be._vx.reset(),Be._vy.reset(),i(oe?l:je,P[1],we,A,!0),Be.deltaX=Be.deltaY=0,H&&H(Be))};De=Be._dc=v.delayedCall(Y||.25,(function(){Be._vx.reset(),Be._vy.reset(),De.pause(),D&&D(Be)})).pause(),Be.deltaX=Be.deltaY=0,Be._vx=f(0,50,!0),Be._vy=f(0,50,!0),Be.scrollX=Ne,Be.scrollY=We,Be.isDragging=Be.isGesturing=Be.isPressed=!1,M(this),Be.enable=function(e){return Be.isEnabled||(i(Ue?je:l,"scroll",s),0<=r.indexOf("scroll")&&i(Ue?je:l,"scroll",Ee,A,de),0<=r.indexOf("wheel")&&i(l,"wheel",Te,A,de),(0<=r.indexOf("touch")&&_||0<=r.indexOf("pointer"))&&(i(l,P[0],$e,A,de),i(je,P[2],_e),i(je,P[3],_e),pe&&i(l,"click",ve,!1,!0),ue&&i(l,"click",Oe),ie&&i(je,"gesturestart",Se),ae&&i(je,"gestureend",ke),ee&&i(l,S+"enter",Me),te&&i(l,S+"leave",Ce),ne&&i(l,S+"move",Pe)),Be.isEnabled=!0,e&&e.type&&$e(e),le&&le(Be)),Be},Be.disable=function(){Be.isEnabled&&(O.filter((function(e){return e!==Be&&o(e.target)})).length||a(Ue?je:l,"scroll",s),Be.isPressed&&(Be._vx.reset(),Be._vy.reset(),a(oe?l:je,P[1],we,!0)),a(Ue?je:l,"scroll",Ee,de),a(l,"wheel",Te,de),a(l,P[0],$e,de),a(je,P[2],_e),a(je,P[3],_e),a(l,"click",ve,!0),a(l,"click",Oe),a(je,"gesturestart",Se),a(je,"gestureend",ke),a(l,S+"enter",Me),a(l,S+"leave",Ce),a(l,S+"move",Pe),Be.isEnabled=Be.isPressed=Be.isDragging=!1,ce&&ce(Be))},Be.kill=Be.revert=function(){Be.disable();var e=O.indexOf(Be);0<=e&&O.splice(e,1),T===Be&&(T=0)},O.push(Be),oe&&o(l)&&(T=Be),Be.enable(F)},function(e,n,r){n&&t(e.prototype,n)}(L,[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}]),L);function L(e){this.init(e)}function N(){return Le=1}function W(){return Le=0}function H(e){return e}function q(e){return Math.round(1e5*e)/1e5||0}function V(){return"undefined"!=typeof window}function U(){return Pe||V()&&(Pe=window.gsap)&&Pe.registerPlugin&&Pe}function j(e){return!!~Re.indexOf(e)}function G(e){return r(e,"getBoundingClientRect")||(j(e)?function(){return Vt.width=Ce.innerWidth,Vt.height=Ce.innerHeight,Vt}:function(){return Et(e)})}function K(e,t){var n=t.s,o=t.d2,i=t.d,a=t.a;return(n="scroll"+o)&&(a=r(e,n))?a()-G(e)()[i]:j(e)?(Ae[n]||De[n])-(Ce["inner"+o]||Ae["client"+o]||De["client"+o]):e[n]-e["offset"+o]}function Z(e,t){for(var n=0;n<Ve.length;n+=3)t&&!~t.indexOf(Ve[n+1])||e(Ve[n],Ve[n+1],Ve[n+2])}function $(e){return"string"==typeof e}function J(e){return"function"==typeof e}function Q(e){return"number"==typeof e}function ee(e){return"object"==typeof e}function te(e,t,n){return e&&e.progress(t?0:1)&&n&&e.pause()}function ne(e,t){if(e.enabled){var n=t(e);n&&n.totalTime&&(e.callbackAnimation=n)}}function re(e){return Ce.getComputedStyle(e)}function oe(e,t){for(var n in t)n in e||(e[n]=t[n]);return e}function ie(e,t){var n=t.d2;return e["offset"+n]||e["client"+n]||0}function ae(e){var t,n=[],r=e.labels,o=e.duration();for(t in r)n.push(r[t]/o);return n}function se(e){var t=Pe.utils.snap(e),n=Array.isArray(e)&&e.slice(0).sort((function(e,t){return e-t}));return n?function(e,r,o){var i;if(void 0===o&&(o=.001),!r)return t(e);if(0<r){for(e-=o,i=0;i<n.length;i++)if(n[i]>=e)return n[i];return n[i-1]}for(i=n.length,e+=o;i--;)if(n[i]<=e)return n[i];return n[0]}:function(n,r,o){void 0===o&&(o=.001);var i=t(n);return!r||Math.abs(i-n)<o||i-n<0==r<0?i:t(r<0?n-e:n+e)}}function le(e,t,n,r){return n.split(",").forEach((function(n){return e(t,n,r)}))}function ce(e,t,n,r,o){return e.addEventListener(t,n,{passive:!r,capture:!!o})}function ue(e,t,n,r){return e.removeEventListener(t,n,!!r)}function fe(e,t,n){return n&&n.wheelHandler&&e(t,"wheel",n)}function de(e,t){if($(e)){var n=e.indexOf("="),r=~n?(e.charAt(n-1)+1)*parseFloat(e.substr(n+1)):0;~n&&(e.indexOf("%")>n&&(r*=t/100),e=e.substr(0,n-1)),e=r+(e in Mt?Mt[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e}function pe(e,t,n,o,i,a,s,l){var c=i.startColor,u=i.endColor,f=i.fontSize,d=i.indent,p=i.fontWeight,h=Oe.createElement("div"),g=j(n)||"fixed"===r(n,"pinType"),v=-1!==e.indexOf("scroller"),m=g?De:n,y=-1!==e.indexOf("start"),b=y?c:u,x="border-color:"+b+";font-size:"+f+";color:"+b+";font-weight:"+p+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return x+="position:"+((v||l)&&g?"fixed;":"absolute;"),!v&&!l&&g||(x+=(o===B?pt:ht)+":"+(a+parseFloat(d))+"px;"),s&&(x+="box-sizing:border-box;text-align:left;width:"+s.offsetWidth+"px;"),h._isStart=y,h.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),h.style.cssText=x,h.innerText=t||0===t?e+"-"+t:e,m.children[0]?m.insertBefore(h,m.children[0]):m.appendChild(h),h._offset=h["offset"+o.op.d2],Ct(h,0,o,y),h}function he(){return 34<lt()-ut&&(rt=rt||requestAnimationFrame(Lt))}function ge(){Ke&&Ke.isPressed&&!(Ke.startX>De.clientWidth)||(A.cache++,Ke?rt=rt||requestAnimationFrame(Lt):Lt(),ut||Yt("scrollStart"),ut=lt())}function ve(){Je=Ce.innerWidth,$e=Ce.innerHeight}function me(){A.cache++,Fe||Ge||Oe.fullscreenElement||Oe.webkitFullscreenElement||Ze&&Je===Ce.innerWidth&&!(Math.abs(Ce.innerHeight-$e)>.25*Ce.innerHeight)||Ye.restart(!0)}function ye(){return ue(jt,"scrollEnd",ye)||zt(!0)}function be(e){for(var t=0;t<It.length;t+=5)(!e||It[t+4]&&It[t+4].query===e)&&(It[t].style.cssText=It[t+1],It[t].getBBox&&It[t].setAttribute("transform",It[t+2]||""),It[t+3].uncache=1)}function xe(e,t){var n;for(We=0;We<Ot.length;We++)!(n=Ot[We])||t&&n._ctx!==t||(e?n.kill(1):n.revert(!0,!0));t&&be(t),t||Yt("revert")}function we(e,t){A.cache++,!t&&ot||A.forEach((function(e){return J(e)&&e.cacheID++&&(e.rec=0)})),$(e)&&(Ce.history.scrollRestoration=tt=e)}function _e(e,t,n,r){if(!e._gsap.swappedIn){for(var o,i=Nt.length,a=t.style,s=e.style;i--;)a[o=Nt[i]]=n[o];a.position="absolute"===n.position?"absolute":"relative","inline"===n.display&&(a.display="inline-block"),s[ht]=s[pt]="auto",a.flexBasis=n.flexBasis||"auto",a.overflow="visible",a.boxSizing="border-box",a[gt]=ie(e,z)+kt,a[vt]=ie(e,B)+kt,a[wt]=s[_t]=s.top=s.left="0",qt(r),s[gt]=s.maxWidth=n[gt],s[vt]=s.maxHeight=n[vt],s[wt]=n[wt],e.parentNode!==t&&(e.parentNode.insertBefore(t,e),t.appendChild(e)),e._gsap.swappedIn=!0}}function Se(e){for(var t=Wt.length,n=e.style,r=[],o=0;o<t;o++)r.push(Wt[o],n[Wt[o]]);return r.t=e,r}function ke(e,t,n,r,o,i,a,s,l,u,f,d,p){J(e)&&(e=e(s)),$(e)&&"max"===e.substr(0,3)&&(e=d+("="===e.charAt(4)?de("0"+e.substr(3),n):0));var h,g,v,m=p?p.time():0;if(p&&p.seek(0),Q(e))a&&Ct(a,n,r,!0);else{J(t)&&(t=t(s));var y,b,x,w,_=(e||"0").split(" ");v=c(t)||De,(y=Et(v)||{})&&(y.left||y.top)||"none"!==re(v).display||(w=v.style.display,v.style.display="block",y=Et(v),w?v.style.display=w:v.style.removeProperty("display")),b=de(_[0],y[r.d]),x=de(_[1]||"0",n),e=y[r.p]-l[r.p]-u+b+o-x,a&&Ct(a,x,r,n-x<20||a._isStart&&20<x),n-=n-x}if(i){var S=e+n,k=i._isStart;h="scroll"+r.d2,Ct(i,S,r,k&&20<S||!k&&(f?Math.max(De[h],Ae[h]):i.parentNode[h])<=S+1),f&&(l=Et(a),f&&(i.style[r.op.p]=l[r.op.p]-r.op.m-i._offset+kt))}return p&&v&&(h=Et(v),p.seek(d),g=Et(v),p._caScrollDist=h[r.p]-g[r.p],e=e/p._caScrollDist*d),p&&p.seek(m),p?e:Math.round(e)}function Ee(e,t,n,r){if(e.parentNode!==t){var o,i,a=e.style;if(t===De){for(o in e._stOrig=a.cssText,i=re(e))+o||Ut.test(o)||!i[o]||"string"!=typeof a[o]||"0"===o||(a[o]=i[o]);a.top=n,a.left=r}else a.cssText=e._stOrig;Pe.core.getCache(e).uncache=1,t.appendChild(e)}}function Te(e,t){function n(t,s,l,c,u){var f=n.tween,d=s.onComplete;return l=l||i(),u=c&&u||0,c=c||t-l,f&&f.kill(),r=Math.round(l),s[a]=t,(s.modifiers={})[a]=function(e){return(e=Math.round(i()))!==r&&e!==o&&3<Math.abs(e-r)&&3<Math.abs(e-o)?(f.kill(),n.tween=0):e=l+c*f.ratio+u*f.ratio*f.ratio,o=r,r=Math.round(e)},s.onUpdate=function(){A.cache++,Lt()},s.onComplete=function(){n.tween=0,d&&d.call(f)},f=n.tween=Pe.to(e,s)}var r,o,i=u(e,t),a="_scroll"+t.p2;return(e[a]=i).wheelHandler=function(){return n.tween&&n.tween.kill()&&(n.tween=0)},ce(e,"wheel",i.wheelHandler),n}F.version="3.11.4",F.create=function(e){return new F(e)},F.register=g,F.getAll=function(){return O.slice()},F.getById=function(e){return O.filter((function(t){return t.vars.id===e}))[0]},n()&&v.registerPlugin(F);var Pe,Me,Ce,Oe,Ae,De,Re,Ye,Ie,Xe,ze,Be,Fe,Le,Ne,We,He,qe,Ve,Ue,je,Ge,Ke,Ze,$e,Je,Qe,et,tt,nt,rt,ot,it,at,st=1,lt=Date.now,ct=lt(),ut=0,ft=0,dt=Math.abs,pt="right",ht="bottom",gt="width",vt="height",mt="Right",yt="Left",bt="Top",xt="Bottom",wt="padding",_t="margin",St="Width",kt="px",Et=function(e,t){var n=t&&"matrix(1, 0, 0, 1, 0, 0)"!==re(e)[Ne]&&Pe.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),r=e.getBoundingClientRect();return n&&n.progress(0).kill(),r},Tt={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Pt={toggleActions:"play",anticipatePin:0},Mt={top:0,left:0,center:.5,bottom:1,right:1},Ct=function(e,t,n,r){var o={display:"block"},i=n[r?"os2":"p2"],a=n[r?"p2":"os2"];e._isFlipped=r,o[n.a+"Percent"]=r?-100:0,o[n.a]=r?"1px":0,o["border"+i+St]=1,o["border"+a+St]=0,o[n.p]=t+"px",Pe.set(e,o)},Ot=[],At={},Dt={},Rt=[],Yt=function(e){return Dt[e]&&Dt[e].map((function(e){return e()}))||Rt},It=[],Xt=0,zt=function(e,t){if(!ut||e){ot=jt.isRefreshing=!0,A.forEach((function(e){return J(e)&&e.cacheID++&&(e.rec=e())}));var n=Yt("refreshInit");Ue&&jt.sort(),t||xe(),A.forEach((function(e){J(e)&&(e.smooth&&(e.target.style.scrollBehavior="auto"),e(0))})),Ot.slice(0).forEach((function(e){return e.refresh()})),Ot.forEach((function(e,t){if(e._subPinOffset&&e.pin){var n=e.vars.horizontal?"offsetWidth":"offsetHeight",r=e.pin[n];e.revert(!0,1),e.adjustPinSpacing(e.pin[n]-r),e.revert(!1,1)}})),Ot.forEach((function(e){return"max"===e.vars.end&&e.setPositions(e.start,Math.max(e.start+1,K(e.scroller,e._dir)))})),n.forEach((function(e){return e&&e.render&&e.render(-1)})),A.forEach((function(e){J(e)&&(e.smooth&&requestAnimationFrame((function(){return e.target.style.scrollBehavior="smooth"})),e.rec&&e(e.rec))})),we(tt,1),Ye.pause(),Xt++,Lt(2),Ot.forEach((function(e){return J(e.vars.onRefresh)&&e.vars.onRefresh(e)})),ot=jt.isRefreshing=!1,Yt("refresh")}else ce(jt,"scrollEnd",ye)},Bt=0,Ft=1,Lt=function(e){if(!ot||2===e){jt.isUpdating=!0,at&&at.update(0);var t=Ot.length,n=lt(),r=50<=n-ct,o=t&&Ot[0].scroll();if(Ft=o<Bt?-1:1,Bt=o,r&&(ut&&!Le&&200<n-ut&&(ut=0,Yt("scrollEnd")),ze=ct,ct=n),Ft<0){for(We=t;0<We--;)Ot[We]&&Ot[We].update(0,r);Ft=1}else for(We=0;We<t;We++)Ot[We]&&Ot[We].update(0,r);jt.isUpdating=!1}rt=0},Nt=["left","top",ht,pt,_t+xt,_t+mt,_t+bt,_t+yt,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],Wt=Nt.concat([gt,vt,"boxSizing","max"+St,"maxHeight","position",_t,wt,wt+bt,wt+mt,wt+xt,wt+yt]),Ht=/([A-Z])/g,qt=function(e){if(e){var t,n,r=e.t.style,o=e.length,i=0;for((e.t._gsap||Pe.core.getCache(e.t)).uncache=1;i<o;i+=2)n=e[i+1],t=e[i],n?r[t]=n:r[t]&&r.removeProperty(t.replace(Ht,"-$1").toLowerCase())}},Vt={left:0,top:0},Ut=/(webkit|moz|length|cssText|inset)/i,jt=(Gt.prototype.init=function(e,t){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),ft){var n,o,i,a,s,l,f,d,p,h,g,v,m,y,b,x,w,_,S,k,E,T,P,M,C,O,R,Y,I,X,F,L,N,W,V,U,Z,le,fe,he=(e=oe($(e)||Q(e)||e.nodeType?{trigger:e}:e,Pt)).onUpdate,ve=e.toggleClass,be=e.id,xe=e.onToggle,we=e.onRefresh,Me=e.scrub,Re=e.trigger,Ye=e.pin,Be=e.pinSpacing,Ne=e.invalidateOnRefresh,He=e.anticipatePin,qe=e.onScrubComplete,Ve=e.onSnapComplete,Ge=e.once,Ke=e.snap,Ze=e.pinReparent,$e=e.pinSpacer,Je=e.containerAnimation,Qe=e.fastScrollEnd,tt=e.preventOverlaps,rt=e.horizontal||e.containerAnimation&&!1!==e.horizontal?z:B,ct=!Me&&0!==Me,pt=c(e.scroller||Ce),ht=Pe.core.getCache(pt),Mt=j(pt),Ct="fixed"===("pinType"in e?e.pinType:r(pt,"pinType")||Mt&&"fixed"),Dt=[e.onEnter,e.onLeave,e.onEnterBack,e.onLeaveBack],Rt=ct&&e.toggleActions.split(" "),Yt="markers"in e?e.markers:Pt.markers,It=Mt?0:parseFloat(re(pt)["border"+rt.p2+St])||0,Bt=this,Lt=e.onRefreshInit&&function(){return e.onRefreshInit(Bt)},Nt=function(e,t,n){var o=n.d,i=n.d2,a=n.a;return(a=r(e,"getBoundingClientRect"))?function(){return a()[o]}:function(){return(t?Ce["inner"+i]:e["client"+i])||0}}(pt,Mt,rt),Wt=function(e,t){return!t||~D.indexOf(e)?G(e):function(){return Vt}}(pt,Mt),Ht=0,Ut=0,jt=u(pt,rt);if(et(Bt),Bt._dir=rt,He*=45,Bt.scroller=pt,Bt.scroll=Je?Je.time.bind(Je):jt,a=jt(),Bt.vars=e,t=t||e.animation,"refreshPriority"in e&&(Ue=1,-9999===e.refreshPriority&&(at=Bt)),ht.tweenScroll=ht.tweenScroll||{top:Te(pt,B),left:Te(pt,z)},Bt.tweenTo=n=ht.tweenScroll[rt.p],Bt.scrubDuration=function(e){(L=Q(e)&&e)?F?F.duration(e):F=Pe.to(t,{ease:"expo",totalProgress:"+=0.001",duration:L,paused:!0,onComplete:function(){return qe&&qe(Bt)}}):(F&&F.progress(1).kill(),F=0)},t&&(t.vars.lazy=!1,t._initted||!1!==t.vars.immediateRender&&!1!==e.immediateRender&&t.duration()&&t.render(0,!0,!0),Bt.animation=t.pause(),(t.scrollTrigger=Bt).scrubDuration(Me),I=0,be=be||t.vars.id),Ot.push(Bt),Ke&&(ee(Ke)&&!Ke.push||(Ke={snapTo:Ke}),"scrollBehavior"in De.style&&Pe.set(Mt?[De,Ae]:pt,{scrollBehavior:"auto"}),A.forEach((function(e){return J(e)&&e.target===(Mt?Oe.scrollingElement||Ae:pt)&&(e.smooth=!1)})),i=J(Ke.snapTo)?Ke.snapTo:"labels"===Ke.snapTo?function(e){return function(t){return Pe.utils.snap(ae(e),t)}}(t):"labelsDirectional"===Ke.snapTo?function(e){return function(t,n){return se(ae(e))(t,n.direction)}}(t):!1!==Ke.directional?function(e,t){return se(Ke.snapTo)(e,lt()-Ut<500?0:t.direction)}:Pe.utils.snap(Ke.snapTo),N=ee(N=Ke.duration||{min:.1,max:2})?Xe(N.min,N.max):Xe(N,N),W=Pe.delayedCall(Ke.delay||L/2||.1,(function(){var e=jt(),r=lt()-Ut<500,o=n.tween;if(!(r||Math.abs(Bt.getVelocity())<10)||o||Le||Ht===e)Bt.isActive&&Ht!==e&&W.restart(!0);else{var a=(e-l)/m,s=t&&!ct?t.totalProgress():a,c=r?0:(s-X)/(lt()-ze)*1e3||0,u=Pe.utils.clamp(-a,1-a,dt(c/2)*c/.185),d=a+(!1===Ke.inertia?0:u),p=Xe(0,1,i(d,Bt)),h=Math.round(l+p*m),g=Ke.onStart,v=Ke.onInterrupt,y=Ke.onComplete;if(e<=f&&l<=e&&h!==e){if(o&&!o._initted&&o.data<=dt(h-e))return;!1===Ke.inertia&&(u=p-a),n(h,{duration:N(dt(.185*Math.max(dt(d-s),dt(p-s))/c/.05||0)),ease:Ke.ease||"power3",data:dt(h-e),onInterrupt:function(){return W.restart(!0)&&v&&v(Bt)},onComplete:function(){Bt.update(),Ht=jt(),I=X=t&&!ct?t.totalProgress():Bt.progress,Ve&&Ve(Bt),y&&y(Bt)}},e,u*m,h-e-u*m),g&&g(Bt,n.tween)}}})).pause()),be&&(At[be]=Bt),fe=(fe=(Re=Bt.trigger=c(Re||Ye))&&Re._gsap&&Re._gsap.stRevert)&&fe(Bt),Ye=!0===Ye?Re:c(Ye),$(ve)&&(ve={targets:Re,className:ve}),Ye&&(!1===Be||Be===_t||(Be=!(!Be&&Ye.parentNode&&Ye.parentNode.style&&"flex"===re(Ye.parentNode).display)&&wt),Bt.pin=Ye,(o=Pe.core.getCache(Ye)).spacer?y=o.pinState:($e&&(($e=c($e))&&!$e.nodeType&&($e=$e.current||$e.nativeElement),o.spacerIsNative=!!$e,$e&&(o.spacerState=Se($e))),o.spacer=w=$e||Oe.createElement("div"),w.classList.add("pin-spacer"),be&&w.classList.add("pin-spacer-"+be),o.pinState=y=Se(Ye)),!1!==e.force3D&&Pe.set(Ye,{force3D:!0}),Bt.spacer=w=o.spacer,Y=re(Ye),P=Y[Be+rt.os2],S=Pe.getProperty(Ye),k=Pe.quickSetter(Ye,rt.a,kt),_e(Ye,w,Y),x=Se(Ye)),Yt){v=ee(Yt)?oe(Yt,Tt):Tt,h=pe("scroller-start",be,pt,rt,v,0),g=pe("scroller-end",be,pt,rt,v,0,h),_=h["offset"+rt.op.d2];var Kt=c(r(pt,"content")||pt);d=this.markerStart=pe("start",be,Kt,rt,v,_,0,Je),p=this.markerEnd=pe("end",be,Kt,rt,v,_,0,Je),Je&&(le=Pe.quickSetter([d,p],rt.a,kt)),Ct||D.length&&!0===r(pt,"fixedMarkers")||(function(e){var t=re(e).position;e.style.position="absolute"===t||"fixed"===t?t:"relative"}(Mt?De:pt),Pe.set([h,g],{force3D:!0}),C=Pe.quickSetter(h,rt.a,kt),R=Pe.quickSetter(g,rt.a,kt))}if(Je){var Zt=Je.vars.onUpdate,$t=Je.vars.onUpdateParams;Je.eventCallback("onUpdate",(function(){Bt.update(0,0,1),Zt&&Zt.apply($t||[])}))}Bt.previous=function(){return Ot[Ot.indexOf(Bt)-1]},Bt.next=function(){return Ot[Ot.indexOf(Bt)+1]},Bt.revert=function(e,n){if(!n)return Bt.kill(!0);var r=!1!==e||!Bt.enabled,o=Fe;r!==Bt.isReverted&&(r&&(U=Math.max(jt(),Bt.scroll.rec||0),V=Bt.progress,Z=t&&t.progress()),d&&[d,p,h,g].forEach((function(e){return e.style.display=r?"none":"block"})),r&&(Fe=1,Bt.update(r)),!Ye||Ze&&Bt.isActive||(r?function(e,t,n){qt(n);var r=e._gsap;if(r.spacerIsNative)qt(r.spacerState);else if(e._gsap.swappedIn){var o=t.parentNode;o&&(o.insertBefore(e,t),o.removeChild(t))}e._gsap.swappedIn=!1}(Ye,w,y):_e(Ye,w,re(Ye),M)),r||Bt.update(r),Fe=o,Bt.isReverted=r)},Bt.refresh=function(r,o){if(!Fe&&Bt.enabled||o)if(Ye&&r&&ut)ce(Gt,"scrollEnd",ye);else{!ot&&Lt&&Lt(Bt),Fe=1,Ut=lt(),n.tween&&(n.tween.kill(),n.tween=0),F&&F.pause(),Ne&&t&&t.revert({kill:!1}).invalidate(),Bt.isReverted||Bt.revert(!0,!0),Bt._subPinOffset=!1;for(var i,v,_,k,P,C,A,D,R,Y,I,X=Nt(),L=Wt(),N=Je?Je.duration():K(pt,rt),H=0,q=0,j=e.end,G=e.endTrigger||Re,ee=e.start||(0!==e.start&&Re?Ye?"0 0":"0 100%":0),te=Bt.pinnedContainer=e.pinnedContainer&&c(e.pinnedContainer),ne=Re&&Math.max(0,Ot.indexOf(Bt))||0,oe=ne;oe--;)(C=Ot[oe]).end||C.refresh(0,1)||(Fe=1),!(A=C.pin)||A!==Re&&A!==Ye||C.isReverted||((Y=Y||[]).unshift(C),C.revert(!0,!0)),C!==Ot[oe]&&(ne--,oe--);for(J(ee)&&(ee=ee(Bt)),l=ke(ee,Re,X,rt,jt(),d,h,Bt,L,It,Ct,N,Je)||(Ye?-.001:0),J(j)&&(j=j(Bt)),$(j)&&!j.indexOf("+=")&&(~j.indexOf(" ")?j=($(ee)?ee.split(" ")[0]:"")+j:(H=de(j.substr(2),X),j=$(ee)?ee:l+H,G=Re)),f=Math.max(l,ke(j||(G?"100% 0":N),G,X,rt,jt()+H,p,g,Bt,L,It,Ct,N,Je))||-.001,m=f-l||(l-=.01)&&.001,H=0,oe=ne;oe--;)(A=(C=Ot[oe]).pin)&&C.start-C._pinPush<=l&&!Je&&0<C.end&&(i=C.end-C.start,(A===Re&&C.start-C._pinPush<l||A===te)&&!Q(ee)&&(H+=i*(1-C.progress)),A===Ye&&(q+=i));if(l+=H,f+=H,Bt._pinPush=q,d&&H&&((i={})[rt.a]="+="+H,te&&(i[rt.p]="-="+jt()),Pe.set([d,p],i)),Ye)i=re(Ye),k=rt===B,_=jt(),E=parseFloat(S(rt.a))+q,!N&&1<f&&((I={style:I=(Mt?Oe.scrollingElement||Ae:pt).style,value:I["overflow"+rt.a.toUpperCase()]})["overflow"+rt.a.toUpperCase()]="scroll"),_e(Ye,w,i),x=Se(Ye),v=Et(Ye,!0),D=Ct&&u(pt,k?z:B)(),Be&&((M=[Be+rt.os2,m+q+kt]).t=w,(oe=Be===wt?ie(Ye,rt)+m+q:0)&&M.push(rt.d,oe+kt),qt(M),te&&Ot.forEach((function(e){e.pin===te&&!1!==e.vars.pinSpacing&&(e._subPinOffset=!0)})),Ct&&jt(U)),Ct&&((P={top:v.top+(k?_-l:D)+kt,left:v.left+(k?D:_-l)+kt,boxSizing:"border-box",position:"fixed"})[gt]=P.maxWidth=Math.ceil(v.width)+kt,P[vt]=P.maxHeight=Math.ceil(v.height)+kt,P[_t]=P[_t+bt]=P[_t+mt]=P[_t+xt]=P[_t+yt]="0",P[wt]=i[wt],P[wt+bt]=i[wt+bt],P[wt+mt]=i[wt+mt],P[wt+xt]=i[wt+xt],P[wt+yt]=i[wt+yt],b=function(e,t,n){for(var r,o=[],i=e.length,a=n?8:0;a<i;a+=2)r=e[a],o.push(r,r in t?t[r]:e[a+1]);return o.t=e.t,o}(y,P,Ze),ot&&jt(0)),t?(R=t._initted,je(1),t.render(t.duration(),!0,!0),T=S(rt.a)-E+m+q,O=1<Math.abs(m-T),Ct&&O&&b.splice(b.length-2,2),t.render(0,!0,!0),R||t.invalidate(!0),t.parent||t.totalTime(t.totalTime()),je(0)):T=m,I&&(I.value?I.style["overflow"+rt.a.toUpperCase()]=I.value:I.style.removeProperty("overflow-"+rt.a));else if(Re&&jt()&&!Je)for(v=Re.parentNode;v&&v!==De;)v._pinOffset&&(l-=v._pinOffset,f-=v._pinOffset),v=v.parentNode;Y&&Y.forEach((function(e){return e.revert(!1,!0)})),Bt.start=l,Bt.end=f,a=s=ot?U:jt(),Je||ot||(a<U&&jt(U),Bt.scroll.rec=0),Bt.revert(!1,!0),W&&(Ht=-1,Bt.isActive&&jt(l+m*V),W.restart(!0)),Fe=0,t&&ct&&(t._initted||Z)&&t.progress()!==Z&&t.progress(Z,!0).render(t.time(),!0,!0),V===Bt.progress&&!Je||(t&&!ct&&t.totalProgress(V,!0),Bt.progress=(a-l)/m===V?0:V),Ye&&Be&&(w._pinOffset=Math.round(Bt.progress*T)),we&&!ot&&we(Bt)}},Bt.getVelocity=function(){return(jt()-s)/(lt()-ze)*1e3||0},Bt.endAnimation=function(){te(Bt.callbackAnimation),t&&(F?F.progress(1):t.paused()?ct||te(t,Bt.direction<0,1):te(t,t.reversed()))},Bt.labelToScroll=function(e){return t&&t.labels&&(l||Bt.refresh()||l)+t.labels[e]/t.duration()*m||0},Bt.getTrailing=function(e){var t=Ot.indexOf(Bt),n=0<Bt.direction?Ot.slice(0,t).reverse():Ot.slice(t+1);return($(e)?n.filter((function(t){return t.vars.preventOverlaps===e})):n).filter((function(e){return 0<Bt.direction?e.end<=l:e.start>=f}))},Bt.update=function(e,r,o){if(!Je||o||e){var i,c,u,d,p,g,v,y=ot?U:Bt.scroll(),_=e?0:(y-l)/m,S=_<0?0:1<_?1:_||0,M=Bt.progress;if(r&&(s=a,a=Je?jt():y,Ke&&(X=I,I=t&&!ct?t.totalProgress():S)),He&&!S&&Ye&&!Fe&&!st&&ut&&l<y+(y-s)/(lt()-ze)*He&&(S=1e-4),S!==M&&Bt.enabled){if(d=(p=(i=Bt.isActive=!!S&&S<1)!=(!!M&&M<1))||!!S!=!!M,Bt.direction=M<S?1:-1,Bt.progress=S,d&&!Fe&&(c=S&&!M?0:1===S?1:1===M?2:3,ct&&(u=!p&&"none"!==Rt[c+1]&&Rt[c+1]||Rt[c],v=t&&("complete"===u||"reset"===u||u in t))),tt&&(p||v)&&(v||Me||!t)&&(J(tt)?tt(Bt):Bt.getTrailing(tt).forEach((function(e){return e.endAnimation()}))),ct||(!F||Fe||st?t&&t.totalProgress(S,!!Fe):(F._dp._time-F._start!==F._time&&F.render(F._dp._time-F._start),F.resetTo?F.resetTo("totalProgress",S,t._tTime/t._tDur):(F.vars.totalProgress=S,F.invalidate().restart()))),Ye)if(e&&Be&&(w.style[Be+rt.os2]=P),Ct){if(d){if(g=!e&&M<S&&y<f+1&&y+1>=K(pt,rt),Ze)if(e||!i&&!g)Ee(Ye,w);else{var A=Et(Ye,!0),D=y-l;Ee(Ye,De,A.top+(rt===B?D:0)+kt,A.left+(rt===B?0:D)+kt)}qt(i||g?b:x),O&&S<1&&i||k(E+(1!==S||g?0:T))}}else k(q(E+T*S));!Ke||n.tween||Fe||st||W.restart(!0),ve&&(p||Ge&&S&&(S<1||!nt))&&Ie(ve.targets).forEach((function(e){return e.classList[i||Ge?"add":"remove"](ve.className)})),!he||ct||e||he(Bt),d&&!Fe?(ct&&(v&&("complete"===u?t.pause().totalProgress(1):"reset"===u?t.restart(!0).pause():"restart"===u?t.restart(!0):t[u]()),he&&he(Bt)),!p&&nt||(xe&&p&&ne(Bt,xe),Dt[c]&&ne(Bt,Dt[c]),Ge&&(1===S?Bt.kill(!1,1):Dt[c]=0),p||Dt[c=1===S?1:3]&&ne(Bt,Dt[c])),Qe&&!i&&Math.abs(Bt.getVelocity())>(Q(Qe)?Qe:2500)&&(te(Bt.callbackAnimation),F?F.progress(1):te(t,"reverse"===u?1:!S,1))):ct&&he&&!Fe&&he(Bt)}if(R){var Y=Je?y/Je.duration()*(Je._caScrollDist||0):y;C(Y+(h._isFlipped?1:0)),R(Y)}le&&le(-y/Je.duration()*(Je._caScrollDist||0))}},Bt.enable=function(e,t){Bt.enabled||(Bt.enabled=!0,ce(pt,"resize",me),ce(Mt?Oe:pt,"scroll",ge),Lt&&ce(Gt,"refreshInit",Lt),!1!==e&&(Bt.progress=V=0,a=s=Ht=jt()),!1!==t&&Bt.refresh())},Bt.getTween=function(e){return e&&n?n.tween:F},Bt.setPositions=function(e,t){Ye&&(E+=e-l,T+=t-e-m,Be===wt&&Bt.adjustPinSpacing(t-e-m)),Bt.start=l=e,Bt.end=f=t,m=t-e,Bt.update()},Bt.adjustPinSpacing=function(e){if(M){var t=M.indexOf(rt.d)+1;M[t]=parseFloat(M[t])+e+kt,M[1]=parseFloat(M[1])+e+kt,qt(M)}},Bt.disable=function(e,t){if(Bt.enabled&&(!1!==e&&Bt.revert(!0,!0),Bt.enabled=Bt.isActive=!1,t||F&&F.pause(),U=0,o&&(o.uncache=1),Lt&&ue(Gt,"refreshInit",Lt),W&&(W.pause(),n.tween&&n.tween.kill()&&(n.tween=0)),!Mt)){for(var r=Ot.length;r--;)if(Ot[r].scroller===pt&&Ot[r]!==Bt)return;ue(pt,"resize",me),ue(pt,"scroll",ge)}},Bt.kill=function(n,r){Bt.disable(n,r),F&&!r&&F.kill(),be&&delete At[be];var i=Ot.indexOf(Bt);0<=i&&Ot.splice(i,1),i===We&&0<Ft&&We--,i=0,Ot.forEach((function(e){return e.scroller===Bt.scroller&&(i=1)})),i||ot||(Bt.scroll.rec=0),t&&(t.scrollTrigger=null,n&&t.revert({kill:!1}),r||t.kill()),d&&[d,p,h,g].forEach((function(e){return e.parentNode&&e.parentNode.removeChild(e)})),at===Bt&&(at=0),Ye&&(o&&(o.uncache=1),i=0,Ot.forEach((function(e){return e.pin===Ye&&i++})),i||(o.spacer=0)),e.onKill&&e.onKill(Bt)},Bt.enable(!1,!1),fe&&fe(Bt),t&&t.add&&!m?Pe.delayedCall(.01,(function(){return l||f||Bt.refresh()}))&&(m=.01)&&(l=f=0):Bt.refresh(),Ye&&function(){if(it!==Xt){var e=it=Xt;requestAnimationFrame((function(){return e===Xt&&zt(!0)}))}}()}else this.update=this.refresh=this.kill=H},Gt.register=function(e){return Me||(Pe=e||U(),V()&&window.document&&Gt.enable(),Me=ft),Me},Gt.defaults=function(e){if(e)for(var t in e)Pt[t]=e[t];return Pt},Gt.disable=function(e,t){ft=0,Ot.forEach((function(n){return n[t?"kill":"disable"](e)})),ue(Ce,"wheel",ge),ue(Oe,"scroll",ge),clearInterval(Be),ue(Oe,"touchcancel",H),ue(De,"touchstart",H),le(ue,Oe,"pointerdown,touchstart,mousedown",N),le(ue,Oe,"pointerup,touchend,mouseup",W),Ye.kill(),Z(ue);for(var n=0;n<A.length;n+=3)fe(ue,A[n],A[n+1]),fe(ue,A[n],A[n+2])},Gt.enable=function(){if(Ce=window,Oe=document,Ae=Oe.documentElement,De=Oe.body,Pe&&(Ie=Pe.utils.toArray,Xe=Pe.utils.clamp,et=Pe.core.context||H,je=Pe.core.suppressOverwrites||H,tt=Ce.history.scrollRestoration||"auto",Pe.core.globals("ScrollTrigger",Gt),De)){ft=1,F.register(Pe),Gt.isTouch=F.isTouch,Qe=F.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),ce(Ce,"wheel",ge),Re=[Ce,Oe,Ae,De],Pe.matchMedia?(Gt.matchMedia=function(e){var t,n=Pe.matchMedia();for(t in e)n.add(t,e[t]);return n},Pe.addEventListener("matchMediaInit",(function(){return xe()})),Pe.addEventListener("matchMediaRevert",(function(){return be()})),Pe.addEventListener("matchMedia",(function(){zt(0,1),Yt("matchMedia")})),Pe.matchMedia("(orientation: portrait)",(function(){return ve(),ve}))):console.warn("Requires GSAP 3.11.0 or later"),ve(),ce(Oe,"scroll",ge);var e,t,n=De.style,r=n.borderTopStyle,o=Pe.core.Animation.prototype;for(o.revert||Object.defineProperty(o,"revert",{value:function(){return this.time(-.01,!0)}}),n.borderTopStyle="solid",e=Et(De),B.m=Math.round(e.top+B.sc())||0,z.m=Math.round(e.left+z.sc())||0,r?n.borderTopStyle=r:n.removeProperty("border-top-style"),Be=setInterval(he,250),Pe.delayedCall(.5,(function(){return st=0})),ce(Oe,"touchcancel",H),ce(De,"touchstart",H),le(ce,Oe,"pointerdown,touchstart,mousedown",N),le(ce,Oe,"pointerup,touchend,mouseup",W),Ne=Pe.utils.checkPrefix("transform"),Wt.push(Ne),Me=lt(),Ye=Pe.delayedCall(.2,zt).pause(),Ve=[Oe,"visibilitychange",function(){var e=Ce.innerWidth,t=Ce.innerHeight;Oe.hidden?(He=e,qe=t):He===e&&qe===t||me()},Oe,"DOMContentLoaded",zt,Ce,"load",zt,Ce,"resize",me],Z(ce),Ot.forEach((function(e){return e.enable(0,1)})),t=0;t<A.length;t+=3)fe(ue,A[t],A[t+1]),fe(ue,A[t],A[t+2])}},Gt.config=function(e){"limitCallbacks"in e&&(nt=!!e.limitCallbacks);var t=e.syncInterval;t&&clearInterval(Be)||(Be=t)&&setInterval(he,t),"ignoreMobileResize"in e&&(Ze=1===Gt.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(Z(ue)||Z(ce,e.autoRefreshEvents||"none"),Ge=-1===(e.autoRefreshEvents+"").indexOf("resize"))},Gt.scrollerProxy=function(e,t){var n=c(e),r=A.indexOf(n),o=j(n);~r&&A.splice(r,o?6:2),t&&(o?D.unshift(Ce,t,De,t,Ae,t):D.unshift(n,t))},Gt.clearMatchMedia=function(e){Ot.forEach((function(t){return t._ctx&&t._ctx.query===e&&t._ctx.kill(!0,!0)}))},Gt.isInViewport=function(e,t,n){var r=($(e)?c(e):e).getBoundingClientRect(),o=r[n?gt:vt]*t||0;return n?0<r.right-o&&r.left+o<Ce.innerWidth:0<r.bottom-o&&r.top+o<Ce.innerHeight},Gt.positionInViewport=function(e,t,n){$(e)&&(e=c(e));var r=e.getBoundingClientRect(),o=r[n?gt:vt],i=null==t?o/2:t in Mt?Mt[t]*o:~t.indexOf("%")?parseFloat(t)*o/100:parseFloat(t)||0;return n?(r.left+i)/Ce.innerWidth:(r.top+i)/Ce.innerHeight},Gt.killAll=function(e){if(Ot.slice(0).forEach((function(e){return"ScrollSmoother"!==e.vars.id&&e.kill()})),!0!==e){var t=Dt.killAll||[];Dt={},t.forEach((function(e){return e()}))}},Gt);function Gt(e,t){Me||Gt.register(Pe)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),this.init(e,t)}function Kt(e,t,n,r){return r<t?e(r):t<0&&e(0),r<n?(r-t)/(n-t):n<0?t/(t-n):1}function Zt(e,t){!0===t?e.style.removeProperty("touch-action"):e.style.touchAction=!0===t?"auto":t?"pan-"+t+(F.isTouch?" pinch-zoom":""):"none",e===Ae&&Zt(De,t)}function $t(e){var t,n=e.event,r=e.target,o=e.axis,i=(n.changedTouches?n.changedTouches[0]:n).target,a=i._gsap||Pe.core.getCache(i),s=lt();if(!a._isScrollT||2e3<s-a._isScrollT){for(;i&&i!==De&&(i.scrollHeight<=i.clientHeight&&i.scrollWidth<=i.clientWidth||!en[(t=re(i)).overflowY]&&!en[t.overflowX]);)i=i.parentNode;a._isScroll=i&&i!==r&&!j(i)&&(en[(t=re(i)).overflowY]||en[t.overflowX]),a._isScrollT=s}!a._isScroll&&"x"!==o||(n.stopPropagation(),n._gsapAllow=!0)}function Jt(e,t,n,r){return F.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:r=r&&$t,onPress:r,onDrag:r,onScroll:r,onEnable:function(){return n&&ce(Oe,F.eventTypes[0],nn,!1,!0)},onDisable:function(){return ue(Oe,F.eventTypes[0],nn,!0)}})}jt.version="3.11.4",jt.saveStyles=function(e){return e?Ie(e).forEach((function(e){if(e&&e.style){var t=It.indexOf(e);0<=t&&It.splice(t,5),It.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),Pe.core.getCache(e),et())}})):It},jt.revert=function(e,t){return xe(!e,t)},jt.create=function(e,t){return new jt(e,t)},jt.refresh=function(e){return e?me():(Me||jt.register())&&zt(!0)},jt.update=function(e){return++A.cache&&Lt(!0===e?2:0)},jt.clearScrollMemory=we,jt.maxScroll=function(e,t){return K(e,t?z:B)},jt.getScrollFunc=function(e,t){return u(c(e),t?z:B)},jt.getById=function(e){return At[e]},jt.getAll=function(){return Ot.filter((function(e){return"ScrollSmoother"!==e.vars.id}))},jt.isScrolling=function(){return!!ut},jt.snapDirectional=se,jt.addEventListener=function(e,t){var n=Dt[e]||(Dt[e]=[]);~n.indexOf(t)||n.push(t)},jt.removeEventListener=function(e,t){var n=Dt[e],r=n&&n.indexOf(t);0<=r&&n.splice(r,1)},jt.batch=function(e,t){function n(e,t){var n=[],r=[],o=Pe.delayedCall(a,(function(){t(n,r),n=[],r=[]})).pause();return function(e){n.length||o.restart(!0),n.push(e.trigger),r.push(e),s<=n.length&&o.progress(1)}}var r,o=[],i={},a=t.interval||.016,s=t.batchMax||1e9;for(r in t)i[r]="on"===r.substr(0,2)&&J(t[r])&&"onRefreshInit"!==r?n(0,t[r]):t[r];return J(s)&&(s=s(),ce(jt,"refresh",(function(){return s=t.batchMax()}))),Ie(e).forEach((function(e){var t={};for(r in i)t[r]=i[r];t.trigger=e,o.push(jt.create(t))})),o};var Qt,en={auto:1,scroll:1},tn=/(input|label|select|textarea)/i,nn=function(e){var t=tn.test(e.target.tagName);(t||Qt)&&(e._gsapAllow=!0,Qt=t)};jt.sort=function(e){return Ot.sort(e||function(e,t){return-1e6*(e.vars.refreshPriority||0)+e.start-(t.start+-1e6*(t.vars.refreshPriority||0))})},jt.observe=function(e){return new F(e)},jt.normalizeScroll=function(e){if(void 0===e)return Ke;if(!0===e&&Ke)return Ke.enable();if(!1===e)return Ke&&Ke.kill();var t=e instanceof F?e:function(e){function t(){return l=!1}function n(){a=K(y,B),O=Xe(Qe?1:0,a),g&&(C=Xe(0,K(y,z))),s=Xt}function r(){w._gsap.y=q(parseFloat(w._gsap.y)+_.offset)+"px",w.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(w._gsap.y)+", 0, 1)",_.offset=_.cacheID=0}function o(){n(),f.isActive()&&f.vars.scrollY>a&&(_()>a?f.progress(1)&&_(a):f.resetTo("scrollY",a))}ee(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var i,a,s,l,f,d,p,h,g=e.normalizeScrollX,v=e.momentum,m=e.allowNestedScroll,y=c(e.target)||Ae,b=Pe.core.globals().ScrollSmoother,x=b&&b.get(),w=Qe&&(e.content&&c(e.content)||x&&!1!==e.content&&!x.smooth()&&x.content()),_=u(y,B),S=u(y,z),k=1,E=(F.isTouch&&Ce.visualViewport?Ce.visualViewport.scale*Ce.visualViewport.width:Ce.outerWidth)/Ce.innerWidth,T=0,P=J(v)?function(){return v(i)}:function(){return v||2.8},M=Jt(y,e.type,!0,m),C=H,O=H;return w&&Pe.set(w,{y:"+=0"}),e.ignoreCheck=function(e){return Qe&&"touchmove"===e.type&&function(){if(l){requestAnimationFrame(t);var e=q(i.deltaY/2),n=O(_.v-e);if(w&&n!==_.v+_.offset){_.offset=n-_.v;var o=q((parseFloat(w&&w._gsap.y)||0)-_.offset);w.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+o+", 0, 1)",w._gsap.y=o+"px",_.cacheID=A.cache,Lt()}return!0}_.offset&&r(),l=!0}()||1.05<k&&"touchstart"!==e.type||i.isGesturing||e.touches&&1<e.touches.length},e.onPress=function(){var e=k;k=q((Ce.visualViewport&&Ce.visualViewport.scale||1)/E),f.pause(),e!==k&&Zt(y,1.01<k||!g&&"x"),d=S(),p=_(),n(),s=Xt},e.onRelease=e.onGestureStart=function(e,t){if(_.offset&&r(),t){A.cache++;var n,i,s=P();g&&(i=(n=S())+.05*s*-e.velocityX/.227,s*=Kt(S,n,i,K(y,z)),f.vars.scrollX=C(i)),i=(n=_())+.05*s*-e.velocityY/.227,s*=Kt(_,n,i,K(y,B)),f.vars.scrollY=O(i),f.invalidate().duration(s).play(.01),(Qe&&f.vars.scrollY>=a||a-1<=n)&&Pe.to({},{onUpdate:o,duration:s})}else h.restart(!0)},e.onWheel=function(){f._ts&&f.pause(),1e3<lt()-T&&(s=0,T=lt())},e.onChange=function(e,t,o,i,a){if(Xt!==s&&n(),t&&g&&S(C(i[2]===t?d+(e.startX-e.x):S()+t-i[1])),o){_.offset&&r();var l=a[2]===o,c=l?p+e.startY-e.y:_()+o-a[1],u=O(c);l&&c!==u&&(p+=u-c),_(u)}(o||t)&&Lt()},e.onEnable=function(){Zt(y,!g&&"x"),jt.addEventListener("refresh",o),ce(Ce,"resize",o),_.smooth&&(_.target.style.scrollBehavior="auto",_.smooth=S.smooth=!1),M.enable()},e.onDisable=function(){Zt(y,!0),ue(Ce,"resize",o),jt.removeEventListener("refresh",o),M.kill()},e.lockAxis=!1!==e.lockAxis,((i=new F(e)).iOS=Qe)&&!_()&&_(1),Qe&&Pe.ticker.add(H),h=i._dc,f=Pe.to(i,{ease:"power4",paused:!0,scrollX:g?"+=0.1":"+=0",scrollY:"+=0.1",onComplete:h.vars.onComplete}),i}(e);return Ke&&Ke.target===t.target&&Ke.kill(),j(t.target)&&(Ke=t),t},jt.core={_getVelocityProp:f,_inputObserver:Jt,_scrollers:A,_proxies:D,bridge:{ss:function(){ut||Yt("scrollStart"),ut=lt()},ref:function(){return Fe}}},U()&&Pe.registerPlugin(jt),e.ScrollTrigger=jt,e.default=jt,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default}));