!function(){if($sel(".uh-oh"))return;let e=2,t=!1;const r=$field("list_type").value,o=()=>{if(t)return;const l=$sel(".load-more-items");let n=getApi("blg/");"category"===r&&(n+="category/"+$end($lay.page.routeArray)+"/"),"author"===r&&(n+="author/"+$end($lay.page.routeArray)+"/"),"search"===r&&(n+="search?query="+$get("query",!0)),$view(l).inView&&$win.requestAnimationFrame((()=>{$curl(n+e,{type:"text",strict:!1,debounce:1e3}).catch((e=>{$on($win,"scroll",o,"del"),osNote(e.statusText.message,"fail")})).then((r=>{if(""===r)return t=!0,l.$class("add","d-none"),$on($win,"scroll",o,"del");l.$html("beforebegin",r),e++}))}))};$on($doc,"scroll",o)}();