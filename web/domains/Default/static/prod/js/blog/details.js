$curl(getApi("blg/related/"+$field("post_id").value)).then((t=>{if(0===t.length)return;let l="";$loop(t,(t=>{const e="blog/"+t.slug;l+=`<li>\n                        <div class="post-thumb">\n                            <a href="${e}"> <img src="${t.thumbnail}" alt="${t.title}"></a>\n                        </div>\n                        <div class="post-content">\n                            <h6 class="post-title">\n                                <a href="${e}">${t.title}</a>\n                            </h6>\n                            <div class="blog-meta">\n                                <ul>\n                                    <li>${t.date}</li>\n                                </ul>\n                            </div>\n                        </div>\n                    </li>`})),$sel(".tj-recent-posts").$class("del","d-none"),$sel(".related-post-container").$html(l)})),setTimeout((()=>{$curl(getApi("blg/view/"),{data:{post_id:$field("post_id").value},method:"PUT"}).catch((()=>console.log("Error counting view")))}),1e4);