class MultiStepForm{constructor(){this.currentStep=1,this.totalSteps=$sela(".step").length,this.formData={},this.storeKey="formAppData",this.storeStepKey="formAppStep",this.init(),this.loadSavedData()}init(){this.storeKey+="-"+$field("form_type").value,this.storeStepKey+="-"+$field("form_type").value,this.bindEvents(),this.updateProgress(),this.updateButtons(),this.scrollToCurrentStep(),this.addNewElements(),this.enableMediaPreviewer()}bindEvents(){$id("nextBtn").$on("click",(()=>this.nextStep())),$id("prevBtn").$on("click",(()=>this.prevStep())),$id("submitBtn").$on("click",((e,t)=>this.submitForm(e,t))),this.enableAutoSave(),$sela(".step, .step-label").forEach((e=>{e.addEventListener("click",(e=>{this.goToStep(e.target.dataset.step)}))})),this.autoFocusFirstInput(),$sel(".new-application").$on("click",(e=>{e.preventDefault(),cMsg("Are you sure you want to start a new application?",(()=>{this.clearSavedData(),$loc.reload()}))}))}enableAutoSave(){$sela("input, select, textarea").forEach((e=>{e.$on("input",(()=>$debounce((()=>this.saveData()),500)),"on")}))}enableDelete(){$sela(".delete-btn").$loop((e=>{e.$on("click",((e,t)=>{if(e.preventDefault(),"1"===t.dataset.execute)return t.closest("details").remove(),void this.saveData();t.dataset.execute="1",osNote("Click again to delete entry","info",{onClose:()=>t.dataset.execute="0"})}),"on")}))}nextStep(){this.currentStep<this.totalSteps&&(this.currentStep++,this.showStep(),this.updateProgress(),this.updateButtons(),this.scrollToCurrentStep(),this.autoFocusFirstInput(),this.currentStep===this.totalSteps&&this.updateReview())}prevStep(){this.currentStep>1&&(this.currentStep--,this.showStep(),this.updateProgress(),this.updateButtons(),this.scrollToCurrentStep(),this.autoFocusFirstInput())}goToStep(e){this.currentStep=parseInt(e),this.showStep(),this.updateProgress(),this.updateButtons(),this.scrollToCurrentStep(),this.autoFocusFirstInput(),this.currentStep===this.totalSteps&&this.updateReview()}showStep(){document.querySelectorAll(".form-step").forEach((e=>{e.classList.remove("active")})),document.getElementById(`step${this.currentStep}`).classList.add("active"),document.querySelectorAll(".step").forEach(((e,t)=>{e.classList.remove("active","completed"),t+1===this.currentStep?e.classList.add("active"):t+1<this.currentStep&&e.classList.add("completed")}))}updateProgress(){const e=this.currentStep/this.totalSteps*100;document.getElementById("progressFill").style.width=`${e}%`}updateButtons(){const e=$id("prevBtn"),t=$id("nextBtn"),s=$id("submitBtn");if(e.style.display=1===this.currentStep?"none":"block",this.currentStep===this.totalSteps)return t.style.display="none",void(s.style.display="block");t.innerText=this.currentStep===this.totalSteps-1?"Preview Form":"Next",t.style.display="block",s.style.display="none"}updateReview(){let e="";$sela(".step-label").$loop(((t,s)=>{if("Preview"===t.innerText)return"continue";const i=$sela(".form-step")[s];let n="";i.$sela(".entry-label").$loop((e=>{const t=e.nextElementSibling??e.parentElement.nextElementSibling;let s=t.value,i=e.innerText;if("HTMLSelectElement"===$type(t)&&(s=t.options[t.selectedIndex].innerText),"radio"===t.type){if(!t.checked)return"continue";i=e.dataset.label,s=t.previousElementSibling.innerText}"file"===t.type&&(t.files.length>1?s=t.files.length+" File(s) Added":t.nextElementSibling&&(s="none"!==t.nextElementSibling.$sel("img").style.display?`<img src="${t.nextElementSibling.$sel("img").src}" loading="lazy" width="200px" height="200px" style="object-fit: contain">`:`<iframe src="${t.nextElementSibling.$sel("iframe").src}" width="200px" height="200px" style="border-style: none"></iframe>`)),n+=`<p><strong>${i}:</strong> ${s}</p>`})),e+=`<div class="review-item">\n                    <h3>${t.innerText}</h3>\n                    <div>${n}</div>\n                </div>`})),$sel(".review-section").$html(e)}scrollToCurrentStep(){const e=document.querySelector(".step-indicators"),t=document.querySelector(`.step[data-step="${this.currentStep}"]`).parentElement;if(e&&t){const s=e.offsetWidth,i=t.offsetLeft-s/2+t.offsetWidth/2;e.scrollTo({left:Math.max(0,i),behavior:"smooth"})}}addNewElements(){$sela(".add-new-ele").$loop((e=>{e.$on("click",((e,t)=>{e.preventDefault();const s=t.nextElementSibling,i=JSON.parse(s.$html()),n=s.dataset.title,l=s.id;let a=$sela("."+l+"-dyna-entry").length+1;const o=parseInt(s.dataset.maxEntry);if(0!==o){if(o===a-1)return osNote("Maximum entries for current field has been created","warn");a=""}let r="";$loop(i,((e,t)=>r+=this.inputElement(t,e))),t.closest(".auto-add-container").$html("beforeend",`<details class="${l}-dyna-entry" open>\n                        <summary><div><b>></b> ${n} ${a}</div> <button type="button" class="btn delete-btn" data-execute="0" title="Delete ${n}">🗑️</button></summary>\n                        <div class="form-grid dyna-add-gp">${r}</div>\n                    </details>`),this.enableDelete(),this.enableAutoSave(),this.enableMediaPreviewer()}))}))}enableMediaPreviewer(){$sela(".preview-file-input").$loop((e=>{if(e.nextElementSibling){const t=e.nextElementSibling.$sel("img"),s=e.nextElementSibling.$sel("iframe");$mediaPreview(e,t,{operation:(e,i)=>{if(s.src=i,e.type.startsWith("image/"))return t.style.display="block",void(s.style.display="none");t.style.display="none",s.style.display="block"}})}}))}inputElement(e,t={}){const s=t.type??"text",i=t.required??!1,n=i?"required":"",l=i?"*":"",a=t.class??"",o=t.help_text??null,r=t.accept?`accept="${t.accept}"`:null,p=t.max_size?`data-max-size="${t.max_size}"`:null,c=t.multiple??null;let d=t.style??"",u=`${e}-id`;const h=t.label??e,$=t.placeholder??`Enter ${h}`;let m=null;"file"===s&&(m="class='preview-file-input'");let y=`<input ${m} type="${s}" id="${u}" name="${e}" placeholder="${$}" ${n} ${r} ${p}>`,S="";if(t.options&&t.options.forEach(((t,i)=>{const l=t.selected??!1;if("select"!==s){if("radio"===s||"checkbox"===s){const a=`${u}-${s}-${i}`;S+=`\n                      <label for="${a}" class="radio-label">\n                        <span class="entry-label" data-label="${h}">${t.name}</span>\n                        <input type="${s}" id="${a}" name="${e}" ${n} ${l?"checked":""}>\n                      </label>\n                    `}}else S+=`<option ${l?"selected":""} value="${t.id}">${t.name}</option>`})),"select"===s&&(y=`<select name='${e}' id='${u}' ${n}>${S}</select>`),"radio"!==s&&"checkbox"!==s||(y=`<div style="margin-top: 10px">${S}</div>`,u=""),"file"===s&&!c){const e=$lay.src.static_img+"placeholder.svg";y+=`<div style="text-align: center; margin: 10px auto">\n                <img src="${e}" width="200px" height="200px" style="object-fit: contain" alt="placeholder">\n                <iframe width='200px' height='200px' style='border-style: none; display: none' src="${e}"></iframe>\n            </div>`}"textarea"===s&&(y=`<textarea id="${u}" name="${e}" placeholder="${$}" ${n}></textarea>`);const v=o?`<div class="tooltip-wrapper" id="myTooltip">?<div class="tooltip-text">${o}</div></div>`:"";let f=`<div>${v} <label class="entry-label" for="${u}">${h} ${l}</label></div>`;return""===u&&(f=`<div>${v} ${h} ${l}</div>`),`<div class="form-group ${a}" style="${d}">\n              ${f}\n              ${y}\n            </div>`}clearSavedData(){$store.removeItem(this.storeKey),$store.removeItem(this.storeStepKey)}saveData(){$store.setItem(this.storeKey,JSON.stringify($getForm($sel("form"),!1).object)),$store.setItem(this.storeStepKey,this.currentStep.toString())}loadSavedData(){const e=$store.getItem(this.storeKey),t=$store.getItem(this.storeStepKey),s=(e,t,s=0)=>{let i=$name(t)[s];i||($sel(`[data-dyna-field=${t}]`)&&$sel(`[data-dyna-field=${t}]`).click(),i=$name(t.replaceAll("\\[\\]","[]"))[s]),i.value=e,"checkbox"!==i.type&&"radio"!==i.type||i.value!==e||(i.checked=!0),"SELECT"!==i.tagName&&"checkbox"!==i.type&&"radio"!==i.type||i.dispatchEvent(new Event("change"))};e&&$loop(JSON.parse(e),((e,t)=>{if("Array"===$type(e))return $loop(e,((e,i)=>s(e,t+"\\[\\]",i))),"continue";s(e,t)})),t&&parseInt(t)>1&&confirm("Would you like to continue from where you left off?")&&(this.currentStep=parseInt(t),this.showStep(),this.updateProgress(),this.updateButtons(),this.scrollToCurrentStep())}autoFocusFirstInput(){setTimeout((()=>{const e=document.getElementById(`step${this.currentStep}`).querySelector("input, select, textarea");e&&!window.matchMedia("(max-width: 768px)").matches&&e.focus()}),100)}async submitForm(e,t){e.preventDefault();const s=t.textContent;$curl(getApi("form-app/"+$end($lay.page.routeArray)),{data:t,preload:()=>{t.textContent="Submitting...",t.disabled=!0,$preloader()}}).finally((()=>{t.textContent=s,t.disabled=!1,$preloader(!1)})).then((e=>{this.clearSavedData(),this.showSuccessMessage(e.data.ref_num)}))}showSuccessMessage(e){$id("visaForm").$html(`<div class="success-message">\n                <h2>Application Submitted Successfully!</h2>\n                <p>\n                    Your form has been submitted. You will receive a confirmation email shortly with your application \n                    reference number and be contacted to start the process.\n                </p>\n                <p><strong>Reference Number:</strong> ${e}</p>\n                <button type="button" class="btn btn-primary" onclick="$loc.reload()">Submit New Application</button>\n            </div>`)}}document.addEventListener("DOMContentLoaded",(()=>{new MultiStepForm})),window.addEventListener("beforeunload",(()=>{window.multiStepForm&&window.multiStepForm.saveData()}));