<?php

use <PERSON><PERSON><PERSON>er\Lay\Core\View\DomainResource;
use BrickLayer\Lay\Core\View\Tags\Img;
use BrickLayer\Lay\Libs\LayDate;
use Bricks\Elevator\Blog\Controller\BlogController;
use Bricks\Elevator\Blog\Controller\CustomMarkdownMap;

DomainResource::include_file("__partials/breadcrumb.inc");

/**
 * @var \Bricks\Elevator\Blog\Resource\BlogResource $data
 */
$data = DomainResource::plaster()->local->data;

$navigate = BlogController::new()->model()->post_prev_next($data->postId);
?>

<input type="hidden" name="post_id" value="<?= $data->postId ?>">

<section class="tj-blog-section section-gap">
    <div class="container">
        <div class="row row-gap-5">
            <div class="col-lg-8">
                <div class="post-details-wrapper">
                    <div class="blog-images wow fadeInUp" data-wow-delay=".1s">
                        <?= Img::new()->alt($data->title)->style('height: 100%')->ratio($data->photo['ratio']['width'], $data->photo['ratio']['height'])->src($data->photo['hd']) ?>
                    </div>
                    <h2 class="title title-anim"><?= $data->title ?></h2>
                    <div class="blog-category-two wow fadeInUp" data-wow-delay=".3s">
                        <div class="category-item">
                            <div class="cate-images">
                                <?= Img::new()->alt($data->author['name'])->ratio("50px", "50px")->src($data->author['dp']) ?>
                            </div>
                            <div class="cate-text">
                                <span class="degination">Authored by</span>
                                <h6 class="title"><a href="blog/author/<?= $data->author['slug'] ?>"><?= $data->author['name'] ?></a></h6>
                            </div>
                        </div>
                        <div class="category-item">
                            <div class="cate-icons">
                                <i class="tji-calendar"></i>
                            </div>
                            <div class="cate-text">
                                <span class="degination">Date Released</span>
                                <h6 class="text"><?= LayDate::date($data->dateCreated, "dS M, Y T") ?></h6>
                            </div>
                        </div>

                        <?php if($data->dateUpdated) : ?>
                            <div class="category-item">
                                <div class="cate-icons">
                                    <i class="tji-clock"></i>
                                </div>
                                <div class="cate-text">
                                    <span class="degination">Last Updated</span>
                                    <h6 class="text"><?= LayDate::date($data->dateUpdated, "dS M, Y T") ?></h6>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="blog-text"><?= (new CustomMarkdownMap)->render($data->postContent) ?></div>

                        <div class="tj-tags-post wow fadeInUp" data-wow-delay=".3s">
                            <?php if($data->tag['id']) : ?>
                                <div class="tagcloud">
                                    <span>Tags:</span>
                                    <?php foreach ($data->tag['values'] as $tag) : ?>
                                        <a href="javascript:void(0)"><?= $tag ?></a>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
<!--                            <div class="post-share">-->
<!--                                <ul>-->
<!--                                    <li> Share:</li>-->
<!--                                    <li><a href="https://www.facebook.com/" target="_blank"><i class="fa-brands fa-facebook-f"></i></a>-->
<!--                                    </li>-->
<!--                                    <li><a href="https://x.com/" target="_blank"><i class="fa-brands fa-x-twitter"></i></a></li>-->
<!--                                    <li><a href="https://www.instagram.com/" target="_blank"><i class="fa-brands fa-instagram"></i></a>-->
<!--                                    </li>-->
<!--                                    <li><a href="https://www.linkedin.com/" target="_blank"><i class="fa-brands fa-linkedin-in"></i></a>-->
<!--                                    </li>-->
<!--                                </ul>-->
<!--                            </div>-->
                        </div>

                    <div class="tj-post__navigation wow fadeInUp" data-wow-delay=".3s">
                        <!-- previous post -->
                        <?php if($navigate['prev']) : ?>
                            <div class="tj-nav__post previous">
                                <div class="tj-nav-post__nav prev_post">
                                    <a title="<?= $navigate['prev']['title'] ?>" href="blog/<?= $navigate['prev']['slug'] ?>"><span><i class="tji-arrow-left"></i></span>Previous</a>
                                </div>
                            </div>
                        <?php endif ?>

                        <div class="tj-nav-post__grid">
                            <a href="blog/"><i class="tji-window"></i></a>
                        </div>

                        <?php if($navigate['next']) : ?>
                            <div class="tj-nav__post next">
                                <div class="tj-nav-post__nav next_post">
                                    <a title="<?= $navigate['next']['title'] ?>" href="blog/<?= $navigate['next']['slug'] ?>">Next<span><i class="tji-arrow-right"></i></span></a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                </div>
            </div>
            <div class="col-lg-4">
                <div class="tj-main-sidebar sticky-lg-top">
                    <div class="tj-sidebar-widget widget-categories wow fadeInUp" data-wow-delay=".1s">
                        <h4 class="widget-title">Table of Content</h4>
                        <ul>
                            <?php foreach ($data->toc as $toc) : ?>
                                <li><a href="<?= DomainResource::plaster()->page->url . "#" . $toc['slug'] ?>"><?= $toc['text'] ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <div class="tj-sidebar-widget tj-recent-posts wow fadeInUp d-none" data-wow-delay=".3s">
                        <h4 class="widget-title">Related post</h4>
                        <ul class="related-post-container"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
