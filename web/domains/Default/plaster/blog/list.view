<?php

use Brick<PERSON>ayer\Lay\Core\View\DomainResource;

DomainResource::include_file("__partials/breadcrumb.inc");

/**
 * @var \Bricks\Elevator\FoundationUtil $foundation
 */
$foundation = DomainResource::plaster()->local->foundation;

/**
 * @var array<int, \Bricks\Elevator\Blog\Resource\BlogResource> $posts
 */
$posts = DomainResource::plaster()->local->posts;

if(empty($posts)) :  ?>
    <section class="tj-error-section pt-5 uh-oh">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="tj-error-wrap text-center">
                        <div class="tj-error-content">
                            <div class="error-img">
                                <?= \BrickLayer\Lay\Core\View\Tags\Img::new()->src("@img/empty.png") ?>
                            </div>
                            <h2 class="error-title title-anim">No data at the moment, please try again later</h2>
                            <div class="error-desc"><?= DomainResource::plaster()->local->empty_msg ?></div>
                            <a class="tj-primary-btn error-btn" href="./">
                                <span class="btn-text"><span>Go to Home Page</span></span>
                                <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php return; endif;

$categories = $foundation->post_category()->list(as_array: false);
?>

<input type="hidden" name="list_type" value="<?= DomainResource::plaster()->local->list_type ?>">

<section class="tj-blog-section section-gap">
    <div class="container">
        <div class="row row-gap-5">
            <div class="col-lg-8">
                <div class="blog-post-wrapper">
                    <?php foreach ($posts as $i => $post) :
                        DomainResource::include_file("__partials/blog-page-list.inc", option: [
                            'once' => false,
                            "local" => [
                                "post" => $post,
                                "post_index" => $i,
                            ]
                        ]);
                        ?>
                    <?php endforeach;

                    if(DomainResource::plaster()->local->list_type !== 'search') : ?>
                        <div class="load-more-items text-center fw-bold">Loading more please wait...</div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="tj-main-sidebar sticky-lg-top">
                    <div class="tj-sidebar-widget widget-search wow fadeInUp" data-wow-delay=".1s">
                        <h4 class="widget-title">Search here</h4>
                        <div class="search-box">
                            <form action="blog/search">
                                <input type="search" name="query" id="searchTwo" placeholder="Search here">
                                <button type="submit">
                                    <i class="tji-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="tj-sidebar-widget widget-categories wow fadeInUp" data-wow-delay=".5s">
                        <h4 class="widget-title">Categories</h4>
                        <ul>
                            <?php foreach ($categories as $category) : ?>
                                <li><a href="blog/category/<?= $category->slug ?>"><?= $category->name ?></a></li>
                            <?php endforeach ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>