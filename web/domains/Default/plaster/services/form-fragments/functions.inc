<?php

use <PERSON><PERSON><PERSON>er\Lay\Core\View\SrcFilter;
use BrickLayer\Lay\Core\View\Tags\Img;

function input_element(string $name, array $opts)  : string
{
    $type = $opts['type'] ?? 'text';
    $required = $opts['required'] ?? false;
    $required_text = $required ? 'required' : '';
    $required_ast = $required ? '*' : '';
    $class = $opts['class'] ?? '';
    $style = $opts['style'] ?? '';
    $help_text = $opts['help_text'] ?? null;
    $accept = isset($opts['accept']) ? 'accept="' . $opts['accept'] . '"' : null;
    $max_size = isset($opts['max_size']) ? 'data-max-size="' . $opts['max_size'] . '"' : null;
    $multiple = isset($opts['multiple']) ? 'multiple' : null;

    $label = $opts['label'] ?? $name;
    $placeholder = $opts['placeholder'] ?? "Enter $label";
    $id = $name . "-id";

    $file_class = null;

    if($type == "file")
        $file_class = "class='preview-file-input'";

    $input = "<input type=\"$type\" id=\"$id\" name=\"$name\" placeholder=\"$placeholder\" $required_text $accept $multiple $max_size $file_class>";

    $options = "";

    if(isset($opts['options'])) {
        foreach ($opts['options'] as $i => $option) {
            $selected = $option['selected'] ?? false;

            if ($type == 'select') {
                $selected = $selected ? 'selected' : '';
                $options .= "<option $selected value=\"{$option['id']}\">{$option['name']}</option>";
                continue;
            }

            if ($type == 'radio' || $type == "checkbox") {
                $selected = $selected ? 'checked' : '';
                $x = "$id-$type-$i";
                $options .= <<<LA
                <label class="radio-label" for="$x">
                    <span class="entry-label" data-label="$label">{$option['name']}</span>
                    <input type='$type' id="$x" name="$name" value="{$option['id']}" $required_text $selected>
                </label>
                LA;
            }
        }
    }

    if($type == "add") {
        $field = array_keys($opts['dynamic'])[0];
        $input = "<button type='button' data-dyna-field='$field' class='add-new-ele btn'>Add +</button>";
        $style .= "; flex-direction: row; align-items: center; border-bottom: dotted var(--accent-color); padding-bottom: 5px";
        $d = $opts['dynamic_config']['label'] ?? $label;
        $max_entry = $opts['dynamic_config']['max_entry'] ?? 0;

        $input .= "<div style='display: none' id='$id' data-max-entry='$max_entry' data-title=\"$d\" class='dynamic-element'>" . json_encode($opts['dynamic']) . "</div>";

        $id = "";
    }

    if($type == 'select') $input = "<select name='$name' id='$id' $required_text>$options</select>";

    if($type == "radio" || $type == "checkbox") {
        $input = "<div style='margin-top: 10px' '>$options</div>";
        $id = "";
    }

    if($type == "file" && !$multiple)
        $input .= "<div style='text-align: center; margin: 10px auto'>"
            . Img::new()->ratio("200px", "200px")
                ->style("object-fit: contain")
                ->src("@img/placeholder.svg") .
                "<iframe width='200px' height='200px' style='border-style: none; display: none' src=\""
                . SrcFilter::go("@img/placeholder.svg") . "\"></iframe>"
        . "</div>";

    if($type == "textarea") $input = "<textarea id=\"$id\" name=\"$name\" placeholder=\"$placeholder\" $required_text></textarea>";


    $help_text = empty($help_text) ? '' : '<div class="tooltip-wrapper" id="myTooltip">?<div class="tooltip-text">' . $help_text . '</div></div>';

    $label_ele = "<div>$help_text <label class='entry-label' for='$id'>$label $required_ast</label></div>";

    if(empty($id))
        $label_ele = "<div style='margin:0'>$help_text $label $required_ast</div>";

    return <<<FORM
    <div class="form-group $class" style="$style">
        $label_ele
        $input
    </div>
    FORM;
};