<?php

use <PERSON><PERSON>ayer\Lay\Core\View\DomainResource;
use BrickLayer\Lay\Core\View\SrcFilter;
use BrickLayer\Lay\Core\View\Tags\Img;
use BrickLayer\Lay\Libs\LayDate;
use Web\Default\Plaster;

$site_data = \BrickLayer\Lay\Core\LayConfig::site_data();

$plaster = new Plaster();
$posts = $plaster->homepage_posts();
$testimonies = $plaster->testimonies();

?>
<!-- start: Banner Section -->
<section class="tj-banner-section section-gap-x">
    <div class="banner-area">
        <div class="banner-left-box">
            <div class="banner-content text-center text-lg-start">
                <span class="sub-title wow fadeInDown" data-wow-delay=".2s">
                  <i class="tji-excellence"></i> Recognized for Excellence
                </span>
                <h1 class="banner-title title-anim">
                    Empowering <span>Students</span> Training <span>Educators</span> Partnering <span>Globally</span>
                </h1>
                <div class="banner-desc-area wow fadeInUp d-none d-lg-block ms-0" data-wow-delay=".7s">
                    <div class="banner-desc p-0">
                        Inside Edge connects students to global education, equips schools with top talent and training, and helps institutions expand internationally.
                    </div>
                </div>

                <div class="wow fadeInLeft" data-wow-delay=".2s">
                    <a class="tj-primary-btn mt-3 btn-dark" href="services">
                        <span class="btn-text"><span>Our Services</span></span>
                        <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
                    </a>

                    <a class="tj-primary-btn mt-3 ms-2" href="company/enquiry">
                        <span class="btn-text"><span>Make Enquiry</span></span>
                        <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
                    </a>
                </div>

            </div>
            <div class="banner-shape">
                <?= Img::new()->src("@ui/assets/images/shape/pattern-bg.webp") ?>
            </div>
        </div>
        <div class="banner-right-box d-none d-md-block">
            <div class="banner-img">
                <?= Img::new()->src("@img/hero.webp") ?>
            </div>
            <div class="box-area">
                <div class="customers-box">
                    <div class="customers">
                        <ul>
                            <li class="wow fadeInLeft" data-wow-delay=".5s">
                                <?= Img::new()->style("object-fit: contain; background: #eeeeee;")->src("@img/partners/aru.webp") ?>
                            </li>
                            <li class="wow fadeInLeft" data-wow-delay=".6s">
                                <?= Img::new()->style("object-fit: contain; background: #eeeeee;")->src("@img/partners/uwe-bristol.webp") ?>
                            </li>
                            <li class="wow fadeInLeft" data-wow-delay=".7s">
                                <?= Img::new()->style("object-fit: contain; background: #eeeeee;")->src("@img/partners/university-of-buckingham.webp") ?>
                            </li>
                            <li class="wow fadeInLeft" data-wow-delay=".8s"><span><i class="tji-plus"></i></span></li>
                        </ul>
                    </div>
                    <div class="customers-number wow fadeInUp" data-wow-delay=".5s">3000+</div>
                    <h6 class="customers-text wow fadeInUp" data-wow-delay=".5s">Students Admitted Globally.</h6>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end: Banner Section -->

<!-- start: Choose Section -->
<section id="choose" class="tj-choose-section section-gap">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="sec-heading text-center">
                    <span class="sub-title wow fadeInUp" data-wow-delay=".3s"><i class="tji-box"></i>Choose the Best</span>
                    <h2 class="sec-title title-anim">Why Choose <span>Inside Edge.</span></h2>
                </div>
            </div>
        </div>
        <?= DomainResource::include_file("__partials/why-choose.inc", option: [
            'as_string' => true,
            'once' => false
        ]) ?>
    </div>
</section>
<!-- end: Choose Section -->

<!-- start: Client Section -->
<section class="tj-client-section client-section-gap wow fadeInUp" data-wow-delay=".4s">
    <div class="container-fluid client-container">
        <div class="row">
            <div class="col-12">
                <div class="client-content">
                    <h5 class="sec-title">
                        Over <span class="client-numbers">100</span> Schools
                        Partner With <span class="client-text">Inside Edge</span>
                    </h5>
                </div>
                <div class="swiper client-slider client-slider-1">
                    <div class="swiper-wrapper">
                        <?php DomainResource::include_file("__partials/partner-list.inc")  ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end: Client Section -->

<!-- start: About Section -->
<section class="tj-about-section section-gap">
    <div class="container">
        <div class="row">
            <div class="col-xl-6 col-lg-6 order-lg-1 order-2">
                <div class="about-img-area wow fadeInLeft" data-wow-delay=".2s">
                    <div class="about-img">
                        <?= Img::new()->src("@img/team/lebari-ukpong.webp") ?>
                    </div>
                    <div class="box-area">
                        <div class="experience-box wow fadeInUp" data-wow-delay=".3s">
                            <span class="sub-title">Experiences</span>
                            <div class="customers-number"><?= date("Y") - 2005 ?>+</div>
                            <h6 class="customers-text">Years of Experience, Endless Innovation</h6>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-6 col-lg-6 order-lg-2 order-1">
                <div class="about-content-area style-1 wow fadeInLeft" data-wow-delay=".2s">
                    <div class="sec-heading">
                        <span class="sub-title wow fadeInUp" data-wow-delay=".3s"><i class="tji-box"></i>Get to Know Us</span>
                        <h2 class="sec-title title-anim">Supporting Growth
                            for Students, Educators,
                            and Institutions <span>Worldwide</span>.
                        </h2>
                    </div>
                    <div class="wow fadeInUp" data-wow-delay=".5s">
                        <a class="text-btn" href="company/">
                            <span class="btn-text"><span>About Us</span></span>
                            <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
                        </a>
                    </div>
                </div>
                <div class="about-bottom-area">
                    <div class="client-review-cont wow fadeInUp" data-wow-delay=".7s">
                        <div class="rating-area">
                            <div class="star-ratings">
                                <div class="fill-ratings" style="width: 100%">
                                    <span>★★★★★</span>
                                </div>
                                <div class="empty-ratings">
                                    <span>★★★★★</span>
                                </div>
                            </div>
                        </div>
                        <p class="desc">
                            We believe in building lasting relationships with our clients through trust,
                            innovation, and exceptional service.
                        </p>
                        <div class="client-info-area">
                            <div class="client-info">
                                <h6 class="title">Mrs. Lebari Ukpong</h6>
                                <span class="designation">Founder - Chief Executive Officer</span>
                            </div>
                            <span class="quote-icon"><i class="tji-quote"></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end: About Section -->


<section class="tj-service-section service-2 section-gap section-gap-x">
    <div class="container">
        <div class="row">
            <div class="col-lg-4">
                <div class="content-wrap sticky-lg-top">
                    <div class="sec-heading style-2">
                        <span class="sub-title wow fadeInUp" data-wow-delay=".3s">Our Services</span>
                        <h2 class="sec-title text-white text-anim">
                            Services to Empower Your <span>Ambitions.</span>
                        </h2>
                    </div>
                    <div class="wow fadeInUp" data-wow-delay=".6s">
                        <a class="tj-primary-btn" href="services">
                            <span class="btn-text"><span>More Services</span></span>
                            <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-8">
                <div class="service-wrapper-2">
                    <?php foreach ((new Plaster)->services_menu()['service_overview'] as $i => $service) : ?>
                        <div class="service-item style-2 wow fadeInUp align-items-center" data-wow-delay=".<?= $i + 1 ?>s">
                            <div class="title-area">
                                <div class="service-icon">
                                    <i class="<?= $service['icon'] ?>"></i>
                                </div>
                                <h4 class="title"><a href="<?= $service['url'] ?>"><?= $service['name'] ?></a></h4>
                            </div>
                            <div class="service-content">
                                <p class="desc"><?= $service['desc'] ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <div class="bg-shape-1">
        <?= Img::new()->src('@ui/assets/images/shape/pattern-2.svg') ?>
    </div>
    <div class="bg-shape-2">
        <?= Img::new()->src('@ui/assets/images/shape/pattern-3.svg') ?>
    </div>
    <div class="bg-shape-3">
        <?= Img::new()->src('@img/shape-blur.svg') ?>
    </div>
</section>

<!-- start: Faq Section -->
<section class="tj-faq-section section-gap">
    <div class="container">
        <div class="row justify-content-between">
            <div class="col-lg-6">
                <div class="faq-img-area wow fadeInLeft" data-wow-delay=".3s">
                    <div class="faq-img">
                        <?= Img::new()->src("@img/contact.webp") ?>
                        <h2 class="title title-anim">Need Help? Start Here...</h2>
                    </div>
                    <div class="box-area">
                        <div class="call-box wow fadeInUp" data-wow-delay=".4s">
                            <h4 class="title">Reach out today</h4>
                            <span class="call-icon"><i class="tji-phone"></i></span>
                            <a class="number" href="tel:<?= $site_data->tel->{0} ?>"><span>Call</span></a> <span>/</span>
                            <a class="number" href="company/enquiry"><span>Enquire</span></a>

                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="accordion tj-faq" id="faqOne">
                    <?php foreach((new Plaster())->faqs() as $i => $faq) :
                    if($i == 3) break;
                        ?>
                        <div class="accordion-item <?= $i == 0 ? 'active' : '' ?> wow fadeInUp" data-wow-delay=".3s">
                            <button class="faq-title" type="button" data-bs-toggle="collapse" data-bs-target="#faq-<?= $i ?>"
                                    aria-expanded="true">
                                <?= $faq['question'] ?>
                            </button>
                            <div id="faq-<?= $i ?>" class="collapse <?= $i == 0 ? 'show' : '' ?>" data-bs-parent="#faqOne">
                                <div class="accordion-body faq-text">
                                    <?= $faq['answer'] ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach ?>
                </div>

                <div class="mt-3 text-end">
                    <a class="tj-primary-btn" href="faqs">
                        <span class="btn-text"><span>View More FAQs</span></span>
                        <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end: Faq Section -->

<!-- start: Countup Section -->
<section class="tj-countup-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="countup-wrap">
                    <div class="countup-item">
                        <div class="inline-content">
                            <span class="odometer countup-number" data-count="100"></span>
                            <span class="count-plus">%</span>
                        </div>
                        <span class="count-text">Successful Placements.</span>
                        <span class="count-separator" data-bg-image="<?= SrcFilter::go('@ui/assets/images/shape/separator.svg') ?>"></span>
                    </div>
                    <div class="countup-item">
                        <div class="inline-content">
                            <span class="odometer countup-number" data-count="3"></span>
                            <span class="count-plus">K</span>
                        </div>
                        <span class="count-text">Applications Processed</span>
                        <span class="count-separator" data-bg-image="<?= SrcFilter::go('@ui/assets/images/shape/separator.svg') ?>"></span>
                    </div>
                    <div class="countup-item">
                        <div class="inline-content">
                            <span class="odometer countup-number" data-count="95"></span>
                            <span class="count-plus">%</span>
                        </div>
                        <span class="count-text">Visa Success Rates</span>
                        <span class="count-separator" data-bg-image="<?= SrcFilter::go('@ui/assets/images/shape/separator.svg') ?>"></span>
                    </div>
                    <div class="countup-item">
                        <div class="inline-content">
                            <span class="odometer countup-number" data-count="20"></span>
                            <span class="count-plus">+</span>
                        </div>
                        <span class="count-text">Years of Experience</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- end: Countup Section -->

<?php if(!empty($testimonies)) : ?>
    <!-- start: Testimonial Section -->
    <section class="tj-testimonial-section section-gap section-gap-x">
        <div class="container">
            <div class="row justify-content-between">
                <div class="col-12">
                    <div class="sec-heading-wrap">
                        <span class="sub-title wow fadeInUp" data-wow-delay=".3s"><i class="tji-box"></i>Customers Feedback</span>
                        <div class="heading-wrap-content">
                            <div class="sec-heading">
                                <h2 class="sec-title title-anim">Success <span>Stories</span> Fuel our Passion.</h2>
                            </div>
                            <div class="slider-navigation d-inline-flex wow fadeInUp" data-wow-delay=".4s">
                                <div class="slider-prev">
                                    <span class="anim-icon">
                                      <i class="tji-arrow-left"></i>
                                      <i class="tji-arrow-left"></i>
                                    </span>
                                </div>
                                <div class="slider-next">
                                    <span class="anim-icon">
                                      <i class="tji-arrow-right"></i>
                                      <i class="tji-arrow-right"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="testimonial-wrapper wow fadeInUp" data-wow-delay=".5s">
                        <div class="swiper swiper-container testimonial-slider">
                            <div class="swiper-wrapper">
                                <?php foreach ($testimonies as $testimony) : ?>
                                    <div class="swiper-slide">
                                        <div class="testimonial-item">
                                            <span class="quote-icon"><i class="tji-quote"></i></span>
                                            <div class="desc">
                                                <p><?= $testimony['message'] ?></p>
                                            </div>
                                            <div class="testimonial-author">
                                                <div class="author-inner">
                                                    <div class="author-img">
                                                        <?= Img::new()->src($testimony['dp'] ?? '@shared_img/blank.webp') ?>
                                                    </div>
                                                    <div class="author-header">
                                                        <h4 class="title"><?= $testimony['name'] ?></h4>

                                                        <?php if($testimony['company']): ?>
                                                            <span class="designation"><?= $testimony['company'] ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="swiper-pagination-area"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-shape-1">
            <?= Img::new()->src("@ui/assets/images/shape/pattern-2.svg") ?>
        </div>
        <div class="bg-shape-2">
            <?= Img::new()->src("@ui/assets/images/shape/pattern-3.svg") ?>
        </div>
    </section>
    <!-- end: Testimonial Section -->
<?php
endif;

if(!empty($posts)) : ?>
<section class="tj-blog-section section-gap">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="sec-heading text-center">
                    <span class="sub-title wow fadeInUp" data-wow-delay=".3s"><i class="tji-box"></i>Insights & Ideas</span>
                    <h2 class="sec-title title-anim">Resource for <span>Success.</span></h2>
                </div>
            </div>
        </div>
        <div class="row row-gap-4">
            <?php foreach ($posts as $post) :

                /**
                 * @var \Bricks\Elevator\Blog\Resource\BlogResource $post
                 */
                ?>
                <div class="col-xl-4 col-md-6">
                    <div class="blog-item wow fadeInUp" data-wow-delay=".4s">
                        <div class="blog-thumb" style="height: 300px">
                            <a href="blog/<?= $post->slug ?>">
                                <?= Img::new()->alt($post->title)
                                    ->style("width: 100%; height: 100%; object-fit: cover; object-position: top")
                                    ->ratio("250px", "250px")
                                    ->src($post->photo['thumbnail']) ?>
                            </a>
                            <div class="blog-date">
                                <span class="date"><?= LayDate::date($post->dateUpdatedUnix ?? $post->dateCreated, "d") ?></span>
                                <span class="month"><?= LayDate::date($post->dateUpdatedUnix ?? $post->dateCreated, "M") ?></span>
                            </div>
                        </div>
                        <div class="blog-content">
                            <div class="blog-meta">
                                <span class="categories"><a href="blog/category/<?= $post->category['slug'] ?>"><?= $post->category['name'] ?></a></span>
                                <span>By <a href="blog/author/<?= $post->author['slug'] ?>"><?= $post->author['name'] ?></a></span>
                            </div>
                            <h4 class="title"><a href="blog/<?= $post->slug ?>"><?= $post->title ?></a></h4>
                            <a class="text-btn" href="blog/<?= $post->slug ?>">
                                <span class="btn-text"><span>Read More</span></span>
                                <span class="btn-icon"><i class="tji-arrow-right-long"></i></span>
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>