<?php

use <PERSON><PERSON><PERSON>er\Lay\Core\View\DomainResource;
use BrickLayer\Lay\Core\View\Tags\Img;
use Web\Default\Plaster;

DomainResource::include_file("__partials/breadcrumb.inc", option: [
    'once' => false
]);

/**
 * @var \Elevator\Brand\Resources\TeamResource $data
 */
$data = DomainResource::plaster()->local->data;

if($data) : ?>
    <section class="team-details">
        <div class="container">
            <div class="row justify-content-center">
                <!--  left -->
                <div class="col-12 col-md-8 col-lg-5">
                    <div class="team-details__img sticky-lg-top wow fadeInUp" data-wow-delay=".1s">
                        <?= Img::new()->alt($data->name)->src($data->dp) ?>
                    </div>
                </div>
                <!-- right -->
                <div class="col-12 col-lg-7 ">
                    <div class="team-details__content">
                        <h2 class="team-details__name title-anim"><?= $data->name ?></h2>
                        <span class="team-details__desig wow fadeInUp" data-wow-delay=".1s"><?= $data->position ?></span>

                        <div class="wow fadeInUp mb-3" data-wow-delay=".3s">
                            <?php
                                foreach (explode("\n", $data->about) as $para) {
                                    echo "<p>$para</p>";
                                }
                            ?>
                        </div>

                        <div class="social-links wow fadeInUp" data-wow-delay=".5s">
                            <ul>
                                <?php foreach ($data->social as $social => $url) :
                                    if(empty($url)) continue;
                                    ?>
                                    <li><a href="<?= $url ?>" target="_blank"><i class="fa-brands fa-<?= $social ?>"></i></a></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php return; endif; ?>


<section class="tj-team-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="sec-heading text-center">
                    <span class="sub-title wow fadeInUp" data-wow-delay=".1s"><i class="tji-box"></i>Meet Our Team</span>
                    <h2 class="sec-title title-anim">People Behind <span>Inside Edge</span></h2>
                </div>
            </div>
        </div>
        <div class="row">
            <?php DomainResource::include_file("__partials/team-list.inc") ?>
        </div>
    </div>
</section>
