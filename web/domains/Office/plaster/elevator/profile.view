<?php

use BrickLayer\Lay\Core\View\DomainResource;
use BrickLayer\Lay\Core\View\SrcFilter;
use ElevatorFrontend\Section\BasePlaster;

/**
 * @var BasePlaster $plaster
 */
$plaster = DomainResource::plaster_instance();

$local = DomainResource::plaster()->local;
$foundation = $plaster::elevator_foundation();
$user =  $foundation->user_profile()->as_resource($foundation->user_profile()->model()->fill_current_user());
$login_logs = $foundation->user_auth_log()->current_user_logs();
?>
<div class="card mb-5 mb-xl-10">
    <div class="collapse show">
        <form class="form">
            <div class="card-body border-top p-9">
                <div class="row mb-4">
                    <label class="col-lg-4 col-form-label fw-semibold fs-6">Name</label>
                    <div class="col-lg-12 fv-row">
                        <input readonly class="form-control form-control-solid" value="<?= $user->name ?>">
                    </div>
                </div>

                <div class="row mb-4">
                    <label class="col-lg-4 col-form-label fw-semibold fs-6">Email</label>
                    <div class="col-lg-12 fv-row">
                        <input readonly class="form-control form-control-solid" value="<?= $user->email ?>">
                    </div>
                </div>

                <div class="row mb-4">
                    <label class="col-lg-4 col-form-label fw-semibold fs-6">Company Staff Position</label>
                    <div class="col-lg-12 fv-row">
                        <input readonly class="form-control form-control-solid" value="<?= $user->position ?>">
                    </div>
                </div>

                <div class="row mb-4">
                    <label class="col-lg-4 col-form-label fw-semibold fs-6">System Role</label>
                    <div class="col-lg-12 fv-row">
                        <input readonly class="form-control form-control-solid" value="<?= $user->role ?>">
                    </div>
                </div>

                <div class="row mb-6">
                    <div class="col-lg-12 text-center mb-6">
                        <label class="d-block col-form-label fw-semibold fs-6">Profile Photo</label>
                        <!--begin::Image input-->
                        <div class="image-input image-input-outline" data-kt-image-input="true"
                             style="background-image: url('<?= $local->placeholder ?>')">
                            <!--begin::Preview existing avatar-->
                            <div class="image-input-wrapper w-125px h-125px"
                                 style="background-image: url(<?= $user->dp ?? SrcFilter::go("@shared_img/blank.webp") ?>)"></div>
                            <!--end::Preview existing avatar-->
                            <!--begin::Label-->
                            <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                   data-kt-image-input-action="change" data-bs-toggle="tooltip" title="Change avatar">
                                <i class="ki-outline ki-pencil fs-7"></i>
                                <!--begin::Inputs-->
                                <input type="file" class="avatar-dp" name="dp" accept=".png, .jpg, .jpeg"/>
                                <input type="hidden" name="avatar_remove"/>
                                <!--end::Inputs-->
                            </label>
                            <!--end::Label-->
                            <!--begin::Cancel-->
                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                  data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title="Cancel avatar">
                                <i class="ki-outline ki-cross fs-2"></i>
                            </span>
                            <!--end::Cancel-->
                            <!--begin::Remove-->
                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow"
                                  data-kt-image-input-action="remove" data-bs-toggle="tooltip" title="Remove avatar">
                                <i class="ki-outline ki-cross fs-2"></i>
                            </span>
                            <!--end::Remove-->
                        </div>
                        <!--end::Image input-->
                        <!--begin::Hint-->
                        <div class="form-text">Allowed file types: png, jpg, webp, heic.</div>
                        <!--end::Hint-->
                    </div>
                </div>

                <div class="row mb-6 social-container"></div>

                <div>
                    <label class="col-form-label fw-semibold fs-6">About</label>
                    <div>
                        <input type="hidden" name="about">
                        <div id="about-editor" style="min-height: 300px"><?= $user->about ?></div>
                    </div>
                </div>

            </div>

            <div class="card-footer d-flex justify-content-end py-6 px-9">
                <button type="submit" class="btn btn-primary save-changes" data-section="policy">
                    <span class="indicator-label">Save Changes</span>

                    <span class="indicator-progress">
                        <span data-kt-translate="general-progress">Please wait...</span>
                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card mb-5 mb-lg-10">
    <!--begin::Card header-->
    <div class="card-header">
        <!--begin::Heading-->
        <div class="card-title">
            <h3>Login Sessions</h3>
        </div>
        <!--end::Heading-->
    </div>
    <!--end::Card header-->
    <!--begin::Card body-->
    <div class="card-body p-0">
        <!--begin::Table wrapper-->
        <div class="table-responsive">
            <!--begin::Table-->
            <table class="table align-middle table-row-bordered table-row-solid gy-4 gs-9">
                <!--begin::Thead-->
                <thead class="border-gray-200 fs-5 fw-semibold bg-lighten">
                <tr>
                    <th class="min-w-250px">Location</th>
                    <th class="min-w-100px">Type</th>
                    <th class="min-w-150px">Device</th>
                    <th class="min-w-150px">Browser</th>
                    <th class="min-w-150px">IP Address</th>
                    <th class="min-w-150px">Time</th>
                </tr>
                </thead>
                <!--end::Thead-->
                <!--begin::Tbody-->
                <tbody class="fw-6 fw-semibold text-gray-600">
                <?php if(empty($login_logs)) : ?>
                    <tr><td class="text-center" colspan="100%">No Result at the moment</td></tr>
                <?php else:
                    foreach ($login_logs as $log) :
                        $env = $log['envInfo'];
                        $status = $log['savedLogin'] ? 'success' : 'info';
                        $cookie = $log['savedLogin'] ? 'Cookie' : 'Session';
                        ?>
                        <tr>
                            <td>
                                <a href="javascript:void(0)" class="text-hover-primary text-gray-600"><?= $env['country'] ?></a>
                            </td>
                            <td>
                                <span class="badge badge-light-<?= $status ?> fs-7 fw-bold"><?= $cookie ?></span>
                            </td>
                            <td><?= $env['user_agent']['platform'] ?? null ?></td>
                            <td><?= $env['user_agent']['browser'] ?? null ?></td>
                            <td><?= $env['ip'] ?></td>
                            <td><?= $log['date'] ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                </tbody>
                <!--end::Tbody-->
            </table>
            <!--end::Table-->
        </div>
        <!--end::Table wrapper-->
    </div>
    <!--end::Card body-->
</div>

