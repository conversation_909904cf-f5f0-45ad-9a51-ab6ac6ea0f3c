<?php
$token = \BrickLayer\Lay\Core\View\DomainResource::plaster()->local->token;
?>

<input type="hidden" name="token" autocomplete="off" value="<?= $token ?>">

<div class="mb-8 fv-row" data-kt-password-meter="true">
    <div class="position-relative mb-3">
        <input class="form-control form-control-lg form-control-solid" type="password" placeholder="Password" name="password" autocomplete="off" data-kt-translate="new-password-input-password"  required>
        <span class="btn btn-sm btn-icon position-absolute translate-middle top-50 end-0 me-n2" data-kt-password-meter-control="visibility">
            <i class="ki-outline ki-eye-slash fs-2"></i>
            <i class="ki-outline ki-eye fs-2 d-none"></i>
        </span>
    </div>
</div>

<div class="d-flex flex-stack">
    <button id="kt_new_password_submit" class="btn btn-primary" data-kt-translate="new-password-submit">
        <span class="indicator-label">Submit</span>

        <span class="indicator-progress">Please wait...
            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
        </span>
    </button>
</div>