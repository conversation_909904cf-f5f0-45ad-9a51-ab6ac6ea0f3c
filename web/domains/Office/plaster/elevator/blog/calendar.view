<?php

use ElevatorFrontend\Section\Page;

Page::table(
    tid: "blog-calendar",
    thead: ["Publish Date", "Title", "Categories", "Summary", "Audience", "Author", "Goals", "Intents", "Pry Keyword", "Sec Keyword", "Type", "Status", "Word Count", "CTA", "Internal Links", "External Links", "Assets Needed", "Social Channels", "KPIs", "Date Created", "Date Updated",  ["Actions", "text-end no-sort"]],
    topts: [
        "toolbar" => <<<BAR
        <div role="group" aria-label="dropdown">  
            <button type="button" class="btn btn-outline btn-outline-info" data-bs-toggle="dropdown" aria-expanded="false">
                Actions <i class="ki-outline ki-wrench fs-2"></i>
            </button>
            
            <ul class="dropdown-menu">
              <li><a class="dropdown-item new-entry-via-ai fw-bold text-info" href="javascript:void(0)">Generate With AI <i class="ki-outline text-info ki-abstract-22 fs-4 align-self-center"></i></a></li>
              <li><a class="dropdown-item new-entry-to-table fw-bold" href="javascript:void(0)">New Table Entry</a></li>
              <li><a class="dropdown-item add-in-batch fw-bold" href="javascript:void(0)">Import CSV</a></li>
            </ul>
        </div>
        BAR,
        "checkbox" => true,
        "batch_action" => "Delete Selected",
        "date" => ["Publish", "YYYY-MM-DD"],
    ]
);

?>
<style>
    /* Custom CSS to make table columns expand dynamically */
    #lay-dtable-blog-calendar {
        table-layout: auto !important;
        width: 100% !important;
    }

    /* Remove fixed widths from table cells */
    #lay-dtable-blog-calendar td,
    #lay-dtable-blog-calendar th {
        width: auto !important;
        min-width: fit-content;
        max-width: none !important;
    }

    /* Allow content to determine column width */
    #lay-dtable-blog-calendar .form-control,
    #lay-dtable-blog-calendar .form-select {
        min-width: 200px;
        width: 100%;
    }

    /* For textarea elements, allow them to expand */
    #lay-dtable-blog-calendar textarea {
        min-width: 200px;
    }
</style>

<div class="save-changes-now d-none bg-white" style="position: fixed;bottom: 15px;z-index: 5;width: 80%;text-align: center;padding: 10px;margin: auto;left: 0;right: 0; border-radius: 20px">
    <span class="text-black">You have unsaved changes on this page</span>
    <button class="btn btn-dark commit-changes mx-3">Save</button>

    <button class="btn btn-danger cancel-changes">Discard</button>

</div>
