export function styleStringToObject(styleString) {
    const styleObj = {};

    if (!styleString) return styleObj;

    styleString.split(';').forEach(style => {
        const [property, value] = style.split(':').map(str => str?.trim());
        if (property && value) {
            styleObj[property] = value;
        }
    });

    return styleObj;
}

export function styleObjectToString(styleObj) {
    return Object.entries(styleObj)
        .filter(([property, value]) => property && value)
        .map(([property, value]) => `${property}:${value}`)
        .join(';');
}