(function (){
    const body = $sel(".nothing-found")
    const response = $sel(".response-table")

    $on($sel(".search-far-wide"), "keyup", (e) => {
        const data = e.target.value;

        if(data.length < 3) {
            if(data.length === 0) {
                $class(body, "del", "d-none")
                $class(response, "add", "d-none")
            }

            return;
        }

        $debounce(() => search(data), 500, "search-far-wide-query")
    });

    function addSearch() {
        $sela(".view-details").$loop(btn => {
            btn.$on("click", (e, btn) => {
                e.preventDefault()

                $curl(genApi("bus/form-app/details/" + btn.dataset.id), {
                    preload: $preloader,
                    type: "html"
                }).finally(() => $preloader(false))
                    .then(detail => {
                        if(detail === "")
                            return osNote("No details found for applicant");

                        osModal({
                            head: `Applicant Details`,
                            foot: "",
                            body: detail,
                            size: "lg"
                        })
                    })
            })
        })
    }

    function search(data) {
        $curl(genApi("/bus/form-app/search?query=" + encodeURI(data)), {
            headers: apiHeaders(),
        })
            .then(res => {
                if (res.length === 0)
                    return;

                let output = "";

                $loop(res, row => {
                    let status = "danger"

                    switch (row.status) {
                        default: break;

                        case "IN_PROGRESS":
                            status = "warning";
                            break;

                        case "COMPLETED":
                            status = "success";
                            break;
                    }

                    output += (
                        `<tr>
                            <td class="text-start">
                                <span class="badge py-3 px-4 fs-7 badge-light-${status}">${row.status}</span>
                            </td>
                            <td class="ps-0 text-start">
                                <span class="text-gray-800 fw-bold fs-6 d-block blog-title">${row.refNum}</span>
                            </td>
                            <td class="text-center">
                                <span class="text-gray-400 fw-bold fs-6 d-block">${row.name}</span>
                            </td>
                            <td class="text-center">
                                <span class="text-gray-400 fw-bold fs-6 d-block">${row.submitted}</span>
                            </td>
                            <td class="text-end">
                                <div class="d-flex">
                                    <a data-id="${row.id}" class="btn btn-sm btn-icon btn-primary w-50px h-50px me-3 view-details" title="View Details">
                                        <i class="ki-outline ki-eye fs-2 text-light"></i>
                                    </a>
                                    <a href="business/form/${row.type}/${row.status}?rn=${row.refNum}" target="_blank" class="btn btn-sm btn-icon btn-info w-50px h-50px me-3" title="Open real page">
                                        <i class="ki-outline ki-share fs-2 text-light"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>`
                    )
                })

                $html($sel(".response-body"), "in", output)
                addSearch();
                $class(body, "add", "d-none")
                $class(response, "del", "d-none")
            })
    }

})();