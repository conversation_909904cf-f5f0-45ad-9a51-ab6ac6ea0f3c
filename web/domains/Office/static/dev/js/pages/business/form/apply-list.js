function updateStatus(element) {
    element.$class("del", "bg-warning", "bg-success", "bg-danger");

    let x = "bg-dark";

    if(element.value === "NOT_STARTED")
        x = "bg-danger";

    if(element.value === "IN_PROGRESS")
        x = "bg-warning";

    if(element.value === "COMPLETED")
        x = "bg-success";


    if(element.value !== '')
        $curl(genApi("bus/form-app/status/" + element.value), {
            debounce: 700,
            method: "PUT",
            data: { id: element.dataset.id },
            headers: apiHeaders()
        }).then(() => {
            element.closest("tr").remove()
        });


    return element.$class("add", x);
}

(function (){
    let statusCache = {};
    let staffCache = {};

    $curl(genApi("bus/form-app/status"), {
        headers: apiHeaders()
    }).then(res => statusCache = res);

    $curl(genApi("/system/all-users/1"), {
        headers: apiHeaders()
    }).then(res => staffCache = res);

    const assignFn = (head, api, selectedStaff, id, loadEntries) => (
        osModal({
            head: head,
            foot: "",
            size: "sm",
            body: (
                `<form>
                    <div class="mb-10">
                        <label class="form-label required mb-0">Staff Members</label>
                        ${loadStaff(selectedStaff)}                            
                    </div>
                    <div class="mb-10 text-center">
                        <label class="form-switch form-switch-lg">
                            <input class="form-check-input" type="checkbox" name="notify_staff" value="true" >
                            <span class="form-check-label">Notify Staff</span>
                        </label>
                    </div>
                    <div class="text-center">
                        <input type="hidden" name="id" value="${id}">
                        <div class="text-center">${$facades.submitBtn()}</div>
                    </div>
                </form>`
            ),
            then: () => {
                $('[name=staff]').select2({
                    dropdownParent: CusWind.get.box
                });

                $on($sel(".submit-form"), "click", (e, btn) => {
                    e.preventDefault();

                    ajax(
                        btn,
                        () => $curl(genApi("bus/form-app/" + api), {
                            preload: () => preloadBtn(btn),
                            data: btn,
                            method: "PUT",
                            headers: apiHeaders()
                        })
                            .finally(() => preloadBtn(btn, false))
                            .then(res => serverResponse(res.code, res.message, loadEntries, false))
                    )
                })
            },
        })
    )

    const statusOpt = (id, selected) => {
        let body = "<option value=''>Change Status</option>";

        $loop(statusCache, cache => {
            body += `<option ${cache.id === selected ? 'selected' : ''} value="${cache.id}" ">${cache.name}</option>`;
        });

        let x = "bg-danger";

        if(selected === "IN_PROGRESS")
            x = "bg-warning";

        if(selected === "COMPLETED")
            x = "bg-success";

        return `<select class="form-select text-white ${x}" data-id="${id}" onchange="updateStatus(this)" name="form_status">${body}</select>`;
    };

    const loadStaff = (staffId) => {
        let staff = "<option value=''>Select Staff</option>"

        $loop(staffCache, (res) => {
            staff += `<option value="${res.userId}" ${res.userId === staffId ? 'selected' : ''}>${res.name}</option>`
        })

        return `<select class="form-select" name="staff" required>${staff}</select>`;
    };

    $on(".copy-form-link", "click", (e, btn) => {
        $copyToClipboard(btn.dataset.link, "Form link copied to clipboard")
    })

    simpleTablePage({
        api : {
            list:   "bus/form-app/" + $lay.page.urlFull,
            dateRange:   "bus/form-app/" + $lay.page.urlFull,
        },

        entry: {
            row: (row, i) => {
                let status = row.status;

                if(statusCache) status = statusOpt(row.id, status);

                return (
                    `<td>${i}</td>
                    <td>${row.refNum}</td>
                    <td>${row.name}</td>
                    <td>${status}</td>
                    <td>${row.assigned?.name ?? '-'}</td>
                    <td>${row.assisted?.name ?? '-'}</td>
                    <td>${row.submitted}</td>`
                )
            },

            search: (value, { populateTable, loadEntries }) => {
                $curl(genApi("/bus/form-app/search?query=" + encodeURI(value)), {
                    headers: apiHeaders(),
                    preload: $preloader
                })
                    .finally(() => $preloader(false))
                    .then(res => populateTable(res))
            },

            anchor: {
                id: "id",
                name: "name",
                edit: false,
                actionsFn: ({info}) => {
                    let menu = [
                        {
                            name: "View Details",
                            act: "view",
                        },
                    ]

                    if(info.status !== "COMPLETED") {
                        if(!info.assigned?.id)
                            menu.push(
                                {
                                    name: "Assign Staff-in-Charge",
                                    act: "assignStaff",
                                },
                            );
                        else
                            menu.push(
                                {
                                    name: "Remove Staff-in-Charge",
                                    act: "removeStaff",
                                    className: "text-danger"
                                },
                            );

                        if(!info.assisted?.id)
                            menu.push(
                                {
                                    name: "Assign a Staff Assistant",
                                    act: "assignAssist",
                                },
                            );
                        else
                            menu.push(
                                {
                                    name: "Remove Staff Assistant",
                                    act: "removeAssist",
                                    className: "text-danger"
                                },
                            );
                    }


                    menu.push(
                        { separator: true },
                        {
                            className: "btn btn-primary btn-sm",
                            name: "Download PDF",
                            href: "business/pdf/" + info.id,
                            target: "_blank",
                            wrap: true,
                        },
                    );

                    return menu;
                },
            },
        },

        entryActionFn: ({ loadEntries }) => ({
            view: ({ id, name }) => {
                $curl(genApi("bus/form-app/details/" + id), {
                    preload: $preloader,
                    type: "html"
                }).finally(() => $preloader(false))
                    .then(detail => {
                        if(detail === "")
                            return osNote("No details found for applicant");

                        osModal({
                            head: `${name} Details`,
                            foot: "",
                            body: detail,
                            size: "lg"
                        })
                    })
            },

            removeStaff: ({ id, info }) => {
                cMsg(
                    `Are you sure you want to remove this staff <b>${info.assigned.name}</b> as the staff in charge?`,
                    () => {
                        $curl(genApi("bus/form-app/remove-staff/" + id), {
                            method: "DELETE",
                            preload: $preloader,
                            headers: apiHeaders()
                        })
                            .finally(() => $preloader(false))
                            .then(res => {
                                osNote(res.message, "success");
                                loadEntries();
                            })
                    }
                )
            },
            removeAssist: ({ id, info }) => {
                cMsg(
                    `Are you sure you want to remove this staff <b>${info.assisted.name}</b> as the assistant staff?`,
                    () => {
                        $curl(genApi("bus/form-app/remove-assist/" + id), {
                            method: "DELETE",
                            preload: $preloader,
                            headers: apiHeaders()
                        })
                            .finally(() => $preloader(false))
                            .then(res => {
                                osNote(res.message, "success");
                                loadEntries();
                            })
                    }
                )
            },

            assignStaff: ({ id, name, info }) => {
                assignFn(
                    `Assign a Staff-in-Charge to Applicant ${info.refNum} ${name}`,
                    "staff-assign",
                    info.assigned?.id,
                    id, loadEntries
                );
            },

            assignAssist: ({ id, name, info }) => {
                assignFn(
                    `Assign An Assistant Staff to ${info.refNum} ${name}`,
                    "staff-assist",
                    info.assist?.id,
                    id, loadEntries
                );
            }

        })
    })
})()