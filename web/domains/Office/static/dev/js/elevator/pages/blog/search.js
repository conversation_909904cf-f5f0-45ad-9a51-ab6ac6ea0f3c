(function (){
    const body = $sel(".nothing-found")
    const response = $sel(".response-table")

    $on($sel(".search-far-wide"), "keyup", (e) => {
        const data = e.target.value;

        if(data.length < 3) {
            if(data.length === 0) {
                $class(body, "del", "d-none")
                $class(response, "add", "d-none")
            }

            return;
        }

        $debounce(() => search(data), 500, "search-far-wide-query")
    })

    function addDelete() {
        $sela(".delete-blog").$loop(btn => {
            btn.$on("click", (e, btn) => {
                e.preventDefault()
                const parent = btn.closest("tr")

                Swal.fire({
                    html: `Are you sure you want to delete <b>${parent.$sel(".blog-title").$html()}</b>? This process is irreversible!`,
                    icon: "warning",
                    buttonsStyling: false,
                    confirmButtonText: "Yeah, do it!",
                    cancelButtonText: "Cancel",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                })
                    .then((res) => {
                        if (res.isConfirmed)
                            $curl(genApi("/blog/delete"), {
                                preload: $preloader,
                                data: {
                                    id: btn.dataset.id,
                                },
                                headers: apiHeaders()
                            })
                                .finally(() => $preloader("hide"))
                                .then(res => {
                                    parent.remove()
                                    osNote(res.message, "success")
                                })
                    })
            })
        })
    }

    function search(data) {
        $curl(genApi("/blog/search?query=" + encodeURI(data)), {
            headers: apiHeaders(),
        })
            .then(res => {
                if (res.length === 0)
                    return;

                let output = "";

                $loop(res, row => {
                    let status = "success"

                    switch (row.postStatus) {
                        default:
                            status = "primary";
                            break;

                        case "REVISION":
                            status = "danger";
                            break;
                        case "DRAFT":
                            status = "warning";
                            break;
                        case "SCHEDULE":
                            status = "info";
                            break;
                        case "PUBLISH":
                            status = "success";
                            break;
                    }

                    output += (
                        `<tr>
                            <td class="text-start">
                                <span class="badge py-3 px-4 fs-7 badge-light-${status}">${row.postStatusName}</span>
                            </td>
                            <td class="ps-0 text-start">
                                <span class="text-gray-800 fw-bold fs-6 d-block blog-title">${row.title}</span>
                            </td>
                            <td class="text-center">
                                <span class="text-gray-400 fw-bold fs-6 d-block">${row.author}</span>
                            </td>
                            <td class="text-end">
                                <div class="d-flex">
                                    <a href="${websiteUrl}blog/${row.slug}?mode=preview" class="btn btn-sm btn-icon btn-primary w-50px h-50px me-3">
                                        <i class="ki-outline ki-eye fs-2 text-light"></i>
                                    </a>
                                    <a href="${baseUrl}blog/compose/${row.postId}" class="btn btn-sm btn-icon btn-warning w-50px h-50px me-3">
                                        <i class="ki-outline ki-pencil fs-2 text-light"></i>
                                    </a>
                                    <a href="javascript:void(0)" data-id="${row.postId}" class="btn btn-sm btn-icon btn-bg-light btn-danger w-50px h-50px delete-blog">
                                        <i class="ki-outline ki-trash fs-2 text-light"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>`
                    )
                })

                $html($sel(".response-body"), "in", output)
                addDelete()
                $class(body, "add", "d-none")
                $class(response, "del", "d-none")
            })
    }

})();