simpleTablePage({
    api: {
        list: "business/growth-tools/prospects/list/{page}",
        delete: "business/growth-tools/prospects/delete",
    },

    entry: {
        row: (row, i) => (
            `<td>${i}</td>
            <td>${row.email}</td>
            <td>${row.date}</td>`
        ),

        anchor: {
            id: "propId",
            name: "email",
            edit: () => "",
            actions: [
                {
                    name: "View Details",
                    act: "view",
                },
            ]
        },
    },

    entryAction : {
        view: ({info, name}) => {
            let body = `<a href="mailto:${name}" class="fs-2 d-block alert alert-info mb-5 p-2 text-center">${name}</a>`

            $loop(info.toolsPicked, data => {
                body += (
                    `<details class="mb-5">
                        <summary class="title fs-3 fw-bold">${data.title}</summary>
                        <div class="p-3 bg-gray-100 ms-5 my-2">
                            <p>${data.toolInfo}</p>
                        </div>
                        <div class="text-center">
                            <img src="${data.coverPhoto}" style="max-height: 200px" class="img-fluid" alt="cover photo">
                        </div>
                    </details>`
                )
            })

            osModal({
                head: name + " Messages",
                foot: "",
                body: body
            })
        }
    }

});