simpleTablePage({
    api: {
        list: "business/prospects/list",
        delete: "business/prospects/delete",
    },

    entry: {
        row: (row, i) => (
            `<td>${i}</td>
            <td>${row.name}</td>
            <td>${row.email}</td>
            <td>${row.updated_at ?? row.created_at}</td>`
        ),

        anchor: {
            id: "id",
            name: "name",
            edit: () => "",
            actions: [
                {
                    name: "View Details",
                    act: "view",
                },
            ]
        },
    },

    entryAction : {
        view: ({info, name}) => {
            let body = `<a href="mailto:${info.email}" class="fs-2 d-block alert alert-info mb-5 p-2 text-center">${info.email}</a>`

            $loop(JSON.parse(info.body), data => {
                data.body = data.body
                    .replaceAll("\\'", "'")
                    .replaceAll("\\n", "<br>")

                body += (
                    `<details class="mb-5">
                        <summary class="title fs-3 fw-bold">${data.subject} [${$facades.date(data.date)}]</summary>
                        <div class="p-3 bg-gray-100 ms-5 mt-2">
                            <p>${data.body}</p>
                        </div>
                    </details>`
                )
            })

            osModal({
                head: name + " Messages",
                foot: "",
                body: body
            })
        }
    }

});