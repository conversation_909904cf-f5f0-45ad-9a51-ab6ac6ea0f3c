simpleTablePage({
    api : {
        list:   "system/logs",
        dateRange: "system/logs",
    },

    entry: {
        row: (row) => (
            `<td>${row.date}</td>
            <td>${row.message}</td>
            <td>${row.user}</td>`
        ),
        anchor: {
            id: "logId",
            name: "logId",
            edit: false,
            actions: [
                {
                    name: "More Info",
                    act: "view"
                }
            ]
        },
    },

    entryAction: {
        view: ({name, info}) => {
            const data = info.dataset

            osModal({
                head: "More Info on " + name,
                foot: "",
                size: "lg",
                body: (
                    `<div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${info.date}" readonly />
                        <label class="form-label mb-3">Date</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${data.activity}" readonly />
                        <label class="form-label mb-3">Activity</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${data.route}" readonly />
                        <label class="form-label mb-3">Route</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${data.referer}" readonly />
                        <label class="form-label mb-3">Referrer URL</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <textarea class="form-control form-control-solid" id="form-b-desc" name="desc" style="height: 200px">${info.message}</textarea>
                        <label class="form-label mb-3">Message</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${info.user}" readonly />
                        <label class="form-label mb-3">User</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${info.userId}" readonly />
                        <label class="form-label mb-3">User ID</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${data.ip}" readonly />
                        <label class="form-label mb-3">User IP</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${data.device.platform}" readonly />
                        <label class="form-label mb-3">Device</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${data.device.browser}" readonly />
                        <label class="form-label mb-3">Browser</label>
                    </div>
                    <div class="mb-10 form-floating">
                        <input class="form-control form-control-lg form-control-solid" value="${data.device.agent}" readonly />
                        <label class="form-label mb-3">Agent</label>
                    </div>
                    
                    
                    `
                )
            })
        }
    },

    enableDelete: false
})