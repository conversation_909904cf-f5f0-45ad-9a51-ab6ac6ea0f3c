simpleTablePage({
    api : {
        add:    "brand/team/",
        batch:  "brand/team/bulk",
        edit:   "brand/team/update",
        list:   "brand/team/{page}",
        delete: "brand/team/",
    },
    form: {
        head: "Add a New Team Member",
        body: (info = {}) => {
            const dp = info.dp ?? DP_PLACEHOLDER

            return (
                `<div class="mb-10 d-flex justify-content-center align-items-center flex-column">
                    <h5 class="mb-5">Team Member's DP</h5>
                    <div class="text-center">
                        <div class="image-input image-input-outline" data-kt-image-input="true" style="background-image: url('${dp}')">
                            <div class="image-input-wrapper w-125px h-125px" style="background-image: url(${dp})"></div>
                    
                            <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" title="Change avatar">
                                <i class="ki-outline ki-pencil fs-7"></i>
                                <input type="file" name="dp" data-max-size="3000000" accept=".png, .jpg, .jpeg, .webp" />
                                <input type="hidden" name="avatar_remove" />
                            </label>
                    
                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title="Cancel avatar">
                                <i class="ki-outline ki-cross fs-2"></i>
                            </span>
                    
                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" title="Remove avatar">
                                <i class="ki-outline ki-cross fs-2"></i>
                            </span>
                        </div>
                    
                        <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                    </div>
                </div>
                <div class="mb-10 form-floating">
                    <input id="c-name" class="form-control form-control-lg form-control-solid" name="name" value="${info.name ?? ''}" placeholder="Client's Name" required />
                    <label for="c-name" class="form-label mb-3 required">Name</label>
                </div>
                <div class="mb-10 form-floating">
                    <input id="c-post" class="form-control form-control-lg form-control-solid" name="position" value="${info.position ?? ''}" placeholder="Client's Position" required />
                    <label for="c-post" class="form-label mb-3 required">Position</label>
                </div>
                <div class="mb-10 form-floating">
                    <textarea class="form-control form-control-solid" placeholder="about" id="form-b-int" name="intro" style="height: 100px">${info.intro ?? ''}</textarea>
                    <label for="form-b-int" class="form-label">Short Intro</label>
                </div>
                <div class="mb-10 form-floating">
                    <textarea class="form-control form-control-solid" placeholder="about" id="form-b-desc" name="about" style="height: 200px">${info.about ?? ''}</textarea>
                    <label for="form-b-desc" class="form-label">About</label>
                </div>
                <div class="mb-10 form-floating">
                    <input id="c-lk" type="url" class="form-control form-control-lg form-control-solid" name="social[linkedin]" value="${info.social?.linkedin ?? ''}" placeholder="LinkedIn" />
                    <label for="c-lk" class="form-label mb-3">LinkedIn Link</label>
                </div>
                <div class="mb-10 form-floating">
                    <input id="c-fb" type="url" class="form-control form-control-lg form-control-solid" name="social[instagram]" value="${info.social?.instagram ?? ''}" placeholder="Instagram" />
                    <label for="c-fb" class="form-label mb-3">Instagram Link</label>
                </div>
                <div class="mb-10 form-floating">
                    <input id="c-tw" type="url" class="form-control form-control-lg form-control-solid" name="social[x]" value="${info.social?.x ?? ''}" placeholder="Twitter" />
                    <label for="c-tw" class="form-label mb-3">Twitter (X) Link</label>
                </div>`
            )
        },
        then: () => KTImageInput.init()
    },

    batch: {
        csv: "teams.csv",
        note: "Please note that if you have any existing team member on the csv file, their existing information will be overwritten on the database.",
    },

    entry: {
        row: (row, i) => {
            row.dp = row.dp ?? DP_PLACEHOLDER

            return (
                `<td>${i}</td>
                <td><img src="${row.dp}" alt="dp" style="max-width: 50px" /></td>
                <td>${row.name}</td>
                <td>${row.position}</td>
                <td>${row.intro}</td>`
            )
        },
        anchor: {
            id: "id",
            name: "name",
            edit: false,
            delete: false,
            actionsFn: ({info}) => {
                return [
                    {
                        name: "Edit Member",
                        act: "edit"
                    },
                    {
                        name: "Change Order",
                        act: "updateOrder",
                    },
                    { separator: true },
                    {
                        name: "Delete Member Profile",
                        act: "delete",
                        wrap: true,
                        className: "btn btn-danger btn-sm"
                    },
                ]
            },
        },
    },

    entryActionFn : ({loadEntries}) => {
        return {
            updateOrder: ({name, id, info}) => {
                osModal({
                    head: "Change Order of " + name,
                    foot: "",
                    closeOnBlur: false,
                    size: "md",
                    body: (
                        `<form>
                            <div class="mb-10">
                                <label for="c-title" class="form-label mb-3 required">Current Order</label>
                                <h3>${info.order}</h3>                                
                            </div>
                            <div class="mb-10 form-floating">
                                <input id="form-b-name" type="number" class="form-control form-control-lg form-control-solid" name="order" placeholder="order" required />
                                <label for="form-b-name" class="form-label mb-3 required">New Order</label>
                            </div>
                            <div class="text-center">
                                <input type="hidden" name="id" value="${id}">
                                <div class="text-center">${$facades.submitBtn()}</div>
                            </div>
                        </form>`
                    ),
                    then: () =>  {
                        $on($sel(".submit-form"), "click", (e, btn) => {
                            e.preventDefault();

                            ajax(
                                btn,
                                () => $curl(genApi("brand/team/change-order"), {
                                    preload: () => preloadBtn(btn),
                                    data: btn,
                                    headers: apiHeaders(),
                                    method: "PUT"
                                })
                                    .finally(() => preloadBtn(btn, false))
                                    .then(res => serverResponse(res.code, res.message, loadEntries, false))
                            )
                        })
                    }
                })
            },
        }
    },

    deleteMsg: (name) => `You are about to delete [${name}], are you sure you want to do that?`,
})