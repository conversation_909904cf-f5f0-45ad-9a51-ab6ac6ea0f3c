(function () {
    $on($name("search_map")[0], "keyup", (e, btn) => {
        e.preventDefault()

        if(btn.value === "")
            return $sel(".map-field").$html("No information received")

        $curl(genApi("/sys-arch/sitemap/inspect"), {
            data: btn.value,
            type: "text",
            headers: apiHeaders(),
            debounce: 1000
        }).then(res => $sel(".map-field").$html(res))
    })
})();