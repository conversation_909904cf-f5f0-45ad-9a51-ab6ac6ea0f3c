simpleTablePage({
    api : {
        add:    "system/user-position/new",
        edit:   "system/user-position/edit",
        list:   "system/user-position/list",
    },
    form: {
        head: "Add Staff Positions",
        body: (info = {}) => (
            `<div class="mb-10 form-floating">
                <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="name" value="${info.name ?? ''}" placeholder="Name" required />
                <label for="form-b-name" class="form-label mb-3 required">Name</label>
            </div>
            <div class="mb-10 form-floating">
                <textarea class="form-control form-control-solid" placeholder="Optional Description" id="form-b-desc" name="note" style="height: 200px">${info.note ?? ''}</textarea>
                <label for="form-b-desc" class="form-label">Description</label>
            </div>`
        ),
    },

    entry: {
        row: (row, i) => (
            `<td>${i}</td>
            <td>${row.name}</td>
            <td>${row.note}</td>`
        ),
        anchor: {
            id: "id",
            name: "name",
        },
    },

    enableDelete: false
})