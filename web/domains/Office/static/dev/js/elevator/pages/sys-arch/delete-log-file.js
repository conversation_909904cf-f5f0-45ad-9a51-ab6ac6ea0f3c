(function () {
    if(!$sel(".delete-exceptions"))
        return;

    $on($sel(".delete-exceptions"), "click", (e, btn) => {
        e.preventDefault()

        ajax(
            btn,
            () => $curl(genApi("/sys-arch/" + $lay.page.urlFull), {
                preload: () => preloadBtn(btn),
                method: "post",
                headers: apiHeaders()
            })
                .finally(() => preloadBtn(btn, false))
                .then(res => {
                    aMsg(`<h1 class="text-success">${res.message}</h1>`, {
                        onClose: () => setTimeout(() => $loc.reload(), 1000),
                        size: "xs"
                    })
                })
        )

    })
})();