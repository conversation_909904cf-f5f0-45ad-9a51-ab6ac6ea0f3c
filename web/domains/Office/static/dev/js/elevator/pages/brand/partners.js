simpleTablePage({
    api : {
        add:    "brand/partner/",
        edit:   "brand/partner/update",
        list:   "brand/partner/{page}",
        delete: "brand/partner/",
    },
    form: {
        head: "Add a New Partner",
        body: (info = {}) => {
            const dp = info.logo ?? DP_PLACEHOLDER

            return (
                `<div class="mb-10 d-flex justify-content-center align-items-center flex-column">
                    <h5 class="mb-5">Client's Logo</h5>
                    <div class="text-center">
                        <div class="image-input image-input-outline" data-kt-image-input="true" style="background-image: url('${dp}')">
                            <div class="image-input-wrapper w-125px h-125px" style="background-image: url(${dp})"></div>
                    
                            <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" title="Change avatar">
                                <i class="ki-outline ki-pencil fs-7"></i>
                                <input type="file" name="logo" data-max-size="3000000" accept=".png, .jpg, .jpeg, .webp" />
                                <input type="hidden" name="avatar_remove" />
                            </label>
                    
                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title="Cancel avatar">
                                <i class="ki-outline ki-cross fs-2"></i>
                            </span>
                    
                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" title="Remove avatar">
                                <i class="ki-outline ki-cross fs-2"></i>
                            </span>
                        </div>
                    
                        <div class="form-text">Allowed file types: png, jpg, jpeg.</div>
                    </div>
                </div>
                <div class="mb-10 form-floating">
                    <input id="c-name" class="form-control form-control-lg form-control-solid" name="name" value="${info.name ?? ''}" placeholder="Client's Name" required />
                    <label for="c-name" class="form-label mb-3 required">Name</label>
                </div>
                <div class="mb-10 form-floating">
                    <input id="c-comp" type="url" class="form-control form-control-lg form-control-solid" name="website" placeholder="Project" value="${info.website ?? ''}" />
                    <label for="c-comp" class="form-label mb-3">Website</label>
                </div>`
            )
        },
        then: () => KTImageInput.init()
    },

    entry: {
        row: (row, i) => {
            row.logo = row.logo ?? DP_PLACEHOLDER

            return (
                `<td>${i}</td>
                <td><img src="${row.logo}" alt="dp" style="max-width: 50px" /></td>
                <td>${row.name}</td>
                <td>${row.website}</td>`
            )
        },
        anchor: {
            id: "id",
            name: "name",
        },
    },

    deleteMsg: (name) => `You are about to delete this partner [${name}], are you sure you want to do that?`,
})