(function () {
    let moreInfoEditor;
    let categories = [];
    let categoriesString = "";

    getCategory().then(() => {
        simpleTablePage({
            api: {
                add: "business/growth-tools/new",
                edit: "business/growth-tools/edit",
                list: "business/growth-tools/list",
                delete: "business/growth-tools/delete",
            },

            form: {
                head: 'Business Growth Tool',
                body: (info = {}) => {
                    const dp = info.coverPhoto ?? DP_PLACEHOLDER
                    getCategory(info.categoryId);

                    return (
                        `
                        <input name="tool_id" value="${info.toolId ?? ''}" type="hidden" />
                        <div class="mb-10 form-floating">
                            <input id="form-b-title" class="form-control form-control-lg" name="title" value="${info.title ?? ''}" placeholder="" required />
                            <label for="form-b-title" class="form-label mb-3 required">Title</label>
                        </div>
                        
                        <div class="mb-10">
                            <label for="c-title" class="form-label mb-3 required">Category</label>
                            ${categoriesString}                            
                        </div>
                        
                        <div class="mb-10 d-flex justify-content-center align-items-center flex-column">
                            <h5 class="mb-5">Tool Cover Image</h5>
                            <div class="text-center">
                                <div class="image-input image-input-outline" data-kt-image-input="true" style="background-image: url('${dp}')">
                                    <div class="image-input-wrapper w-125px h-125px" style="background-image: url(${dp})"></div>
                            
                                    <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" title="Change avatar">
                                        <i class="ki-outline ki-pencil fs-7"></i>
                                        <input type="file" ${info.FORM_ACTION === 'ADD' ? 'required' : ''} name="cover_photo" accept=".png, .jpg, .jpeg, .webp" />
                                        <input type="hidden" name="avatar_remove" />
                                    </label>
                            
                                    <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-2"></i>
                                    </span>
                            
                                    <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-2"></i>
                                    </span>
                                </div>
                            
                                <div class="form-text">Allowed file types: png, jpg, jpeg, webp.</div>
                            </div>
                        </div>
                        
                        <div class="form-group mb-10">
                            <label for="form-b-file" class="form-label mb-3 ${info.FORM_ACTION === 'ADD' ? 'required' : ''}">Resource</label>
                            <input id="form-b-file" class="form-control form-control-lg mb-4" name="resource" type="file" accept="application/pdf" data-max-size="8000000" ${info.FORM_ACTION === 'ADD' ? 'required' : ''} />
                            <embed class="preview-document bg-dark" type="application/pdf" src="${info.file ?? ''}" width="100%" height="200">
                        </div>
                        <div class="mb-10">
                            <div class="form-check p-3 form-switch form-check-custom d-inline-flex flex-column col-md-2 col-4 text-center">
                                <input class="form-check-input" ${info.FORM_ACTION === 'ADD' ? 'checked' : (info.collectData ? 'checked' : '') } type="checkbox" value="1" name="collect_data" id="collect-data"/>
                                <label class="form-check-label mx-0 mt-2 fw-bold" for="collect-data">
                                    Request for Email
                                </label>
                            </div>
                            <div class="form-check p-3 form-switch form-check-custom d-inline-flex flex-column col-md-2 col-4 text-center">
                                <input class="form-check-input" ${info.notifyPeople ? 'checked' : '' } type="checkbox" value="1" name="notify_subscribers" id="notify_subscribers"/>
                                <label class="form-check-label mx-0 mt-2 fw-bold" for="notify_subscribers">
                                    Notify Newsletter List
                                </label>
                            </div>
                            
                        </div>
                        <div class="form-group mb-10">
                            <label for="form-b-desc" class="form-label required">Resource Info</label>
                            <input type="hidden" name="more_info">
                            <div id="resource-info" style="min-height: 150px">${info.toolInfo ?? ''}</div>
                        </div>
                        
                    `
                    );
                },
                then: () => {
                    KTImageInput.init();

                    moreInfoEditor = quillEditor("resource-info", "Add a basic description for this resource");

                    $media({
                        srcElement: $name('resource')[0],
                        previewElement: $sel('.preview-document'),
                    })
                },
                onSubmit: (btn) => {
                    $name('more_info')[0].value = quillEditorContent(moreInfoEditor)

                    if($name('more_info')[0].value.trim() === "") {
                        osNote(
                            "Resource Info cannot be empty, you must describe this resource for the consumers",
                            "warn"
                        );

                        return false;
                    }

                    return true;
                }
            },

            entry: {
                row: (row, i) => (
                    `<td>${i}</td>
                    <td>${row.title}</td>
                    <td>${row.category}</td>
                    <td>${row.collectData}</td>
                    <td>${row.date}</td>`
                ),

                anchor: {
                    id: "toolId",
                    name: "title",
                },
            },

        });
    });

    async function getCategory(id = null, invalidate = false)
    {
        function loopCat(resolve) {
            categories  = resolve;

            let cat = "<option value=''>Select a Category</option>";

            $loop(resolve, res => {
                cat += (
                    `<option ${id === res.id ? 'selected' : ''} value="${res.id}">${res.name}</option>`
                )
            })

            categoriesString = `<select name="category" required class="form-control form-control-lg">${cat}</select>`
        }

        if(!invalidate && categories.length !== 0) {
            loopCat(categories);
            return true;
        }

        return $curl(genApi("/business/growth-tools/category/list"),{
            headers: apiHeaders()
        }).then(resolve => {
            loopCat(resolve);
            return true;
        });
    }
})()