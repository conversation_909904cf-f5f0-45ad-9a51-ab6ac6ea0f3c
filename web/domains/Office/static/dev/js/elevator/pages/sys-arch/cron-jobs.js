simpleTablePage({
    api : {
        add:    "sys-arch/cron-jobs/new",
        list:   "sys-arch/cron-jobs/list",
        edit:   "sys-arch/cron-jobs/edit",
        delete: "sys-arch/cron-jobs/delete",
    },

    deleteMsg: (name) => `Are you sure you want to delete this job: <b>${name}</b>?`,

    form: {
        head: "New Cron Job",
        body: (info = {}) => {
            let checked = "checked"

            if($type(info.use_php) === "String")
                checked = info.use_php === "1" ? "checked" : ""

            return (
                `<h3 class="mb-2 text-center"><a rel="noreferrer" href="https://crontab.guru/" target="_blank">Schedule Examples</a></h3>

                <div class="form-floating">
                    <input id="form-b-schedule" class="form-control form-control-lg form-control-solid text-center" style="font-size: 2rem; padding: 3rem 1rem 1.5rem" name="schedule" value="${info.schedule ?? ''}" required />
                    <label for="form-b-schedule" class="form-label mb-3 required">Schedule (* * * * *) [5]</label>
                </div>
                <div class="text-muted mb-10 text-center">Schedule Example: 30 18 * * *</div>
                
                <div class="form-floating">
                    <input id="form-b-script" class="form-control form-control-lg form-control-solid text-center" name="script" value="${info.script ?? ''}" required />
                    <label for="form-b-script" class="form-label mb-3 required">Script</label>
                </div>
                <div class="text-muted mb-5 text-center">Script Example: utils/db-backup.php </div>
                
                <div class="form-check p-3 mb-10 form-switch form-check-custom form-check-solid d-flex flex-row col-md-2 col-4 justify-content-center w-100 gap-3">
                    <input class="form-check-input" type="checkbox" ${checked} type="checkbox" value="true" id="use-php-script" name="use_php" />
                    <label class="form-check-label mx-0 mt-2 fw-bold" for="use-php-script">
                        Run As PHP Script 
                    </label>
                </div>`
            )
        },
        then: () => {

        }
    },

    entry: {

        row: (row) => {
            let active = `<div class="badge badge-outline badge-success">Running</div>`

            if(row.active === "0")
                active = `<div class="badge badge-outline badge-warning">Paused</div>`

            return (
                `<td>${row.schedule}</td>
                <td>${row.script}</td>
                <td>${active}</td>
                <td>${row.last_run ? $facades.date(row.last_run) : 'Never'}</td>`
            )
        },

        anchor : {
            id: "id",
            name: "script",

            actionsFn: ({info}) => [
                {
                    name: "Run Script",
                    act: "run",
                },
                info.active === "1" ? {
                    name: "Pause Script",
                    act: "pause",
                    className: "text-warning"
                } : {
                    name: "Play Script",
                    act: "play",
                    className: "text-primary"
                },

            ],
        },

    },

    entryActionFn: ({ loadEntries }) => ({
        run: ({ name, id }) => {
            Swal.fire({
                html: `Are you sure you want to run this script? <b>${name}</b>`,
                icon: "info",
                buttonsStyling: false,
                confirmButtonText: "Run it!",
                cancelButtonText: "Cancel",
                customClass: {
                    confirmButton: "btn btn-primary"
                }
            })
                .then((res) => {
                    if (res.isConfirmed)
                        $curl(genApi("/sys-arch/cron-jobs/run"), {
                            preload: $preloader,
                            data: {
                                id: id,
                            },
                            headers: apiHeaders()
                        })
                            .finally(() => $preloader("hide"))
                            .then(res => {
                                loadEntries()
                                osNote(res.message, "success")
                            })
                })
        },
        pause: ({ name, id }) => {
            Swal.fire({
                html: `You are about <b>pause</b> this script? <b>${name}</b>`,
                icon: "warning",
                buttonsStyling: false,
                confirmButtonText: "Pause it!",
                cancelButtonText: "Cancel",
                customClass: {
                    confirmButton: "btn btn-primary"
                }
            })
                .then((res) => {
                    if (res.isConfirmed)
                        $curl(genApi("/sys-arch/cron-jobs/pause"), {
                            preload: $preloader,
                            data: {
                                id: id,
                            },
                            headers: apiHeaders()
                        })
                            .finally(() => $preloader("hide"))
                            .then(res => {
                                loadEntries()
                                osNote(res.message, "success")
                            })
                })
        },
        play: ({ name, id }) => {
            Swal.fire({
                html: `Script will <b>Play</b> if you continue? <b>${name}</b>`,
                icon: "info",
                buttonsStyling: false,
                confirmButtonText: "Play it!",
                cancelButtonText: "Cancel",
                customClass: {
                    confirmButton: "btn btn-primary"
                }
            })
                .then((res) => {
                    if (res.isConfirmed)
                        $curl(genApi("/sys-arch/cron-jobs/play"), {
                            preload: $preloader,
                            data: {
                                id: id,
                            },
                            headers: apiHeaders()
                        })
                            .finally(() => $preloader("hide"))
                            .then(res => {
                                let type = "warn"

                                if (res.code === 200) {
                                    type = "success"
                                    loadEntries()
                                }

                                osNote(res.message, type)
                            })
                })
        },

    })
})