simpleTablePage({
    api : {
        add:    "faqs/new",
        batch:  "faqs/batch-new",
        edit:   "faqs/edit",
        list:   "faqs/list",
        delete: "faqs/delete",
    },
    form: {
        head: "Add New FAQ",
        body: (info = {}) => (
            `<div class="mb-10 form-floating">
                <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="question" value="${info.question ?? ''}" placeholder="FAQ" required />
                <label for="form-b-name" class="form-label mb-3 required">Question</label>
            </div>
            <div class="mb-10 form-floating">
                <textarea class="form-control form-control-solid" placeholder="Answer" id="form-b-desc" name="answer" style="height: 200px" required>${info.answer ?? ''}</textarea>
                <label for="form-b-desc" class="form-label required">Answer</label>
            </div>`
        ),
    },

    batch: {
        csv: "faqs.csv",
        note: "Please note that if you have any existing question inside the file, the question will be overwritten with the current answer.",
    },

    entry: {
        row: (row, i) => (
            `<td>${i}</td>
            <td>${row.question}</td>
            <td>${row.answer}</td>`
        ),
        anchor: {
            id: "id",
            name: "question",
        },
    },

    deleteMsg: (name) => `You are about to delete the question [${name}], are you sure you want to do that?`,
})