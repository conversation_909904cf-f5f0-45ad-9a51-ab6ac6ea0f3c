let moduleCache = [];
const getModules = async () => $curl(genApi("/system/roles/modules"), {
    headers: apiHeaders()
}).then(res => moduleCache = res)

const permissionTemplate = ({ permissions = null, xPermissions = null, disableSelected = false, disableAll = false, isSuperAdmin }) => {
    let modules = "";

    if (moduleCache.length === 0)
        getModules()


    if(isSuperAdmin)
        disableSelected = true;

    $loop(moduleCache, module => {
        let checked = isSuperAdmin ? 'checked disabled' : '';

        let actions = "";

        $loop(module.access, access => {
            $loop(permissions, (permission) => {
                if(checked !== "") return "break";

                checked = "";

                if (permission !== access.value)
                    return 'continue';

                checked = 'checked' + (disableSelected ? ' disabled' : '');
            })

            if(xPermissions !== null)
                $loop(xPermissions, (permission) => {
                    if(checked !== "") return "break";

                    checked = "";

                    if (permission !== access.value)
                        return 'continue';

                    checked = 'checked';
                })

            checked += disableAll ? " disabled" : "";

            actions += (
                `<label class="form-check form-check-sm form-check-custom me-5 me-lg-20">
                    <input class="form-check-input" type="checkbox" name="permissions[]" ${checked} value="${access.value}" id="system-module-${module.id}-${access.type}" />
                    <span class="form-check-label">${access.type}</span>
                </label>`
            )

            checked = "";
        })

        modules += (
            `<tr>
                <td class="text-gray-800">${module.name}</td>
                <td><div class="d-flex">${actions}</div></td>
            </tr>`
        )
    })

    return (
        `<div class="fv-row role-resting-place">
            <label class="fs-5 fw-bold form-label mb-2">Role Permissions</label>
            
            <div class="table-responsive">
                <table class="table align-middle table-row-dashed fs-6 gy-5">
                    <tbody class="text-gray-600 fw-semibold">
                        <tr>
                            <td class="text-gray-800">Administrator Access
                                <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true" data-bs-content="Allows a full access to the system">
                                    <i class="ki-outline ki-information fs-7"></i>
                                </span>
                            </td>
                            <td>
                                <label class="form-check form-check-custom me-9">
                                    <input class="form-check-input" type="checkbox" value="" id="select-all-perm" ${disableAll ? 'disabled' : ''} ${isSuperAdmin ? 'checked disabled' : ''} />
                                    <span class="form-check-label">Select all</span>
                                </label>
                            </td>
                        </tr>
                        <div class="row">${modules}</div>
                    </tbody>
                </table>
            </div>
        </div>`
    );
}

const selectAllPermissions = () => {
    $on($id("select-all-perm"), "click", (e, btn) => {
        const checked = btn.checked

        if ($name('permissions[]').length === 0)
            return

        $loop($name("permissions[]"), module => module.checked = checked)
    })
}

getModules()