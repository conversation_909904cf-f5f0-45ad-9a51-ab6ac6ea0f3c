(function () {
    let categoryOptCache = "";
    let categoryEl;

    const autoSaveNow  = () => {
        if(
            $name("title")[0].value.trim() === ""
            || $name("category")[0].value.trim() === ""
        ) return;

        $debounce(() => submitArticle(false), 1000)
    }

    $('#summary').maxlength({
        warningClass: "badge badge-success",
        limitReachedClass: "badge badge-warning",
        limitExceededClass: "badge badge-danger",
        allowOverMax: true,
    });

    const blog = ckEditor({
        selector: "#blog-editor",
        uploadApi: genApi("/blog/compose/upload-file")
    });

    $on($sel(".save-changes"), "click", e => {
        e.preventDefault()
        submitArticle()
    })

    autoSave()
    handleDeleteArticle()
    loadCategories(true)
    genWithAi()
    loadCollaboratorsNAuthors()
    handleStatus()
    // handleTemplate()
    handleMediaPreview()
    handleBlogURL()
    handleTags()
    handleCalendar()

    // Notify editor about saved blog post content
    $on($win,"beforeunload", (e) => {
        e.preventDefault()
        return "Are you sure you want to leave this page?";
    })

    function submitArticle(popUpOnSave = true) {
        const btn = $sel(".save-changes");

        blog.then(body => {
            ckEditorContent(body, false)

            if($name("body_content")[0].value === "")
                return;

            const id = $name("id")[0]

            let api = (id.value === '' ? 'new' : 'edit');

            if(!popUpOnSave)
                api = "autosave";

            ajax(btn, () =>(
                $curl(genApi("/blog/compose/" + api), {
                    preload: () => preloadBtn(btn),
                    data: btn,
                    headers: apiHeaders()
                })
                    .finally(() => preloadBtn(btn, false))
                    .then(res => {

                        const handleSuccess = () => {
                            if(res.data === "")
                                return;

                            id.value = res.data.postId

                            // if(!res.data.canEditSlug)
                            //     handleBlogURL(false);
                            //
                            // handleBlogURL()

                            handleBlogURL(res.data.canEditSlug)
                        }

                        if(popUpOnSave)
                            return serverResponse(
                                res.code, res.message,
                                handleSuccess,
                                false
                            )

                        handleSuccess()
                    })
            ))
        })
    }

    function handleCalendar() {
        const btn = $sel(".toggle-calendar-info");

        if(!btn) return;

        $sel(".hide-calendar").$on("click", () => btn.click());

        btn.$on("click", e => {
            e.preventDefault();
            const parent = $sel(".calendar-info", btn.closest(".cal-parent"));

            if(parent.$class("has", "d-none"))
                parent.$class("del", "d-none");
            else
                parent.$class("add", "d-none");

            $sela(".copy-btn", parent).$loop(
                copyBtn => copyBtn.$on("click", (e, copy) => {
                    e.preventDefault();
                    $copyToClipboard(copy.closest("details").$sel(".copy-para").innerText)
                })
            )
        })
    }

    function loadCollaboratorsNAuthors(){
        let collabo = $sel(".blog-collaborators-field")
        let newAuthor = $id("new-author")

        $curl(genApi("/blog/utils/collaborators"), {
            headers: apiHeaders()
        })
            .then(resolve => {
                if(resolve.length === 0)
                    return;

                let out = "<option></option>"

                collabo = collabo ? JSON.parse(collabo.innerHTML) : []

                $loop(resolve, (res) => {
                    let selected = "";
                    if(collabo.length > 0 && (res.id === $name("updated_by")[0].value)) return "continue";

                    $loop(collabo, (col, i) => {
                        if(res.id === col) {
                            selected = true
                            delete collabo[i]
                        }
                    })

                    out += `<option value="${res.id}" ${selected ? 'selected' : ''}>${res.name}</option>`
                })

                $html($id("blog-collaborators"), "in", out)

                $('#blog-collaborators').select2()
            })

        $curl(genApi("/blog/utils/writers"), {
            headers: apiHeaders()
        })
            .then(resolve => {
                if(resolve.length === 0)
                    return;

                let authorOut = "<option></option>"

                $loop(resolve, (res) => {
                    authorOut += `<option value="${res.id}" ${res.id === $data(newAuthor,"id") ? 'selected' : ''}>${res.name}</option>`
                })

                $html(newAuthor, "in", authorOut)

                $('#new-author').select2()
            })

    }

    function autoSave(){
        $sela(".autosave-on-input").$loop(auto => auto.$on("input", autoSaveNow))
        $sela(".autosave-on-change").$loop(auto => auto.$on("change", autoSaveNow))

        blog.then((bdy) => {
            bdy.model.document.on("change", autoSaveNow)

            return bdy
        })
    }

    function genWithAi(){
        const btn = $sel(".gen-with-ai");

        if(!btn) return;

        let promptCache = {};

        btn.$on("click", () => {
            const categories = populateCategory(categoryOptCache, false, promptCache.category)

            osModal({
                head: "Generate a Blog Post",
                foot: "",
                closeOnBlur: false,
                body: (
                    `<form>
                        ${getAgentSelect(promptCache.agent)}
                        <div class="selected-agent-box d-none">
                            <div class="mb-5 text-center bg-info-subtle p-4 d-table mx-auto">
                                <p class="agent-personality m-0"></p>
                            </div>
                        
                            <div class="mb-10 form-floating">
                                <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="ai_title" value="${promptCache.title ?? ''}" placeholder="Title" required />
                                <label for="form-b-name" class="form-label mb-3 required">Title</label>
                            </div>
                            
                            <select name="ai_category" data-index="0" id="agent-blog-category" class="form-select mb-10" required>
                                <option value="">Select a Blog Category</option>
                                ${categories}
                            </select>
                            
                            <div class="mb-10">
                                <label for="form-b-keyw" class="form-label mb-3">Target Keywords</label>
                                <textarea class="form-control form-control-solid" id="form-b-keyw" 
                                    placeholder="Each Keyword on a new line" 
                                    name="keywords" style="height: 200px">${promptCache.keywords ?? ''}</textarea>
                            </div>
                            
                            <div class="mb-10">
                                <label for="form-b-desc" class="form-label">Prompt</label>
                                <textarea class="form-control form-control-solid" id="form-b-desc" 
                                    placeholder="Tweak your post even more with fine-grained prompts" 
                                    name="prompt" style="height: 100px">${promptCache.prompt ?? ''}</textarea>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-center">${$facades.submitBtn(
                        "Generate Post",
                        "generate-post-now btn btn-outline btn-outline-dark",
                        "Generating post..."
                    )}</div>
                            </div>
                        </div>
                    </form>`
                ),
                then: () => {
                    const runSelected = (index = null) => {
                        index = index ?? $sel(".lay-ai-agent").selectedIndex - 1;

                        const agent = aiAgentCache[index]

                        const selectedAgentBox = $sel(".selected-agent-box");

                        selectedAgentBox.$sel(".agent-personality").$html(agent.personality)

                        selectedAgentBox.$class("del", "d-none")
                    };

                    if(promptCache.agent)
                        runSelected()

                    $sel(".lay-ai-agent").$on("change", (e, ai) => {

                        if(ai.value === "") return $sel(".selected-agent-box").$class("add", "d-none")

                        runSelected(ai.selectedIndex - 1)
                    })

                    $on($sel(".generate-post-now"), "click", (e, btn) => {
                        e.preventDefault();

                        promptCache = {
                            agent: $name("agent")[0].value,
                            title: $name("ai_title")[0].value,
                            category: $name("ai_category")[0].selectedIndex,
                            keywords: $name("keywords")[0].value,
                            prompt: $name("prompt")[0].value,
                        };

                        ajax(
                            btn,
                            () => $curl(genApi("/ai/actions/blog/gen"), {
                                preload: () => preloadBtn(btn),
                                data: btn,
                                headers: apiHeaders()
                            })
                                .finally(() => preloadBtn(btn, false))
                                .then(res => {
                                    $name("meta_content")[0].value = res.meta_description;
                                    $name("tags")[0].value = res.tags;
                                    blog.then(writer => writer.setData(res.body_content));

                                    categoryEl.val(categoryEl.find('option').eq(promptCache.category).val()).trigger("change");
                                    $name("title")[0].value = promptCache.title;
                                    $name("title")[0].dispatchEvent(new Event('input'))

                                    $name("slug")[0].value = promptCache.title;

                                    CusWind.closeBox()
                                    osNote("Post generated successfully, please review it", "success")
                                })
                        )
                    })
                }
            })
        })
    }

    function populateCategory(resolve, initWithOpt = true, selected = null) {
        let groupSec = {};

        $loop(resolve, group => {
            if(!groupSec[group.collection])
                groupSec[group.collection] = []

            groupSec[group.collection].push(group)
        })

        let out = initWithOpt ? "<option></option>" : ""

        $loop(groupSec, (sec, i) => {
            out += `<optgroup label="${i} - Collection">`;
            $loop(sec, res => {
                out += `<option data-section="${res.collection}" value="${res.id}" ${selected === res.id ? 'selected' : ''} >${res.name} (${res.collection})</option>`
            })
            out += `</optgroup>`
        })

        return out;
    }

    function loadCategories(firstTime = true){
        const refreshBtn = $sel(".refresh-category");

        $curl(genApi("/blog/category/list"), {
            preload: () => preloadBtn(refreshBtn),
            headers: apiHeaders()
        })
            .finally(() => preloadBtn(refreshBtn, false))
            .then(resolve => {
                if(resolve.length === 0)
                    return;

                categoryOptCache = resolve;

                $html($name("category")[0], "in", populateCategory(resolve, true, $name("category")[0].dataset.value))

                categoryEl = $('#blog-category').select2()
            })

        if(!firstTime)
            return;

        $on(refreshBtn, "click", e => {
            e.preventDefault()
            ajax($sel(".refresh-category"), () => loadCategories(false))
        })
    }

    function handleStatus ()  {
        // handle improvement box
        const improvBox = $id('blog-status-improvement');
        const showImprovBox = () => {
            improvBox.parentNode.classList.remove('d-none');
        }

        const hideImprovBox = () => {
            improvBox.parentNode.classList.add('d-none');
        }

        // Handle datepicker
        const datepicker = $id('blog-status-picker');
        const currentDate = new Date()

        // Init flatpickr --- more info: https://flatpickr.js.org/
        $('#blog-status-picker').flatpickr({
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            minDate: currentDate
        });


        const showDatepicker = () => {
            datepicker.parentNode.classList.remove('d-none');
        }

        const hideDatepicker = () => {
            datepicker.parentNode.classList.add('d-none');
        }

        const target = $id('blog-status-color');
        const select = $id('blog-status-select');
        const statusClasses = ['bg-success', 'bg-warning', 'bg-danger', 'bg-info', 'bg-gray-400'];

        const checkCase = (value) => {
            target.classList.remove(...statusClasses);
            hideDatepicker();
            hideImprovBox();

            switch (value) {
                case "PUB__REVISE_POST": {
                    target.classList.add('bg-danger');
                    showImprovBox()
                    break;
                }
                case "PUB__REQUEST_APPROVAL": {
                    target.classList.add('bg-gray-400');
                    break;
                }
                case "PUBLISH": {
                    target.classList.add('bg-success');
                    break;
                }
                case "SCHEDULE": {
                    target.classList.add('bg-info');
                    showDatepicker();
                    break;
                }
                case "DRAFT": {
                    target.classList.add('bg-warning');
                    break;
                }
                default:break;
            }
        }

        checkCase(select.value)

        $(select).on('change', (e) => checkCase(e.target.value));
    }

    function handleTags () {
        new Tagify($sel("#blog-tags"), {

            //TODO: Add a curl call to get last sets of tags to be populated here
            whitelist: ["trending", "buyers guide", "historical fact", "business",],

            originalInputValueFormat: valuesArr => valuesArr.map(item => item.value).join(","),
            dropdown: {
                maxItems: 20,
                classname: "tagify__inline__suggestions",
                enabled: 0,             // <- show suggestions on focus
                closeOnSelect: false,


            },
            callbacks: {
                "change": (e) => {
                    autoSaveNow()
                }
            }
        })
    }

    function handleTemplate(){
        $name("article_template")[0].$on("change", (e, opt) => {
            const selected = opt.options[opt.selectedIndex]
            const previewImg = $sel(".template-preview-image")

            previewImg.href = selected.dataset.img
            previewImg.$sel(".overlay-wrapper").$style(`background-image: url("${selected.dataset.img}")`)
            refreshFsLightbox()
        })
    }

    function handleMediaPreview(){
        const featImg = $name("feat_img")[0];

        $on($name("img_type"), "change", (e, field) => {
            if(field.value === "link")
                return featImg.type = "url"

            featImg.type = "file"
            featImg.accept = "image/*"
        })

        $media({
            srcElement: $sel(".image-link"),
            previewElement: $sel(".preview-image")
        })
    }

    function handleBlogURL(enable = true){
        if($name("can_edit_url")[0].value === '0')
            return

        function handle(e) {
            const value = e.target.value.trim()

            if (value === "")
                return

            $id("blog-url").value = value.replace(/[^\w\s-]/g, '').replace(/\s+/g, '-').toLowerCase()
        }

        if(!enable) {
            $on($name("title")[0], "input", handle, "del")
            $name("can_edit_url")[0].value = "0"
            $id("blog-url").disabled = true;
            return
        }

        $on($name("title")[0], "input", handle)
    }

    function handleDeleteArticle(){
        if(!$sel(".delete-article"))
            return;

        $sela(".delete-article").$loop(btn => {
            btn.$on("click", (e, btn) => {
                e.preventDefault()

                Swal.fire({
                    html: "Are you sure you want to delete this article? It will no longer be accessible and this process is irreversible!",
                    icon: "warning",
                    buttonsStyling: false,
                    confirmButtonText: "Yeah, do it!",
                    cancelButtonText: "Cancel",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                })
                    .then((res) => {
                        if (res.isConfirmed)
                            $curl(genApi("/blog/delete"), {
                                preload: $preloader,
                                data: {
                                    id: btn.dataset.id,
                                },
                                headers: apiHeaders()
                            })
                                .finally(() => $preloader("hide"))
                                .then(res => {
                                    setTimeout(() => $loc.href = $lay.src.base + "blog/", 3000)
                                    osNote(res.message, "success")
                                })
                    })
            })
        })
    }
})();