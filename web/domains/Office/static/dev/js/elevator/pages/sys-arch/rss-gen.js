$sel(".refresh-rss").$on("click", e => {
    e.preventDefault()

    cMsg(
        `<div class="text-danger">Are you sure you want to proceed with the sitemap refresh? This is a resource intensive operation and could cause the whole site to slow down!</div>`,
        () => $curl(genApi("/sys-arch/rss/gen"),{
            preload: $preloader,
            headers: apiHeaders(),
            method: "post",
            type: "html"
        })
            .finally(() => $preloader('hide'))
            .then(res => aMsg(`<div>${res}</div>`))
    )
})