simpleTablePage({
    api : {
        add:    "system/roles/new",
        edit:   "system/roles/edit",
        list:   "system/roles/list",
    },
    form: {
        head: "Add New User Roles",
        body: (info = {}) => (
            `<div class="mb-10 form-floating">
                <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="name" value="${info.name ?? ''}" placeholder="Name" required />
                <label for="form-b-name" class="form-label mb-3 required">Role Name</label>
            </div>
            <div class="mb-10 form-floating">
                <input class="form-control form-control-solid" id="form-b-desc" name="note" value="${info.note ?? ''}" >
                <label for="form-b-desc" class="form-label">Role Note</label>
            </div>
            
            ${permissionTemplate({
                permissions: info.permissions,
                isSuperAdmin: info.isSuper
            })}`
        ),
        then: () => selectAllPermissions()
    },

    entry: {
        row: (row, i) => (
            `<td>${i}</td>
            <td>${row.name} </td>
            <td>${row.note ?? 'No description provided'}</td>
            <td>${row.user}</td>`
        ),
        anchor: {
            id: "roleId",
            name: "name",
        },
    },

    enableDelete: false
})