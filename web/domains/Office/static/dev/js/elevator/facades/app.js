CusWind.config({
    head: "color: var(--bs-body-color);",
    wrapper: "background: var(--bs-app-bg-color); color: var(--bs-body-color);",
    foot: "color: var(--bs-body-color);",
})

function serverResponse(code, message, onSuccess = null, usePromise = true, closeBox = true){

    if (code === 1 || code === 200 || code === 201) {
        if(closeBox)
            CusWind.closeBox()

        const notify = Swal.fire({
            html: message,
            icon: "success",
            buttonsStyling: false,
            confirmButtonText: "Ok, got it!",
            customClass: {
                confirmButton: "btn btn-primary",
                // lift Swal above omjs default modal
                container: "osai-dialogbox__appear",
            }
        })

        if(usePromise)
            return notify.then((res) => onSuccess && res.isConfirmed && onSuccess())

        return onSuccess()
    }

    if (code === 2)
        return Swal.fire({
            html: message,
            icon: "warning",
            buttonsStyling: false,
            confirmButtonText: "Oh, okay",
            customClass: {
                confirmButton: "btn btn-primary",
                // lift Swal above omjs default modal
                container: "osai-dialogbox__appear",
            }
        })

    osNote(message, "warn", {
        position: "center"
    })
}

function preloadBtn(submitButton, showLoader = true){
    if(showLoader) {
        // Show loading indication
        submitButton.setAttribute('data-kt-indicator', 'on');

        // Disable button to avoid multiple click
        submitButton.disabled = true;

        return;
    }

    // Hide loading indication
    submitButton.removeAttribute('data-kt-indicator');

    // Enable button
    submitButton.disabled = false;
}

function ajax(submitButton, xhrRequest){
    if(submitButton.disabled)
        return;

    xhrRequest();
}

function monitorXHR(){
    const origOpen = XMLHttpRequest.prototype.open;

    XMLHttpRequest.prototype.open = function() {
        this.addEventListener('load', function() {
            try {
                const res = JSON.parse(this.responseText)

                if(res.code === 909) {
                    return Swal.fire({
                        html: res.message + ". Click on `Refresh Session` to login on another tab, then return to this tab, to continue your work.",
                        icon: "warning",
                        buttonsStyling: false,
                        showCancelButton: true,
                        confirmButtonText: "Refresh Session",
                        cancelButtonText: "Sign out, I'm done",
                        customClass: {
                            confirmButton: "btn btn-info",
                            cancelButton: "btn btn-danger",
                            // lift Swal above omjs default modal
                            container: "osai-dialogbox__appear",
                        },
                    })
                        .then((res) => {
                            if(res.isConfirmed)
                                return $win.open($lay.src.base + "sign-in", "_blank")

                            location.href = $lay.src.base + "sign-in"
                        })
                }

            } catch (e) { }
        });

        origOpen.apply(this, arguments);
    };
}

function saveCsrf(value){
    $store.setItem("osaitech_app_token", value)
}

function getCsrf(){
    return $store.getItem("osaitech_app_token");
}

function apiHeaders(headers = {}) {
    const defaultHeaders = {
        "X-CSRF-TOKEN": getCsrf(),
    }

    return {...defaultHeaders, ...headers};
}

function genApi(string) {
    return $lay.src.api + "v1/" + DASH_PREFIX + "/" + string.replace(/^\//,"")
}