function ckEditor({selector, uploadApi}) {
    if(!$id("iframe-embed-style"))
        $sel("head").$html("beforeend",
            `<style id="iframe-embed-style">
            .iframely-responsive {
              position: relative;
              top: 0;
              left: 0;
              width: 100%;
              height: 0;
              padding-bottom: 30%;
            }
            .iframely-responsive > * {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              border: 0;
            }
            </style>`
        )

    const markdown = $sel(selector).innerText;
    $sel(selector).$html('del')

    return ClassicEditor
        .create($sel(selector), {
            // extraPlugins: [(editor) => uploadAdapter(editor, uploadApi),]
            extraPlugins: [uploadAdapter, ],
            licenseKey: "GPL"
        })
        .then(editor => {
            editor.sourceElement
                .nextElementSibling
                .querySelector(".ck-editor__main")
                .appendChild(
                    editor.plugins
                        .get('WordCount')
                        .wordCountContainer
                );

            // Convert text that look like heading 2 to heading 2
            editor.conversion.elementToElement( {
                model: 'headingTwo',
                view: 'h2',
                // Convert "heading-like" paragraphs to headings.
                upcastAlso: viewElement => {
                    const fontSize = viewElement.getStyle( 'font-size' );

                    if ( !fontSize ) {
                        return null;
                    }

                    const match = fontSize.match( /(\d+)\s*px/ );

                    if ( !match ) {
                        return null;
                    }

                    const size = Number( match[ 1 ] );

                    if ( size > 26 ) {
                        // Returned value can be an object with the matched properties.
                        // These properties will be "consumed" during the conversion.
                        // See `engine.view.Matcher~MatcherPattern` and `engine.view.Matcher#match` for more details.

                        return { name: true, styles: [ 'font-size' ] };
                    }

                    return null;
                }
            } );

            // convert text that look like heading 3 to heading 3
            editor.conversion.elementToElement( {
                model: 'headingThree',
                view: 'h3',
                // Convert "heading-like" paragraphs to headings.
                upcastAlso: viewElement => {
                    const fontSize = viewElement.getStyle( 'font-size' );

                    if ( !fontSize ) {
                        return null;
                    }

                    const match = fontSize.match( /(\d+)\s*px/ );

                    if ( !match ) {
                        return null;
                    }

                    const size = Number( match[ 1 ] );

                    if ( size < 27 ) {
                        // Returned value can be an object with the matched properties.
                        // These properties will be "consumed" during the conversion.
                        // See `engine.view.Matcher~MatcherPattern` and `engine.view.Matcher#match` for more details.

                        return { name: true, styles: [ 'font-size' ] };
                    }

                    return null;
                }
            } );

            $sel("head").$html(
                "beforeend",
                `<style>
                .ck.ck-sticky-panel__content {
                    border-radius: .85rem .85rem 0 0 !important;
                }
                .ck.ck-word-count {
                    padding: 10px;
                    padding-left: 0;
                    display: flex;
                    gap: 1rem
                }
                .ck.ck-word-count .ck-word-count__words, .ck.ck-word-count .ck-word-count__characters {
                    border: solid 1px var(--ck-color-base-border);
                    padding: 5px;
                    font-weight: 500;
                    border-radius: .85rem;
                }
                </style>`
            );

            return editor
        })
        .then(editor => {
            editor.setData(markdown);

            return editor;
        })
        .catch(error => {
            console.error('Oops, something went wrong!');
            console.error('Please, report the following error on https://github.com/ckeditor/ckeditor5/issues with the build id and the error stack trace:');
            console.warn('Build id: njfw0ualbho4-60wn6fjw0g7s');
            console.error(error);
        })

    function uploadAdapter(editor) {
        editor.plugins
            .get('FileRepository')
            .createUploadAdapter = (loader) => {
            return new UploadToServer(loader, uploadApi, getCsrf());
        };
    }

    function parsePaste(editor) {
        editor.editing.view.document.on('paste', (evt, data) => {
            const htmlContent = data.dataTransfer.getData('text/html')

            if(!htmlContent)
                return;

            // Create a temporary div to parse the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // Function to recursively process nodes
            const processNode = (node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    console.log(node.style)
                    // Remove color and background-color styles
                    // node.style.removeProperty('color');
                    // node.style.removeProperty('background-color');

                    // Process child nodes
                    Array.from(node.childNodes).forEach(processNode);
                }
            };

            // Process all nodes in the pasted content
            processNode(tempDiv);

            // Update the clipboard data with the processed HTML
            data.dataTransfer.setData('text/html', tempDiv.innerHTML);
        }, { priority: 'high' });
    }
}

function ckEditorContent(ckEditorInstance, reportEmptyValue = true) {
    ckEditorInstance.updateSourceElement()

    if(ckEditorInstance.sourceElement.value === "" && reportEmptyValue)
        return osNote("You need to write something on the editor first")
}

class UploadToServer {
    constructor(loader, api, csrf) {
        // The file loader instance to use during the upload.
        this.loader = loader;
        this._serverApi = api;
        this._csrf = csrf;
    }

    upload() {
        return this.loader.file
            .then(file => new Promise((resolve, reject) => {
                this._initRequest();
                this._initListeners(resolve, reject, file);
                this._sendRequest(file);
            }))
            .then(res => {
                if(!res.default)
                    throw res.message

                return res
            });
    }

    abort() {
        if (this.xhr) {
            this.xhr.abort();
        }
    }

    _initRequest() {
        const xhr = this.xhr = new XMLHttpRequest();
        xhr.open('POST', this._serverApi, true);
        xhr.responseType = 'json';
    }

    // Initializes XMLHttpRequest listeners.
    _initListeners(resolve, reject, file) {
        const xhr = this.xhr;
        const loader = this.loader;
        const genericErrorText = `Couldn't upload file: ${file.name}.`;

        xhr.addEventListener('error', () => reject(genericErrorText));
        xhr.addEventListener('abort', () => reject());
        xhr.addEventListener('load', () => {
            const response = xhr.response;

            if (!response || response.error || response.status !== 'success')
                return reject(response && response.error ? response.error.message : genericErrorText);

            resolve({
                default: response.url ?? response.data.url,
                message: response.message ?? response.message
            });
        });

        if (xhr.upload) {
            xhr.upload.addEventListener('progress', evt => {
                if (evt.lengthComputable) {
                    loader.uploadTotal = evt.total;
                    loader.uploaded = evt.loaded;
                }
            });
        }
    }

    _sendRequest(file) {
        const data = new FormData();

        data.append('text_editor_img', file);
        data.append('ack', "true");

        this.xhr.setRequestHeader("X-CSRF-TOKEN", this._csrf)

        this.xhr.send(data);
    }
}

