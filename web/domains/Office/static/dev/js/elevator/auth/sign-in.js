$id("kt_sign_in_submit").disabled = false;

$on($id("kt_sign_in_submit"), 'click', (e, submitButton) => {
    e.preventDefault();

    ajax(submitButton,
        () =>
        $ajax(genApi("/auth/login"), {
            preload: () => preloadBtn(submitButton),
            data: submitButton
        })
            .finally(() => preloadBtn(submitButton, false))
            .then(res => {
                saveCsrf(res.data.token)

                setTimeout(
                    () => location.href = $lay.src.base + ($get("from") ?? ""),
                    3000
                )

                osNote(res.message, "success", {
                    position: "center"
                })
            })
    )
});