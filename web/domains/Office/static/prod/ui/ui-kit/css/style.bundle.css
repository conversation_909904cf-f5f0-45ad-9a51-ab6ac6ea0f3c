@charset "UTF-8";
/*!
 * Bootstrap  v5.3.3 (https://getbootstrap.com/)
 * Copyright 2011-2024 The Bootstrap Authors
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root,
[data-bs-theme=light] {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-black: #000000;
  --bs-white: #ffffff;
  --bs-gray: #78829D;
  --bs-gray-dark: #252F4A;
  --bs-gray-100: #F9F9F9;
  --bs-gray-200: #F1F1F4;
  --bs-gray-300: #DBDFE9;
  --bs-gray-400: #C4CADA;
  --bs-gray-500: #99A1B7;
  --bs-gray-600: #78829D;
  --bs-gray-700: #4B5675;
  --bs-gray-800: #252F4A;
  --bs-gray-900: #071437;
  --bs-light: #F9F9F9;
  --bs-primary: #2AA29E;
  --bs-secondary: #E3EBF6;
  --bs-success: #17C653;
  --bs-info: #7239EA;
  --bs-warning: #FF9355;
  --bs-danger: #F8285A;
  --bs-dark: #1E2129;
  --bs-light-rgb: 249, 249, 249;
  --bs-primary-rgb: 42, 162, 158;
  --bs-secondary-rgb: 227, 235, 246;
  --bs-success-rgb: 23, 198, 83;
  --bs-info-rgb: 114, 57, 234;
  --bs-warning-rgb: 255, 147, 85;
  --bs-danger-rgb: 248, 40, 90;
  --bs-dark-rgb: 30, 33, 41;
  --bs-primary-text-emphasis: #11413f;
  --bs-secondary-text-emphasis: #5b5e62;
  --bs-success-text-emphasis: #094f21;
  --bs-info-text-emphasis: #2e175e;
  --bs-warning-text-emphasis: #663b22;
  --bs-danger-text-emphasis: #631024;
  --bs-light-text-emphasis: #4B5675;
  --bs-dark-text-emphasis: #4B5675;
  --bs-primary-bg-subtle: #d4ecec;
  --bs-secondary-bg-subtle: #f9fbfd;
  --bs-success-bg-subtle: #d1f4dd;
  --bs-info-bg-subtle: #e3d7fb;
  --bs-warning-bg-subtle: #ffe9dd;
  --bs-danger-bg-subtle: #fed4de;
  --bs-light-bg-subtle: #fcfcfc;
  --bs-dark-bg-subtle: #C4CADA;
  --bs-primary-border-subtle: #aadad8;
  --bs-secondary-border-subtle: #f4f7fb;
  --bs-success-border-subtle: #a2e8ba;
  --bs-info-border-subtle: #c7b0f7;
  --bs-warning-border-subtle: #ffd4bb;
  --bs-danger-border-subtle: #fca9bd;
  --bs-light-border-subtle: #F1F1F4;
  --bs-dark-border-subtle: #99A1B7;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-font-sans-serif: Inter, Helvetica, "sans-serif";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #071437;
  --bs-body-color-rgb: 7, 20, 55;
  --bs-body-bg: #ffffff;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-emphasis-color: #000000;
  --bs-emphasis-color-rgb: 0, 0, 0;
  --bs-secondary-color: rgba(7, 20, 55, 0.75);
  --bs-secondary-color-rgb: 7, 20, 55;
  --bs-secondary-bg: #F1F1F4;
  --bs-secondary-bg-rgb: 241, 241, 244;
  --bs-tertiary-color: rgba(7, 20, 55, 0.5);
  --bs-tertiary-color-rgb: 7, 20, 55;
  --bs-tertiary-bg: #F9F9F9;
  --bs-tertiary-bg-rgb: 249, 249, 249;
  --bs-heading-color: #071437;
  --bs-link-color: #2AA29E;
  --bs-link-color-rgb: 42, 162, 158;
  --bs-link-decoration: none;
  --bs-link-hover-color: #1F8280;
  --bs-link-hover-color-rgb: 31, 130, 128;
  --bs-link-hover-decoration: none;
  --bs-code-color: #b93993;
  --bs-highlight-color: #071437;
  --bs-highlight-bg: #fff3cd;
  --bs-border-width: 1px;
  --bs-border-style: solid;
  --bs-border-color: #F1F1F4;
  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
  --bs-border-radius: 0.85rem;
  --bs-border-radius-sm: 0.75rem;
  --bs-border-radius-lg: 1rem;
  --bs-border-radius-xl: 1.35rem;
  --bs-border-radius-xxl: 2rem;
  --bs-border-radius-2xl: var(--bs-border-radius-xxl);
  --bs-border-radius-pill: 50rem;
  --bs-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  --bs-box-shadow-sm: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.05);
  --bs-box-shadow-lg: 0 1rem 2rem 1rem rgba(0, 0, 0, 0.1);
  --bs-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --bs-focus-ring-width: 0.25rem;
  --bs-focus-ring-opacity: 0.25;
  --bs-focus-ring-color: rgba(42, 162, 158, 0.25);
  --bs-form-valid-color: #17C653;
  --bs-form-valid-border-color: #17C653;
  --bs-form-invalid-color: #F8285A;
  --bs-form-invalid-border-color: #F8285A;
}

[data-bs-theme=dark] {
  color-scheme: dark;
  --bs-body-color: #F5F5F5;
  --bs-body-color-rgb: 245, 245, 245;
  --bs-body-bg: #15171C;
  --bs-body-bg-rgb: 21, 23, 28;
  --bs-emphasis-color: #ffffff;
  --bs-emphasis-color-rgb: 255, 255, 255;
  --bs-secondary-color: rgba(245, 245, 245, 0.75);
  --bs-secondary-color-rgb: 245, 245, 245;
  --bs-secondary-bg: #252F4A;
  --bs-secondary-bg-rgb: 37, 47, 74;
  --bs-tertiary-color: rgba(245, 245, 245, 0.5);
  --bs-tertiary-color-rgb: 245, 245, 245;
  --bs-tertiary-bg: #162241;
  --bs-tertiary-bg-rgb: 22, 34, 65;
  --bs-primary-text-emphasis: #7fc7c5;
  --bs-secondary-text-emphasis: #eef3fa;
  --bs-success-text-emphasis: #74dd98;
  --bs-info-text-emphasis: #aa88f2;
  --bs-warning-text-emphasis: #ffbe99;
  --bs-danger-text-emphasis: #fb7e9c;
  --bs-light-text-emphasis: #F9F9F9;
  --bs-dark-text-emphasis: #DBDFE9;
  --bs-primary-bg-subtle: #082020;
  --bs-secondary-bg-subtle: #2d2f31;
  --bs-success-bg-subtle: #052811;
  --bs-info-bg-subtle: #170b2f;
  --bs-warning-bg-subtle: #331d11;
  --bs-danger-bg-subtle: #320812;
  --bs-light-bg-subtle: #252F4A;
  --bs-dark-bg-subtle: #131825;
  --bs-primary-border-subtle: #19615f;
  --bs-secondary-border-subtle: #888d94;
  --bs-success-border-subtle: #0e7732;
  --bs-info-border-subtle: #44228c;
  --bs-warning-border-subtle: #995833;
  --bs-danger-border-subtle: #951836;
  --bs-light-border-subtle: #4B5675;
  --bs-dark-border-subtle: #252F4A;
  --bs-heading-color: #F5F5F5;
  --bs-link-color: #1F8280;
  --bs-link-hover-color: #4c9b99;
  --bs-link-color-rgb: 31, 130, 128;
  --bs-link-hover-color-rgb: 76, 155, 153;
  --bs-code-color: #b93993;
  --bs-highlight-color: #F5F5F5;
  --bs-highlight-bg: #664d03;
  --bs-border-color: #26272F;
  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);
  --bs-form-valid-color: #75b798;
  --bs-form-valid-border-color: #75b798;
  --bs-form-invalid-color: #ea868f;
  --bs-form-invalid-border-color: #ea868f;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

hr {
  margin: 1rem 0;
  color: inherit;
  border: 0;
  border-top: var(--bs-border-width) solid;
  opacity: 0.25;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
  color: var(--bs-heading-color);
}

h1, .h1 {
  font-size: calc(1.3rem + 0.6vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 1.75rem;
  }
}

h2, .h2 {
  font-size: calc(1.275rem + 0.3vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 1.5rem;
  }
}

h3, .h3 {
  font-size: calc(1.26rem + 0.12vw);
}
@media (min-width: 1200px) {
  h3, .h3 {
    font-size: 1.35rem;
  }
}

h4, .h4 {
  font-size: 1.25rem;
}

h5, .h5 {
  font-size: 1.15rem;
}

h6, .h6 {
  font-size: 1.075rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title] {
  text-decoration: underline dotted;
  cursor: help;
  text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-left: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 600;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: 700;
}

small, .small {
  font-size: 0.875em;
}

mark, .mark {
  padding: 0.1875em;
  color: var(--bs-highlight-color);
  background-color: var(--bs-highlight-bg);
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
  text-decoration: none;
}
a:hover {
  --bs-link-color-rgb: var(--bs-link-hover-color-rgb);
  text-decoration: none;
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 1rem;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 1rem;
  color: var(--bs-code-color);
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.1875rem 0.375rem;
  font-size: 1rem;
  color: var(--bs-body-bg);
  background-color: var(--bs-body-color);
  border-radius: 0.75rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #99A1B7;
  text-align: left;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
  display: none !important;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::file-selector-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.container {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
   .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
   .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
   .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
   .container {
    max-width: 1140px;
  }
}
@media (min-width: 1400px) {
   .container {
    max-width: 1320px;
  }
}
:root {
  --bs-breakpoint-xs: 0;
  --bs-breakpoint-sm: 576px;
  --bs-breakpoint-md: 768px;
  --bs-breakpoint-lg: 992px;
  --bs-breakpoint-xl: 1200px;
  --bs-breakpoint-xxl: 1400px;
}

.row {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
  margin-left: calc(-0.5 * var(--bs-gutter-x));
}
.row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

.col {
  flex: 1 0 0%;
}

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-6 {
  flex: 0 0 auto;
  width: 50%;
}

.col-12 {
  flex: 0 0 auto;
  width: 100%;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.g-1 {
  --bs-gutter-x: 0.25rem;
}

.g-1 {
  --bs-gutter-y: 0.25rem;
}

.g-2 {
  --bs-gutter-x: 0.5rem;
}

.g-2 {
  --bs-gutter-y: 0.5rem;
}

.g-3 {
  --bs-gutter-x: 0.75rem;
}

.g-3 {
  --bs-gutter-y: 0.75rem;
}


.gy-4 {
  --bs-gutter-y: 1rem;
}

.g-5 {
  --bs-gutter-x: 1.25rem;
}

.g-5,
.gy-5 {
  --bs-gutter-y: 1.25rem;
}

.g-6 {
  --bs-gutter-x: 1.5rem;
}

.g-6,
.gy-6 {
  --bs-gutter-y: 1.5rem;
}

@media (min-width: 576px) {
  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
}
@media (min-width: 992px) {
  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .g-lg-9 {
    --bs-gutter-x: 2.25rem;
  }
  .g-lg-9 {
    --bs-gutter-y: 2.25rem;
  }
}
@media (min-width: 1200px) {
  .g-xl-9 {
    --bs-gutter-x: 2.25rem;
  }
  .g-xl-9 {
    --bs-gutter-y: 2.25rem;
  }
}
@media (min-width: 1400px) {
  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .g-xxl-10 {
    --bs-gutter-x: 2.5rem;
  }
  .g-xxl-10 {
    --bs-gutter-y: 2.5rem;
  }
}
.table {
  --bs-table-color-type: initial;
  --bs-table-bg-type: initial;
  --bs-table-color-state: initial;
  --bs-table-bg-state: initial;
  --bs-table-color: var(--bs-body-color);
  --bs-table-bg: transparent;
  --bs-table-border-color: var(--bs-border-color);
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: var(--bs-body-color);
  --bs-table-striped-bg: rgba(var(--bs-gray-100-rgb), 0.75);
  --bs-table-active-color: var(--bs-body-color);
  --bs-table-active-bg: var(--bs-gray-100);
  --bs-table-hover-color: var(--bs-body-color);
  --bs-table-hover-bg: var(--bs-gray-100);
  width: 100%;
  margin-bottom: 1rem;
  vertical-align: top;
  border-color: var(--bs-table-border-color);
}
.table > :not(caption) > * > * {
  padding: 0.75rem 0.75rem;
  color: var(--bs-table-color-state, var(--bs-table-color-type, var(--bs-table-color)));
  background-color: var(--bs-table-bg);
  border-bottom-width: 1px;
  box-shadow: inset 0 0 0 9999px var(--bs-table-bg-state, var(--bs-table-bg-type, var(--bs-table-accent-bg)));
}
.table > tbody {
  vertical-align: inherit;
}
.table > thead {
  vertical-align: bottom;
}

.table-bordered > :not(caption) > * {
  border-width: 1px 0;
}
.table-bordered > :not(caption) > * > * {
  border-width: 0 1px;
}

.table-active {
  --bs-table-color-state: var(--bs-table-active-color);
  --bs-table-bg-state: var(--bs-table-active-bg);
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
.form-label {
  margin-bottom: 0.5rem;
  font-size: 1.05rem;
  font-weight: 500;
  color: var(--bs-gray-800);
}

.col-form-label {
  padding-top: calc(0.775rem + 1px);
  padding-bottom: calc(0.775rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  font-weight: 500;
  line-height: 1.5;
  color: var(--bs-gray-800);
}

.form-text {
  margin-top: 0.5rem;
  font-size: 0.95rem;
  color: var(--bs-text-muted);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.775rem 1rem;
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1.5;
  color: var(--bs-gray-700);
  appearance: none;
  background-color: var(--bs-body-bg);
  background-clip: padding-box;
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.85rem;
  box-shadow: false;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control[type=file] {
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  color: var(--bs-gray-700);
  background-color: var(--bs-body-bg);
  border-color: var(--bs-gray-400);
  outline: 0;
  box-shadow: false, 0 0 0 0.25rem rgba(42, 162, 158, 0.25);
}
.form-control::-webkit-date-and-time-value {
  min-width: 85px;
  height: 1.5em;
  margin: 0;
}
.form-control::-webkit-datetime-edit {
  display: block;
  padding: 0;
}
.form-control::placeholder {
  color: var(--bs-gray-500);
  opacity: 1;
}
.form-control:disabled {
  color: var(--bs-gray-500);
  background-color: var(--bs-gray-200);
  border-color: var(--bs-gray-300);
  opacity: 1;
}
.form-control::file-selector-button {
  padding: 0.775rem 1rem;
  margin: -0.775rem -1rem;
  margin-inline-end: 1rem;
  color: var(--bs-gray-700);
  background-color: var(--bs-gray-100);
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: shade-color(var(--bs-gray-100), 5%);
}

.form-control-sm {
  min-height: calc(1.5em + 1.1rem + 2px);
  padding: 0.55rem 0.75rem;
  font-size: 0.95rem;
  border-radius: 0.75rem;
}
.form-control-sm::file-selector-button {
  padding: 0.55rem 0.75rem;
  margin: -0.55rem -0.75rem;
  margin-inline-end: 0.75rem;
}

.form-control-lg {
  min-height: calc(1.5em + 1.65rem + 2px);
  padding: 0.825rem 1.5rem;
  font-size: 1.15rem;
  border-radius: 1rem;
}
.form-control-lg::file-selector-button {
  padding: 0.825rem 1.5rem;
  margin: -0.825rem -1.5rem;
  margin-inline-end: 1.5rem;
}

textarea.form-control {
  min-height: calc(1.5em + 1.55rem + 2px);
}
textarea.form-control-sm {
  min-height: calc(1.5em + 1.1rem + 2px);
}
textarea.form-control-lg {
  min-height: calc(1.5em + 1.65rem + 2px);
}

.form-select {
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%2378829D' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  display: block;
  width: 100%;
  padding: 0.775rem 3rem 0.775rem 1rem;
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1.5;
  color: var(--bs-gray-700);
  appearance: none;
  background-color: var(--bs-body-bg);
  background-image: var(--bs-form-select-bg-img), var(--bs-form-select-bg-icon, none);
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 16px 12px;
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.85rem;
  box-shadow: false;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    transition: none;
  }
}
.form-select:focus {
  border-color: var(--bs-gray-400);
  outline: 0;
  box-shadow: false, 0 0 0 0.25rem rgba(var(--bs-component-active-bg), 0.25);
}
.form-select[multiple], .form-select[size]:not([size="1"]) {
  padding-right: 1rem;
  background-image: none;
}
.form-select:disabled {
  color: var(--bs-gray-500);
  background-color: var(--bs-gray-200);
  border-color: var(--bs-gray-300);
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 var(--bs-gray-700);
}

.form-select-sm {
  padding-top: 0.55rem;
  padding-bottom: 0.55rem;
  padding-left: 0.75rem;
  font-size: 0.95rem;
  border-radius: 0.75rem;
}

[data-bs-theme=dark] .form-select {
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23808290' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}

.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 2.25rem;
  margin-bottom: 0.125rem;
}
.form-check .form-check-input {
  float: left;
  margin-left: -2.25rem;
}

.form-check-input {
  --bs-form-check-bg: transparent;
  flex-shrink: 0;
  width: 1.75rem;
  height: 1.75rem;
  margin-top: -0.125rem;
  vertical-align: top;
  appearance: none;
  background-color: var(--bs-form-check-bg);
  background-image: var(--bs-form-check-bg-image);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid var(--bs-gray-300);
  print-color-adjust: exact;
}
.form-check-input[type=checkbox] {
  border-radius: 0.45em;
}
.form-check-input[type=radio] {
  border-radius: 50%;
}
.form-check-input:active {
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: var(--bs-gray-400);
  outline: 0;
  box-shadow: none;
}
.form-check-input:checked {
  background-color: #2AA29E;
  border-color: #2AA29E;
}
.form-check-input:checked[type=checkbox] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 13 11' width='13' height='11' fill='none'%3e%3cpath d='M11.0426 1.02893C11.3258 0.695792 11.8254 0.655283 12.1585 0.938451C12.4917 1.22162 12.5322 1.72124 12.249 2.05437L5.51985 9.97104C5.23224 10.3094 4.72261 10.3451 4.3907 10.05L0.828197 6.88335C0.50141 6.59288 0.471975 6.09249 0.762452 5.7657C1.05293 5.43891 1.55332 5.40948 1.88011 5.69995L4.83765 8.32889L11.0426 1.02893Z' fill='%23ffffff'/%3e%3c/svg%3e");
}
.form-check-input:checked[type=radio] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e");
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: #2AA29E;
  border-color: #2AA29E;
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  cursor: default;
  opacity: 0.5;
}

.form-check-label {
  color: var(--bs-gray-500);
}

.form-switch {
  padding-left: 3.75rem;
}
.form-switch .form-check-input {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  width: 3.25rem;
  margin-left: -3.75rem;
  background-image: var(--bs-form-switch-bg);
  background-position: left center;
  border-radius: 3.25rem;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-position: right center;
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
}

[data-bs-theme=dark] .form-switch .form-check-input:not(:checked):not(:focus) {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255, 255, 255, 0.25%29'/%3e%3c/svg%3e");
}

.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.75rem + 2px);
  min-height: calc(3.75rem + 2px);
  line-height: 1.25;
}
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  height: 100%;
  padding: 1rem 1rem;
  overflow: hidden;
  text-align: start;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    transition: none;
  }
}
.form-floating > .form-control {
  padding: 1rem 1rem;
}
.form-floating > .form-control::placeholder {
  color: transparent;
}
.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.85rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill {
  padding-top: 1.85rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-select {
  padding-top: 1.85rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  color: rgba(var(--bs-body-color-rgb), 0.65);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:focus ~ label::after,
.form-floating > .form-control:not(:placeholder-shown) ~ label::after,
.form-floating > .form-select ~ label::after {
  position: absolute;
  inset: 1rem 0.5rem;
  z-index: -1;
  height: 1.5em;
  content: "";
  background-color: var(--bs-body-bg);
  border-radius: 0.85rem;
}
.form-floating > .form-control:-webkit-autofill ~ label {
  color: rgba(var(--bs-body-color-rgb), 0.65);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > :disabled ~ label,
.form-floating > .form-control:disabled ~ label {
  color: #78829D;
}
.form-floating > :disabled ~ label::after,
.form-floating > .form-control:disabled ~ label::after {
  background-color: var(--bs-gray-200);
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select,
.input-group > .form-floating {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus,
.input-group > .form-floating:focus-within {
  z-index: 5;
}
.input-group .btn {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus {
  z-index: 5;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.775rem 1rem;
  font-size: 1.1rem;
  font-weight: 500;
  line-height: 1.5;
  color: var(--bs-gray-700);
  text-align: center;
  white-space: nowrap;
  background-color: var(--bs-gray-100);
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.85rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3),
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control,
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: calc(1px * -1);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > .form-floating:not(:first-child) > .form-control,
.input-group > .form-floating:not(:first-child) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn {
  --bs-btn-padding-x: 1.5rem;
  --bs-btn-padding-y: 0.775rem;
  --bs-btn-font-family: ;
  --bs-btn-font-size: 1.1rem;
  --bs-btn-font-weight: 500;
  --bs-btn-line-height: 1.5;
  --bs-btn-color: var(--bs-body-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-width: 1px;
  --bs-btn-border-color: transparent;
  --bs-btn-border-radius: 0.85rem;
  --bs-btn-hover-border-color: transparent;
  --bs-btn-box-shadow: none;
  --bs-btn-disabled-opacity: 0.65;
  --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);
  display: inline-block;
  padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
  font-family: var(--bs-btn-font-family);
  font-size: var(--bs-btn-font-size);
  font-weight: var(--bs-btn-font-weight);
  line-height: var(--bs-btn-line-height);
  color: var(--bs-btn-color);
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
  border-radius: var(--bs-btn-border-radius);
  background-color: var(--bs-btn-bg);
  box-shadow: var(--bs-btn-box-shadow);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}
.btn:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}
.btn:focus-visible {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-box-shadow), var(--bs-btn-focus-box-shadow);
}
 :not(.btn-check) + .btn:active, .btn:first-child:active, .btn.active, .btn.show {
  color: var(--bs-btn-active-color);
  background-color: var(--bs-btn-active-bg);
  border-color: var(--bs-btn-active-border-color);
  box-shadow: var(--bs-btn-active-shadow);
}
 :not(.btn-check) + .btn:active:focus-visible, .btn:first-child:active:focus-visible, .btn.active:focus-visible, .btn.show:focus-visible {
  box-shadow: var(--bs-btn-active-shadow), var(--bs-btn-focus-box-shadow);
}
.btn:disabled, .btn.disabled, fieldset:disabled .btn {
  color: var(--bs-btn-disabled-color);
  pointer-events: none;
  background-color: var(--bs-btn-disabled-bg);
  border-color: var(--bs-btn-disabled-border-color);
  opacity: var(--bs-btn-disabled-opacity);
  box-shadow: none;
}

.btn-light {
  --bs-btn-color: #000000;
  --bs-btn-bg: #F9F9F9;
  --bs-btn-border-color: #F9F9F9;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #d4d4d4;
  --bs-btn-hover-border-color: #c7c7c7;
  --bs-btn-focus-shadow-rgb: 212, 212, 212;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #c7c7c7;
  --bs-btn-active-border-color: #bbbbbb;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #F9F9F9;
  --bs-btn-disabled-border-color: #F9F9F9;
}

.btn-primary {
  --bs-btn-color: #000000;
  --bs-btn-bg: #2AA29E;
  --bs-btn-border-color: #2AA29E;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #4ab0ad;
  --bs-btn-hover-border-color: #3faba8;
  --bs-btn-focus-shadow-rgb: 36, 138, 134;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #55b5b1;
  --bs-btn-active-border-color: #3faba8;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #2AA29E;
  --bs-btn-disabled-border-color: #2AA29E;
}

.btn-secondary {
  --bs-btn-color: #000000;
  --bs-btn-bg: #E3EBF6;
  --bs-btn-border-color: #E3EBF6;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #e7eef7;
  --bs-btn-hover-border-color: #e6edf7;
  --bs-btn-focus-shadow-rgb: 193, 200, 209;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #e9eff8;
  --bs-btn-active-border-color: #e6edf7;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #E3EBF6;
  --bs-btn-disabled-border-color: #E3EBF6;
}

.btn-success {
  --bs-btn-color: #000000;
  --bs-btn-bg: #17C653;
  --bs-btn-border-color: #17C653;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #3acf6d;
  --bs-btn-hover-border-color: #2ecc64;
  --bs-btn-focus-shadow-rgb: 20, 168, 71;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #45d175;
  --bs-btn-active-border-color: #2ecc64;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #17C653;
  --bs-btn-disabled-border-color: #17C653;
}

.btn-info {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #7239EA;
  --bs-btn-border-color: #7239EA;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #6130c7;
  --bs-btn-hover-border-color: #5b2ebb;
  --bs-btn-focus-shadow-rgb: 135, 87, 237;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #5b2ebb;
  --bs-btn-active-border-color: #562bb0;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #7239EA;
  --bs-btn-disabled-border-color: #7239EA;
}

.btn-warning {
  --bs-btn-color: #000000;
  --bs-btn-bg: #FF9355;
  --bs-btn-border-color: #FF9355;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #ffa36f;
  --bs-btn-hover-border-color: #ff9e66;
  --bs-btn-focus-shadow-rgb: 217, 125, 72;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #ffa977;
  --bs-btn-active-border-color: #ff9e66;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #FF9355;
  --bs-btn-disabled-border-color: #FF9355;
}

.btn-danger {
  --bs-btn-color: #000000;
  --bs-btn-bg: #F8285A;
  --bs-btn-border-color: #F8285A;
  --bs-btn-hover-color: #000000;
  --bs-btn-hover-bg: #f94873;
  --bs-btn-hover-border-color: #f93e6b;
  --bs-btn-focus-shadow-rgb: 211, 34, 77;
  --bs-btn-active-color: #000000;
  --bs-btn-active-bg: #f9537b;
  --bs-btn-active-border-color: #f93e6b;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #000000;
  --bs-btn-disabled-bg: #F8285A;
  --bs-btn-disabled-border-color: #F8285A;
}

.btn-dark {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #1E2129;
  --bs-btn-border-color: #1E2129;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #404249;
  --bs-btn-hover-border-color: #35373e;
  --bs-btn-focus-shadow-rgb: 64, 66, 73;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #4b4d54;
  --bs-btn-active-border-color: #35373e;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #1E2129;
  --bs-btn-disabled-border-color: #1E2129;
}

.btn-outline-info {
  --bs-btn-color: #7239EA;
  --bs-btn-border-color: #7239EA;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #7239EA;
  --bs-btn-hover-border-color: #7239EA;
  --bs-btn-focus-shadow-rgb: 114, 57, 234;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #7239EA;
  --bs-btn-active-border-color: #7239EA;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #7239EA;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #7239EA;
  --bs-gradient: none;
}

.btn-outline-dark {
  --bs-btn-color: #1E2129;
  --bs-btn-border-color: #1E2129;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #1E2129;
  --bs-btn-hover-border-color: #1E2129;
  --bs-btn-focus-shadow-rgb: 30, 33, 41;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #1E2129;
  --bs-btn-active-border-color: #1E2129;
  --bs-btn-active-shadow: none;
  --bs-btn-disabled-color: #1E2129;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #1E2129;
  --bs-gradient: none;
}

.btn-lg {
  --bs-btn-padding-y: 0.825rem;
  --bs-btn-padding-x: 1.75rem;
  --bs-btn-font-size: 1.15rem;
  --bs-btn-border-radius: 1rem;
}

.btn-sm {
  --bs-btn-padding-y: 0.55rem;
  --bs-btn-padding-x: 1rem;
  --bs-btn-font-size: 0.95rem;
  --bs-btn-border-radius: 0.75rem;
}

.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    transition: none;
  }
}

.dropup,
.dropend,
.dropdown,
.dropstart,
.dropup-center,
.dropdown-center {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: 10rem;
  --bs-dropdown-padding-x: 0;
  --bs-dropdown-padding-y: 0.5rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size: 1rem;
  --bs-dropdown-color: var(--bs-body-color);
  --bs-dropdown-bg: var(--bs-body-bg);
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-border-radius: 0.85rem;
  --bs-dropdown-border-width: 0rem;
  --bs-dropdown-inner-border-radius: calc(0.85rem - 0rem);
  --bs-dropdown-divider-bg: var(--bs-gray-100);
  --bs-dropdown-divider-margin-y: 0.5rem;
  --bs-dropdown-box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  --bs-dropdown-link-color: var(--bs-gray-900);
  --bs-dropdown-link-hover-color: var(--bs-gray-900);
  --bs-dropdown-link-hover-bg: var(--bs-tertiary-bg);
  --bs-dropdown-link-active-color: var(--bs-component-hover-color);
  --bs-dropdown-link-active-bg: var(--bs-component-hover-bg);
  --bs-dropdown-link-disabled-color: var(--bs-gray-500);
  --bs-dropdown-item-padding-x: 0.85rem;
  --bs-dropdown-item-padding-y: 0.65rem;
  --bs-dropdown-header-color: var(--bs-gray-600);
  --bs-dropdown-header-padding-x: 0.85rem;
  --bs-dropdown-header-padding-y: 0.5rem;
  position: absolute;
  z-index: var(--bs-dropdown-zindex);
  display: none;
  min-width: var(--bs-dropdown-min-width);
  padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
  margin: 0;
  font-size: var(--bs-dropdown-font-size);
  color: var(--bs-dropdown-color);
  text-align: left;
  list-style: none;
  background-color: var(--bs-dropdown-bg);
  background-clip: padding-box;
  border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
  border-radius: var(--bs-dropdown-border-radius);
  box-shadow: var(--bs-dropdown-box-shadow);
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropend .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.dropend .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}
.dropstart .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropstart .dropdown-toggle::after {
  display: none;
}
.dropstart .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.dropstart .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-divider {
  height: 0;
  margin: var(--bs-dropdown-divider-margin-y) 0;
  overflow: hidden;
  border-top: 1px solid var(--bs-dropdown-divider-bg);
  opacity: 1;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  clear: both;
  font-weight: 400;
  color: var(--bs-dropdown-link-color);
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  border-radius: var(--bs-dropdown-item-border-radius, 0);
}
.dropdown-item:hover, .dropdown-item:focus {
  color: var(--bs-dropdown-link-hover-color);
  background-color: var(--bs-dropdown-link-hover-bg);
}
.dropdown-item.active, .dropdown-item:active {
  color: var(--bs-dropdown-link-active-color);
  text-decoration: none;
  background-color: var(--bs-dropdown-link-active-bg);
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: var(--bs-dropdown-link-disabled-color);
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.btn-group {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn {
  position: relative;
  flex: 1 1 auto;
}

.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active {
  z-index: 1;
}

.btn-group {
  border-radius: 0.85rem;
}
.btn-group > :not(.btn-check:first-child) + .btn,
.btn-group > .btn-group:not(:first-child) {
  margin-left: calc(1px * -1);
}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn.dropdown-toggle-split:first-child,
.btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn:nth-child(n+3),
.btn-group > :not(.btn-check) + .btn,
.btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 1.125rem;
  padding-left: 1.125rem;
}
.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle-split::before {
  margin-right: 0;
}

.btn-sm + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.btn-lg + .dropdown-toggle-split {
  padding-right: 1.3125rem;
  padding-left: 1.3125rem;
}

.btn-group.show .dropdown-toggle {
  box-shadow: none;
}

.nav {
  --bs-nav-link-padding-x: 1rem;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-link-color);
  --bs-nav-link-hover-color: var(--bs-link-hover-color);
  --bs-nav-link-disabled-color: var(--bs-secondary-color);
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  font-size: var(--bs-nav-link-font-size);
  font-weight: var(--bs-nav-link-font-weight);
  color: var(--bs-nav-link-color);
  background: none;
  border: 0;
  transition: color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: var(--bs-nav-link-hover-color);
}
.nav-link:focus-visible {
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(42, 162, 158, 0.25);
}
.nav-link.disabled, .nav-link:disabled {
  color: var(--bs-nav-link-disabled-color);
  pointer-events: none;
  cursor: default;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  --bs-navbar-padding-x: 0;
  --bs-navbar-padding-y: 0.5rem;
  --bs-navbar-color: rgba(var(--bs-emphasis-color-rgb), 0.65);
  --bs-navbar-hover-color: rgba(var(--bs-emphasis-color-rgb), 0.8);
  --bs-navbar-disabled-color: rgba(var(--bs-emphasis-color-rgb), 0.3);
  --bs-navbar-active-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-brand-padding-y: 0.44375rem;
  --bs-navbar-brand-margin-end: 1rem;
  --bs-navbar-brand-font-size: 1.075rem;
  --bs-navbar-brand-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-brand-hover-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-nav-link-padding-x: 0.5rem;
  --bs-navbar-toggler-padding-y: 0.25rem;
  --bs-navbar-toggler-padding-x: 0.75rem;
  --bs-navbar-toggler-font-size: 1.075rem;
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%287, 20, 55, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  --bs-navbar-toggler-border-color: rgba(var(--bs-emphasis-color-rgb), 0.15);
  --bs-navbar-toggler-border-radius: 0.85rem;
  --bs-navbar-toggler-focus-width: 0.25rem;
  --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-navbar-padding-y) var(--bs-navbar-padding-x);
}
.navbar > .container {
  display: flex;
  flex-wrap: inherit;
  align-items: center;
  justify-content: space-between;
}

.navbar-nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-navbar-color);
  --bs-nav-link-hover-color: var(--bs-navbar-hover-color);
  --bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link.active, .navbar-nav .nav-link.show {
  color: var(--bs-navbar-active-color);
}
.navbar-nav .dropdown-menu {
  position: static;
}


.navbar[data-bs-theme=dark] {
  --bs-navbar-color: rgba(255, 255, 255, 0.55);
  --bs-navbar-hover-color: rgba(255, 255, 255, 0.75);
  --bs-navbar-disabled-color: rgba(255, 255, 255, 0.25);
  --bs-navbar-active-color: #ffffff;
  --bs-navbar-brand-color: #ffffff;
  --bs-navbar-brand-hover-color: #ffffff;
  --bs-navbar-toggler-border-color: rgba(255, 255, 255, 0.1);
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.card {
  --bs-card-spacer-y: 1rem;
  --bs-card-spacer-x: 1rem;
  --bs-card-title-spacer-y: 0.5rem;
  --bs-card-title-color: var(--bs-gray-900);
  --bs-card-subtitle-color: ;
  --bs-card-border-width: 1px;
  --bs-card-border-color: #F1F1F4;
  --bs-card-border-radius: 1rem;
  --bs-card-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --bs-card-inner-border-radius: calc(1rem - 1px);
  --bs-card-cap-padding-y: 0.5rem;
  --bs-card-cap-padding-x: 1rem;
  --bs-card-cap-bg: transparent;
  --bs-card-cap-color: ;
  --bs-card-height: ;
  --bs-card-color: ;
  --bs-card-bg: var(--bs-body-bg);
  --bs-card-img-overlay-padding: 1rem;
  --bs-card-group-margin: 0.75rem;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: var(--bs-card-height);
  color: var(--bs-body-color);
  word-wrap: break-word;
  background-color: var(--bs-card-bg);
  background-clip: border-box;
  border: var(--bs-card-border-width) solid var(--bs-card-border-color);
  border-radius: var(--bs-card-border-radius);
  box-shadow: var(--bs-card-box-shadow);
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
  border-bottom-left-radius: var(--bs-card-inner-border-radius);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  flex: 1 1 auto;
  padding: var(--bs-card-spacer-y) var(--bs-card-spacer-x);
  color: var(--bs-card-color);
}

.card-title {
  margin-bottom: var(--bs-card-title-spacer-y);
  color: var(--bs-card-title-color);
}

.card-header {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  margin-bottom: 0;
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-bottom: var(--bs-card-border-width) solid var(--bs-card-border-color);
}
.card-header:first-child {
  border-radius: var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius) 0 0;
}

.card-footer {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-top: var(--bs-card-border-width) solid var(--bs-card-border-color);
}
.card-footer:last-child {
  border-radius: 0 0 var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius);
}

.accordion {
  --bs-accordion-color: var(--bs-body-color);
  --bs-accordion-bg: var(--bs-body-bg);
  --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: var(--bs-border-color);
  --bs-accordion-border-width: var(--bs-border-width);
  --bs-accordion-border-radius: 0.85rem;
  --bs-accordion-inner-border-radius: calc(0.85rem - (var(--bs-border-width)));
  --bs-accordion-btn-padding-x: 1.5rem;
  --bs-accordion-btn-padding-y: 1.5rem;
  --bs-accordion-btn-color: var(--bs-body-color);
  --bs-accordion-btn-bg: var(--bs-body-bg);
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23071437'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-icon-width: 1.15rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%232AA29E'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-focus-box-shadow: none;
  --bs-accordion-body-padding-x: 1.5rem;
  --bs-accordion-body-padding-y: 1.5rem;
  --bs-accordion-active-color: var(--bs-primary);
  --bs-accordion-active-bg: var(--bs-gray-100);
}

.pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.375rem;
  --bs-pagination-font-size: 1.075rem;
  --bs-pagination-color: var(--bs-gray-700);
  --bs-pagination-bg: transparent;
  --bs-pagination-border-width: 0;
  --bs-pagination-border-color: transparent;
  --bs-pagination-border-radius: 0.85rem;
  --bs-pagination-hover-color: var(--bs-component-hover-color);
  --bs-pagination-hover-bg: var(--bs-component-hover-bg);
  --bs-pagination-hover-border-color: transparent;
  --bs-pagination-focus-color: var(--bs-component-hover-color);
  --bs-pagination-focus-bg: var(--bs-component-hover-bg);
  --bs-pagination-focus-box-shadow: none;
  --bs-pagination-active-color: var(--bs-component-active-color);
  --bs-pagination-active-bg: var(--bs-component-active-bg);
  --bs-pagination-active-border-color: transparent;
  --bs-pagination-disabled-color: var(--bs-gray-400);
  --bs-pagination-disabled-bg: transparent;
  --bs-pagination-disabled-border-color: transparent;
  display: flex;
  padding-left: 0;
  list-style: none;
}

.page-link {
  position: relative;
  display: block;
  padding: var(--bs-pagination-padding-y) var(--bs-pagination-padding-x);
  font-size: var(--bs-pagination-font-size);
  color: var(--bs-pagination-color);
  background-color: var(--bs-pagination-bg);
  border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .page-link {
    transition: none;
  }
}
.page-link:hover {
  z-index: 2;
  color: var(--bs-pagination-hover-color);
  background-color: var(--bs-pagination-hover-bg);
  border-color: var(--bs-pagination-hover-border-color);
}
.page-link:focus {
  z-index: 3;
  color: var(--bs-pagination-focus-color);
  background-color: var(--bs-pagination-focus-bg);
  outline: 0;
  box-shadow: var(--bs-pagination-focus-box-shadow);
}
.page-link.active, .active > .page-link {
  z-index: 3;
  color: var(--bs-pagination-active-color);
  background-color: var(--bs-pagination-active-bg);
  border-color: var(--bs-pagination-active-border-color);
}
.page-link.disabled, .disabled > .page-link {
  color: var(--bs-pagination-disabled-color);
  pointer-events: none;
  background-color: var(--bs-pagination-disabled-bg);
  border-color: var(--bs-pagination-disabled-border-color);
}

.page-item:not(:first-child) .page-link {
  margin-left: calc(0 * -1);
}
.page-item:first-child .page-link {
  border-top-left-radius: var(--bs-pagination-border-radius);
  border-bottom-left-radius: var(--bs-pagination-border-radius);
}
.page-item:last-child .page-link {
  border-top-right-radius: var(--bs-pagination-border-radius);
  border-bottom-right-radius: var(--bs-pagination-border-radius);
}

.badge {
  --bs-badge-padding-x: 0.5rem;
  --bs-badge-padding-y: 0.325rem;
  --bs-badge-font-size: 0.85rem;
  --bs-badge-font-weight: 600;
  --bs-badge-color: var(--bs-body-color);
  --bs-badge-border-radius: 0.75rem;
  display: inline-block;
  padding: var(--bs-badge-padding-y) var(--bs-badge-padding-x);
  font-size: var(--bs-badge-font-size);
  font-weight: var(--bs-badge-font-weight);
  line-height: 1;
  color: var(--bs-badge-color);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--bs-badge-border-radius);
}
.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

.alert {
  --bs-alert-bg: transparent;
  --bs-alert-padding-x: 1rem;
  --bs-alert-padding-y: 1rem;
  --bs-alert-margin-bottom: 1rem;
  --bs-alert-color: inherit;
  --bs-alert-border-color: transparent;
  --bs-alert-border: var(--bs-border-width) solid var(--bs-alert-border-color);
  --bs-alert-border-radius: var(--bs-border-radius);
  --bs-alert-link-color: inherit;
  position: relative;
  padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
  margin-bottom: var(--bs-alert-margin-bottom);
  color: var(--bs-alert-color);
  background-color: var(--bs-alert-bg);
  border: var(--bs-alert-border);
  border-radius: var(--bs-alert-border-radius);
}

.alert-info {
  --bs-alert-color: var(--bs-info-text-emphasis);
  --bs-alert-bg: var(--bs-info-bg-subtle);
  --bs-alert-border-color: var(--bs-info-border-subtle);
  --bs-alert-link-color: var(--bs-info-text-emphasis);
}

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}
.progress {
  --bs-progress-height: 1rem;
  --bs-progress-font-size: 0.75rem;
  --bs-progress-bg: var(--bs-gray-100);
  --bs-progress-border-radius: 6px;
  --bs-progress-box-shadow: none;
  --bs-progress-bar-color: #ffffff;
  --bs-progress-bar-bg: #2AA29E;
  --bs-progress-bar-transition: width 0.6s ease;
  display: flex;
  height: var(--bs-progress-height);
  overflow: hidden;
  font-size: var(--bs-progress-font-size);
  background-color: var(--bs-progress-bg);
  border-radius: var(--bs-progress-border-radius);
  box-shadow: var(--bs-progress-box-shadow);
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--bs-progress-bar-color);
  text-align: center;
  white-space: nowrap;
  background-color: var(--bs-progress-bar-bg);
  transition: var(--bs-progress-bar-transition);
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.list-group {
  --bs-list-group-color: var(--bs-gray-900);
  --bs-list-group-bg: #ffffff;
  --bs-list-group-border-color: rgba(0, 0, 0, 0.125);
  --bs-list-group-border-width: var(--bs-border-width);
  --bs-list-group-border-radius: var(--bs-border-radius);
  --bs-list-group-item-padding-x: 1rem;
  --bs-list-group-item-padding-y: 0.5rem;
  --bs-list-group-action-color: var(--bs-gray-700);
  --bs-list-group-action-hover-color: var(--bs-gray-700);
  --bs-list-group-action-hover-bg: var(--bs-gray-100);
  --bs-list-group-action-active-color: var(--bs-body-color);
  --bs-list-group-action-active-bg: var(--bs-gray-200);
  --bs-list-group-disabled-color: var(--bs-gray-600);
  --bs-list-group-disabled-bg: #ffffff;
  --bs-list-group-active-color: var(--bs-component-active-color);
  --bs-list-group-active-bg: var(--bs-component-active-bg);
  --bs-list-group-active-border-color: var(--bs-component-active-bg);
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: var(--bs-list-group-border-radius);
}

.list-group-item {
  position: relative;
  display: block;
  padding: var(--bs-list-group-item-padding-y) var(--bs-list-group-item-padding-x);
  color: var(--bs-list-group-color);
  background-color: var(--bs-list-group-bg);
  border: var(--bs-list-group-border-width) solid var(--bs-list-group-border-color);
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.list-group-item.disabled, .list-group-item:disabled {
  color: var(--bs-list-group-disabled-color);
  pointer-events: none;
  background-color: var(--bs-list-group-disabled-bg);
}
.list-group-item.active {
  z-index: 2;
  color: var(--bs-list-group-active-color);
  background-color: var(--bs-list-group-active-bg);
  border-color: var(--bs-list-group-active-border-color);
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: calc(-1 * var(--bs-list-group-border-width));
  border-top-width: var(--bs-list-group-border-width);
}

.btn-close {
  --bs-btn-close-color: #000000;
  --bs-btn-close-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e");
  --bs-btn-close-opacity: 0.5;
  --bs-btn-close-hover-opacity: 0.75;
  --bs-btn-close-focus-shadow: none;
  --bs-btn-close-focus-opacity: 1;
  --bs-btn-close-disabled-opacity: 0.25;
  --bs-btn-close-white-filter: invert(1) grayscale(100%) brightness(200%);
  box-sizing: content-box;
  width: 0.75rem;
  height: 0.75rem;
  padding: 0.25em 0.25em;
  color: var(--bs-btn-close-color);
  background: transparent var(--bs-btn-close-bg) center/0.75rem auto no-repeat;
  border: 0;
  border-radius: 0.85rem;
  opacity: var(--bs-btn-close-opacity);
}
.btn-close:hover {
  color: var(--bs-btn-close-color);
  text-decoration: none;
  opacity: var(--bs-btn-close-hover-opacity);
}
.btn-close:focus {
  outline: 0;
  box-shadow: var(--bs-btn-close-focus-shadow);
  opacity: var(--bs-btn-close-focus-opacity);
}
.btn-close:disabled, .btn-close.disabled {
  pointer-events: none;
  user-select: none;
  opacity: var(--bs-btn-close-disabled-opacity);
}

[data-bs-theme=dark] .btn-close {
  filter: var(--bs-btn-close-white-filter);
}

.toast {
  --bs-toast-zindex: 1090;
  --bs-toast-padding-x: 0.75rem;
  --bs-toast-padding-y: 0.5rem;
  --bs-toast-spacing: 1.5rem;
  --bs-toast-max-width: 350px;
  --bs-toast-font-size: 0.875rem;
  --bs-toast-color: var(--bs-gray-700);
  --bs-toast-bg: var(--bs-body-bg);
  --bs-toast-border-width: var(--bs-border-width);
  --bs-toast-border-color: transparent;
  --bs-toast-border-radius: var(--bs-border-radius);
  --bs-toast-box-shadow: var(--bs-box-shadow);
  --bs-toast-header-color: var(--bs-gray-700);
  --bs-toast-header-bg: var(--bs-body-bg);
  --bs-toast-header-border-color: var(--bs-border-color);
  width: var(--bs-toast-max-width);
  max-width: 100%;
  font-size: var(--bs-toast-font-size);
  color: var(--bs-toast-color);
  pointer-events: auto;
  background-color: var(--bs-toast-bg);
  background-clip: padding-box;
  border: var(--bs-toast-border-width) solid var(--bs-toast-border-color);
  box-shadow: var(--bs-toast-box-shadow);
  border-radius: var(--bs-toast-border-radius);
}
.toast.showing {
  opacity: 0;
}
.toast:not(.show) {
  display: none;
}

.modal {
  --bs-modal-zindex: 1055;
  --bs-modal-width: 500px;
  --bs-modal-padding: 1.75rem;
  --bs-modal-margin: 0.5rem;
  --bs-modal-color: ;
  --bs-modal-bg: var(--bs-body-bg);
  --bs-modal-border-color: var(--bs-border-color-translucent);
  --bs-modal-border-width: 0;
  --bs-modal-border-radius: 0.85rem;
  --bs-modal-box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  --bs-modal-inner-border-radius: 0.85rem;
  --bs-modal-header-padding-x: 1.75rem;
  --bs-modal-header-padding-y: 1.75rem;
  --bs-modal-header-padding: 1.75rem 1.75rem;
  --bs-modal-header-border-color: var(--bs-border-color);
  --bs-modal-header-border-width: 1px;
  --bs-modal-title-line-height: 1.5;
  --bs-modal-footer-gap: 0.5rem;
  --bs-modal-footer-bg: ;
  --bs-modal-footer-border-color: var(--bs-border-color);
  --bs-modal-footer-border-width: 1px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-modal-zindex);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--bs-modal-margin);
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  color: var(--bs-modal-color);
  pointer-events: auto;
  background-color: var(--bs-modal-bg);
  background-clip: padding-box;
  border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
  border-radius: var(--bs-modal-border-radius);
  box-shadow: var(--bs-modal-box-shadow);
  outline: 0;
}

.modal-backdrop {
  --bs-backdrop-zindex: 1050;
  --bs-backdrop-bg: #000000;
  --bs-backdrop-opacity: 0.4;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-backdrop-zindex);
  width: 100vw;
  height: 100vh;
  background-color: var(--bs-backdrop-bg);
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: var(--bs-backdrop-opacity);
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  padding: var(--bs-modal-header-padding);
  border-bottom: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color);
  border-top-left-radius: var(--bs-modal-inner-border-radius);
  border-top-right-radius: var(--bs-modal-inner-border-radius);
}
.modal-header .btn-close {
  padding: calc(var(--bs-modal-header-padding-y) * 0.5) calc(var(--bs-modal-header-padding-x) * 0.5);
  margin: calc(-0.5 * var(--bs-modal-header-padding-y)) calc(-0.5 * var(--bs-modal-header-padding-x)) calc(-0.5 * var(--bs-modal-header-padding-y)) auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: var(--bs-modal-title-line-height);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--bs-modal-padding);
}

@media (min-width: 576px) {
  .modal {
    --bs-modal-margin: 1.75rem;
    --bs-modal-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
  }
  .modal-dialog {
    max-width: var(--bs-modal-width);
    margin-right: auto;
    margin-left: auto;
  }
}
.tooltip {
  --bs-tooltip-zindex: 1080;
  --bs-tooltip-max-width: 200px;
  --bs-tooltip-padding-x: 1rem;
  --bs-tooltip-padding-y: 0.75rem;
  --bs-tooltip-margin: 0;
  --bs-tooltip-font-size: 1rem;
  --bs-tooltip-color: var(--bs-gray-800);
  --bs-tooltip-bg: var(--bs-body-bg);
  --bs-tooltip-border-radius: 0.85rem;
  --bs-tooltip-opacity: 1;
  --bs-tooltip-arrow-width: 0.8rem;
  --bs-tooltip-arrow-height: 0.4rem;
  z-index: var(--bs-tooltip-zindex);
  display: block;
  margin: var(--bs-tooltip-margin);
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-tooltip-font-size);
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: var(--bs-tooltip-opacity);
}
.tooltip .tooltip-arrow {
  display: block;
  width: var(--bs-tooltip-arrow-width);
  height: var(--bs-tooltip-arrow-height);
}
.tooltip .tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

/* rtl:begin:ignore */

/* rtl:end:ignore */

/* rtl:begin:ignore */

/* rtl:end:ignore */
.tooltip-inner {
  max-width: var(--bs-tooltip-max-width);
  padding: var(--bs-tooltip-padding-y) var(--bs-tooltip-padding-x);
  color: var(--bs-tooltip-color);
  text-align: center;
  background-color: var(--bs-tooltip-bg);
  border-radius: var(--bs-tooltip-border-radius);
}

.popover {
  --bs-popover-zindex: 1070;
  --bs-popover-max-width: 276px;
  --bs-popover-font-size: 1rem;
  --bs-popover-bg: #ffffff;
  --bs-popover-border-width: var(--bs-border-width);
  --bs-popover-border-color: #ffffff;
  --bs-popover-border-radius: 0.85rem;
  --bs-popover-inner-border-radius: 0.85rem;
  --bs-popover-box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  --bs-popover-header-padding-x: 1.25rem;
  --bs-popover-header-padding-y: 1rem;
  --bs-popover-header-font-size: 1rem;
  --bs-popover-header-color: var(--bs-gray-800);
  --bs-popover-header-bg: #ffffff;
  --bs-popover-body-padding-x: 1.25rem;
  --bs-popover-body-padding-y: 1.25rem;
  --bs-popover-body-color: var(--bs-gray-800);
  --bs-popover-arrow-width: 1rem;
  --bs-popover-arrow-height: 0.5rem;
  --bs-popover-arrow-border: var(--bs-popover-border-color);
  z-index: var(--bs-popover-zindex);
  display: block;
  max-width: var(--bs-popover-max-width);
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-popover-font-size);
  word-wrap: break-word;
  background-color: var(--bs-popover-bg);
  background-clip: padding-box;
  border: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-radius: var(--bs-popover-border-radius);
  box-shadow: var(--bs-popover-box-shadow);
}
.popover .popover-arrow {
  display: block;
  width: var(--bs-popover-arrow-width);
  height: var(--bs-popover-arrow-height);
}
.popover .popover-arrow::before, .popover .popover-arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
  border-width: 0;
}

/* rtl:begin:ignore */

/* rtl:end:ignore */

/* rtl:begin:ignore */

/* rtl:end:ignore */
.popover-header {
  padding: var(--bs-popover-header-padding-y) var(--bs-popover-header-padding-x);
  margin-bottom: 0;
  font-size: var(--bs-popover-header-font-size);
  color: var(--bs-popover-header-color);
  background-color: var(--bs-popover-header-bg);
  border-bottom: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-top-left-radius: var(--bs-popover-inner-border-radius);
  border-top-right-radius: var(--bs-popover-inner-border-radius);
}
.popover-header:empty {
  display: none;
}

.popover-body {
  padding: var(--bs-popover-body-padding-y) var(--bs-popover-body-padding-x);
  color: var(--bs-popover-body-color);
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  touch-action: pan-y;
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

.carousel-item-next:not(.carousel-item-start),
.active.carousel-item-end {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-end),
.active.carousel-item-start {
  transform: translateX(-100%);
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
}
.carousel-indicators [data-bs-target] {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-bs-target] {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}
[data-bs-theme=dark] .carousel .carousel-indicators [data-bs-target], [data-bs-theme=dark].carousel .carousel-indicators [data-bs-target] {
  background-color: #000000;
}


.spinner-border {
  display: inline-block;
  width: var(--bs-spinner-width);
  height: var(--bs-spinner-height);
  vertical-align: var(--bs-spinner-vertical-align);
  border-radius: 50%;
  animation: var(--bs-spinner-animation-speed) linear infinite var(--bs-spinner-animation-name);
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg) /* rtl:ignore */;
  }
}
.spinner-border {
  --bs-spinner-width: 2rem;
  --bs-spinner-height: 2rem;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-border-width: 0.185rem;
  --bs-spinner-animation-speed: 0.65s;
  --bs-spinner-animation-name: spinner-border;
  border: var(--bs-spinner-border-width) solid currentcolor;
  border-right-color: transparent;
}

.spinner-border-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
  --bs-spinner-border-width: 0.145em;
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}

@media (prefers-reduced-motion: reduce) {
  .spinner-border {
    --bs-spinner-animation-speed: 1.3s;
  }
}
.offcanvas {
  --bs-offcanvas-zindex: 1045;
  --bs-offcanvas-width: 400px;
  --bs-offcanvas-height: 30vh;
  --bs-offcanvas-padding-x: 1.75rem;
  --bs-offcanvas-padding-y: 1.75rem;
  --bs-offcanvas-color: var(--bs-body-color);
  --bs-offcanvas-bg: var(--bs-body-bg);
  --bs-offcanvas-border-width: 0;
  --bs-offcanvas-border-color: var(--bs-border-color-translucent);
  --bs-offcanvas-box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  --bs-offcanvas-transition: transform 0.3s ease-in-out;
  --bs-offcanvas-title-line-height: 1.5;
}

.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: var(--bs-offcanvas-zindex);
  display: flex;
  flex-direction: column;
  max-width: 100%;
  color: var(--bs-offcanvas-color);
  visibility: hidden;
  background-color: var(--bs-offcanvas-bg);
  background-clip: padding-box;
  outline: 0;
  box-shadow: var(--bs-offcanvas-box-shadow);
  transition: var(--bs-offcanvas-transition);
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    transition: none;
  }
}
.offcanvas.showing, .offcanvas.show:not(.hiding) {
  transform: none;
}
.offcanvas.showing, .offcanvas.hiding, .offcanvas.show {
  visibility: visible;
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000000;
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.4;
}

.placeholder {
  display: inline-block;
  min-height: 1em;
  vertical-align: middle;
  cursor: wait;
  background-color: currentcolor;
  opacity: 0.5;
}
.placeholder.btn::before {
  display: inline-block;
  content: "";
}

@keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}

@keyframes placeholder-wave {
  100% {
    mask-position: -200% 0%;
  }
}
.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.link-primary {
  color: RGBA(var(--bs-primary-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-primary-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-primary:hover, .link-primary:focus {
  color: RGBA(85, 181, 177, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(85, 181, 177, var(--bs-link-underline-opacity, 1)) !important;
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--bs-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}

.vr {
  display: inline-block;
  align-self: stretch;
  width: var(--bs-border-width);
  min-height: 1em;
  background-color: currentcolor;
  opacity: 0.25;
}

.align-middle {
  vertical-align: middle !important;
}

.object-fit-cover {
  object-fit: cover !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-table {
  display: table !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  box-shadow: var(--bs-box-shadow) !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.start-0 {
  left: 0 !important;
}

.end-0 {
  right: 0 !important;
}

.translate-middle {
  transform: translate(-50%, -50%) !important;
}

.border {
  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-end-0 {
  border-right: 0 !important;
}

.border-bottom {
  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-start-0 {
  border-left: 0 !important;
}

.border-primary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
}

.border-success {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;
}

.border-info {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;
}

.border-warning {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;
}

.border-0 {
  border-width: 0 !important;
}

.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.w-15px {
  width: 15px !important;
}

.w-25px {
  width: 25px !important;
}

.w-30px {
  width: 30px !important;
}

.w-40px {
  width: 40px !important;
}

.w-50px {
  width: 50px !important;
}

.w-125px {
  width: 125px !important;
}

.w-150px {
  width: 150px !important;
}

.w-175px {
  width: 175px !important;
}

.mw-400px {
  max-width: 400px !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.h-15px {
  height: 15px !important;
}

.h-20px {
  height: 20px !important;
}

.h-25px {
  height: 25px !important;
}

.h-30px {
  height: 30px !important;
}

.h-40px {
  height: 40px !important;
}

.h-50px {
  height: 50px !important;
}

.h-125px {
  height: 125px !important;
}

.h-150px {
  height: 150px !important;
}

.h-250px {
  height: 250px !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-center {
  align-items: center !important;
}

.align-self-center {
  align-self: center !important;
}

.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.m-3 {
  margin: 0.75rem !important;
}

.m-15 {
  margin: 3.75rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.mx-3 {
  margin-right: 0.75rem !important;
  margin-left: 0.75rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.my-5 {
  margin-top: 1.25rem !important;
  margin-bottom: 1.25rem !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-3 {
  margin-top: 0.75rem !important;
}

.mt-4 {
  margin-top: 1rem !important;
}

.mt-10 {
  margin-top: 2.5rem !important;
}

.me-1 {
  margin-right: 0.25rem !important;
}

.me-2 {
  margin-right: 0.5rem !important;
}

.me-3 {
  margin-right: 0.75rem !important;
}

.me-5 {
  margin-right: 1.25rem !important;
}

.me-7 {
  margin-right: 1.75rem !important;
}

.me-9 {
  margin-right: 2.25rem !important;
}

.me-15 {
  margin-right: 3.75rem !important;
}

.me-16 {
  margin-right: 4rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 0.75rem !important;
}

.mb-4 {
  margin-bottom: 1rem !important;
}

.mb-5 {
  margin-bottom: 1.25rem !important;
}

.mb-6 {
  margin-bottom: 1.5rem !important;
}

.mb-7 {
  margin-bottom: 1.75rem !important;
}

.mb-8 {
  margin-bottom: 2rem !important;
}

.mb-10 {
  margin-bottom: 2.5rem !important;
}

.mb-15 {
  margin-bottom: 3.75rem !important;
}

.ms-1 {
  margin-left: 0.25rem !important;
}

.ms-2 {
  margin-left: 0.5rem !important;
}

.ms-3 {
  margin-left: 0.75rem !important;
}

.ms-5 {
  margin-left: 1.25rem !important;
}

.ms-auto {
  margin-left: auto !important;
}

.me-n1 {
  margin-right: -0.25rem !important;
}

.me-n2 {
  margin-right: -0.5rem !important;
}

.ms-n1 {
  margin-left: -0.25rem !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 0.75rem !important;
}

.p-4 {
  padding: 1rem !important;
}

.p-5 {
  padding: 1.25rem !important;
}

.p-6 {
  padding: 1.5rem !important;
}

.p-7 {
  padding: 1.75rem !important;
}

.p-8 {
  padding: 2rem !important;
}

.p-9 {
  padding: 2.25rem !important;
}

.px-3 {
  padding-right: 0.75rem !important;
  padding-left: 0.75rem !important;
}

.px-4 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}

.px-5 {
  padding-right: 1.25rem !important;
  padding-left: 1.25rem !important;
}

.px-6 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}

.px-7 {
  padding-right: 1.75rem !important;
  padding-left: 1.75rem !important;
}

.px-9 {
  padding-right: 2.25rem !important;
  padding-left: 2.25rem !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-3 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

.py-4 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-5 {
  padding-top: 1.25rem !important;
  padding-bottom: 1.25rem !important;
}

.py-6 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-2 {
  padding-top: 0.5rem !important;
}

.pt-3 {
  padding-top: 0.75rem !important;
}

.pt-4 {
  padding-top: 1rem !important;
}

.pt-6 {
  padding-top: 1.5rem !important;
}

.pt-7 {
  padding-top: 1.75rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-2 {
  padding-bottom: 0.5rem !important;
}

.pb-3 {
  padding-bottom: 0.75rem !important;
}

.pb-5 {
  padding-bottom: 1.25rem !important;
}

.ps-0 {
  padding-left: 0 !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 0.75rem !important;
}

.gap-7 {
  gap: 1.75rem !important;
}

.fs-1 {
  font-size: calc(1.3rem + 0.6vw) !important;
}

.fs-2 {
  font-size: calc(1.275rem + 0.3vw) !important;
}

.fs-3 {
  font-size: calc(1.26rem + 0.12vw) !important;
}

.fs-4 {
  font-size: 1.25rem !important;
}

.fs-5 {
  font-size: 1.15rem !important;
}

.fs-6 {
  font-size: 1.075rem !important;
}

.fs-7 {
  font-size: 0.95rem !important;
}

.fs-base {
  font-size: 1rem !important;
}

.fs-2x {
  font-size: calc(1.325rem + 0.9vw) !important;
}

.fs-2hx {
  font-size: calc(1.375rem + 1.5vw) !important;
}

.fs-3x {
  font-size: calc(1.425rem + 2.1vw) !important;
}

.fw-semibold {
  font-weight: 500 !important;
}

.fw-bold {
  font-weight: 600 !important;
}

.fw-bolder {
  font-weight: 700 !important;
}

.text-start {
  text-align: left !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-wrap {
  white-space: normal !important;
}

/* rtl:begin:remove */

/* rtl:end:remove */
.text-light {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;
}

.text-primary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;
}

.text-success {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;
}

.text-info {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;
}

.text-warning {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;
}

.text-danger {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;
}

.text-dark {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
}

.text-black {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
}

.text-white {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;
}

.text-muted {
  --bs-text-opacity: 1;
  color: var(--bs-secondary-color) !important;
}

.text-warning-emphasis {
  color: var(--bs-warning-text-emphasis) !important;
}

.bg-primary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;
}

.bg-success {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;
}

.bg-info {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;
}

.bg-warning {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;
}

.bg-danger {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;
}

.bg-dark {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;
}

.bg-white {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;
}

.bg-body {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-info-subtle {
  background-color: var(--bs-info-bg-subtle) !important;
}

.rounded {
  border-radius: 0.85rem !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-3 {
  border-radius: 1rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.rounded-top-0 {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.rounded-end {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

.rounded-end-0 {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.rounded-bottom-0 {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.rounded-start {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important;
}

.rounded-start-0 {
  border-bottom-left-radius: 0 !important;
  border-top-left-radius: 0 !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

.z-index-1 {
  z-index: 1 !important;
}

.z-index-2 {
  z-index: 2 !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.min-w-100px {
  min-width: 100px !important;
}

.min-w-150px {
  min-width: 150px !important;
}

.min-w-250px {
  min-width: 250px !important;
}

.min-h-200px {
  min-height: 200px !important;
}

.border-top-0 {
  border-top-width: 0 !important;
}

.border-bottom-0 {
  border-bottom-width: 0 !important;
}
@media (min-width: 768px) {
  .d-md-flex {
    display: flex !important;
  }
  .w-md-50 {
    width: 50% !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .text-md-start {
    text-align: left !important;
  }
}
@media (min-width: 992px) {
  .w-lg-300px {
    width: 300px !important;
  }
  .flex-lg-row {
    flex-direction: row !important;
  }
  .me-lg-10 {
    margin-right: 2.5rem !important;
  }
  .me-lg-20 {
    margin-right: 5rem !important;
  }
  .mb-lg-10 {
    margin-bottom: 2.5rem !important;
  }
  .mb-lg-12 {
    margin-bottom: 3rem !important;
  }
  .p-lg-17 {
    padding: 4.25rem !important;
  }
  .gap-lg-10 {
    gap: 2.5rem !important;
  }
  .fs-lg-2hx {
    font-size: calc(1.375rem + 1.5vw) !important;
  }
}
@media (min-width: 1200px) {
  .w-xl-25 {
    width: 25% !important;
  }
  .mb-xl-10 {
    margin-bottom: 2.5rem !important;
  }
}
@media (min-width: 1400px) {
  .mb-xxl-10 {
    margin-bottom: 2.5rem !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 1.75rem !important;
  }
  .fs-2 {
    font-size: 1.5rem !important;
  }
  .fs-3 {
    font-size: 1.35rem !important;
  }
  .fs-2x {
    font-size: 2rem !important;
  }
  .fs-2hx {
    font-size: 2.5rem !important;
  }
  .fs-3x {
    font-size: 3rem !important;
  }
  .fs-lg-2hx {
    font-size: 2.5rem !important;
  }
}
:root {
  --bs-xs:0;
  --bs-sm:576px;
  --bs-md:768px;
  --bs-lg:992px;
  --bs-xl:1200px;
  --bs-xxl:1400px;
  --bs-scrollbar-size: 5px;
  --bs-scrollbar-overlay-size: 19px;
  --bs-scrollbar-overlay-space: 7px;
  --bs-white-bg-rgb: 255, 255, 255;
  --bs-black-bg-rgb: 0, 0, 0;
}

[data-bs-theme=light] {
  --bs-text-muted: #99A1B7;
  --bs-gray-100: #F9F9F9;
  --bs-gray-100-rgb: 249, 249, 249;
  --bs-gray-200: #F1F1F4;
  --bs-gray-200-rgb: 241, 241, 244;
  --bs-gray-300: #DBDFE9;
  --bs-gray-300-rgb: 219, 223, 233;
  --bs-gray-400: #C4CADA;
  --bs-gray-400-rgb: 196, 202, 218;
  --bs-gray-500: #99A1B7;
  --bs-gray-500-rgb: 153, 161, 183;
  --bs-gray-600: #78829D;
  --bs-gray-600-rgb: 120, 130, 157;
  --bs-gray-700: #4B5675;
  --bs-gray-700-rgb: 75, 86, 117;
  --bs-gray-800: #252F4A;
  --bs-gray-800-rgb: 37, 47, 74;
  --bs-gray-900: #071437;
  --bs-gray-900-rgb: 7, 20, 55;
  --bs-light: #F9F9F9;
  --bs-primary: #2AA29E;
  --bs-secondary: #E3EBF6;
  --bs-success: #17C653;
  --bs-info: #7239EA;
  --bs-warning: #FF9355;
  --bs-danger: #F8285A;
  --bs-dark: #1E2129;
  --bs-primary-active: #1F8280;
  --bs-secondary-active: #CEDCEF;
  --bs-light-active: #F1F1F4;
  --bs-success-active: #04B440;
  --bs-info-active: #5014D0;
  --bs-warning-active: #F57327;
  --bs-danger-active: #D81A48;
  --bs-dark-active: #111318;
  --bs-primary-light: #F2FBFA;
  --bs-secondary-light: #F3F6FB;
  --bs-success-light: #DFFFEA;
  --bs-info-light: #F8F5FF;
  --bs-warning-light: #FFF8DD;
  --bs-danger-light: #FFEEF3;
  --bs-dark-light: #F9F9F9;
  --bs-light-light: #ffffff;
  --bs-primary-inverse: #ffffff;
  --bs-secondary-inverse: #252F4A;
  --bs-light-inverse: #252F4A;
  --bs-success-inverse: #ffffff;
  --bs-info-inverse: #ffffff;
  --bs-warning-inverse: #ffffff;
  --bs-danger-inverse: #ffffff;
  --bs-dark-inverse: #ffffff;
  --bs-primary-clarity: rgba(42, 162, 158, 0.2);
  --bs-secondary-clarity: rgba(227, 235, 246, 0.2);
  --bs-success-clarity: rgba(23, 198, 83, 0.2);
  --bs-info-clarity: rgba(114, 57, 234, 0.2);
  --bs-warning-clarity: rgba(255, 147, 85, 0.2);
  --bs-danger-clarity: rgba(248, 40, 90, 0.2);
  --bs-dark-clarity: rgba(30, 33, 41, 0.2);
  --bs-light-clarity: rgba(255, 255, 255, 0.2);
  --bs-light-rgb: 249, 249, 249;
  --bs-primary-rgb: 42, 162, 158;
  --bs-secondary-rgb: 227, 235, 246;
  --bs-success-rgb: 23, 198, 83;
  --bs-info-rgb: 114, 57, 234;
  --bs-warning-rgb: 255, 147, 85;
  --bs-danger-rgb: 248, 40, 90;
  --bs-dark-rgb: 30, 33, 41;
  --bs-text-white: #ffffff;
  --bs-text-primary: #2AA29E;
  --bs-text-secondary: #E3EBF6;
  --bs-text-light: #F9F9F9;
  --bs-text-success: #17C653;
  --bs-text-info: #7239EA;
  --bs-text-warning: #FF9355;
  --bs-text-danger: #F8285A;
  --bs-text-dark: #1E2129;
  --bs-text-muted: #99A1B7;
  --bs-text-gray-100: #F9F9F9;
  --bs-text-gray-200: #F1F1F4;
  --bs-text-gray-300: #DBDFE9;
  --bs-text-gray-400: #C4CADA;
  --bs-text-gray-500: #99A1B7;
  --bs-text-gray-600: #78829D;
  --bs-text-gray-700: #4B5675;
  --bs-text-gray-800: #252F4A;
  --bs-text-gray-900: #071437;
  --bs-border-color: #F1F1F4;
  --bs-border-dashed-color: #DBDFE9;
  --bs-component-active-color: #ffffff;
  --bs-component-active-bg: #2AA29E;
  --bs-component-hover-color: #2AA29E;
  --bs-component-hover-bg: #F9F9F9;
  --bs-component-checked-color: #ffffff;
  --bs-component-checked-bg: #2AA29E;
  --bs-box-shadow-xs: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
  --bs-box-shadow-sm: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.05);
  --bs-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  --bs-box-shadow-lg: 0 1rem 2rem 1rem rgba(0, 0, 0, 0.1);
  --bs-input-bg: var(--bs-body-bg);
  --bs-input-color: var(--bs-gray-700);
  --bs-input-solid-color: var(--bs-gray-700);
  --bs-input-solid-bg: var(--bs-gray-100);
  --bs-input-solid-bg-focus: var(--bs-gray-200);
  --bs-input-solid-placeholder-color: var(--bs-gray-500);
  --bs-root-card-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.03);
  --bs-root-card-border-color: #F1F1F4;
  --bs-tooltip-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.15);
  --bs-table-striped-bg: rgba(var(--bs-gray-100-rgb), 0.75);
  --bs-table-loading-message-box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  --bs-dropdown-bg: var(--bs-body-bg);
  --bs-dropdown-box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  --bs-code-bg: #f1f3f8;
  --bs-code-shadow: 0px 3px 9px rgba(0, 0, 0, 0.08);
  --bs-code-border-color: transparent;
  --bs-code-color: #b93993;
  --bs-symbol-label-color: var(--bs-gray-800);
  --bs-symbol-label-bg: var(--bs-gray-100);
  --bs-symbol-border-color: rgba(var(--bs-body-bg), 0.5);
  --bs-bullet-bg-color: var(--bs-gray-400);
  --bs-scrolltop-opacity: 0;
  --bs-scrolltop-opacity-on: 0.3;
  --bs-scrolltop-opacity-hover: 1;
  --bs-scrolltop-box-shadow: var(--bs-box-shadow);
  --bs-scrolltop-bg-color: var(--bs-primary);
  --bs-scrolltop-bg-color-hover: var(--bs-primary);
  --bs-scrolltop-icon-color: var(--bs-primary-inverse);
  --bs-scrolltop-icon-color-hover: var(--bs-primary-inverse);
  --bs-drawer-box-shadow: 0px 1px 9px -3px rgba(0, 0, 0, 0.05);
  --bs-drawer-bg-color: #ffffff;
  --bs-drawer-overlay-bg-color: rgba(0, 0, 0, 0.2);
  --bs-menu-dropdown-box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  --bs-menu-dropdown-bg-color: var(--bs-body-bg);
  --bs-menu-heading-color: #99A1B7;
  --bs-menu-link-color-hover: #2AA29E;
  --bs-menu-link-color-show: #2AA29E;
  --bs-menu-link-color-here: #2AA29E;
  --bs-menu-link-color-active: #2AA29E;
  --bs-menu-link-bg-color-hover: #F9F9F9;
  --bs-menu-link-bg-color-show: #F9F9F9;
  --bs-menu-link-bg-color-here: #F9F9F9;
  --bs-menu-link-bg-color-active: #F9F9F9;
  --bs-scrollbar-color: #F1F1F4;
  --bs-scrollbar-hover-color: #DBDFE9;
  --bs-overlay-bg: rgba(0, 0, 0, 0.05);
  --bs-blockui-overlay-bg: rgba(0, 0, 0, 0.05);
  --bs-rating-color-default: #C4CADA;
  --bs-rating-color-active: #FFAD0F;
  --bs-ribbon-label-box-shadow: 0px -1px 5px 0px rgba(30, 33, 41, 0.1);
  --bs-ribbon-label-bg: #2AA29E;
  --bs-ribbon-label-border-color: #15514f;
  --bs-ribbon-clip-bg: #1E2129;
  --bs-engage-btn-bg: #ffffff;
  --bs-engage-btn-box-shadow: 0px 0px 22px #E0E0E0;
  --bs-engage-btn-border-color: #E8E8E8;
  --bs-engage-btn-color: #252F4A;
  --bs-engage-btn-icon-color: #78829D;
  --bs-engage-btn-color-active: #252F4A;
}

[data-bs-theme=dark] {
  --bs-text-muted: #636674;
  --bs-gray-100: #1B1C22;
  --bs-gray-100-rgb: 27, 28, 34;
  --bs-gray-200: #26272F;
  --bs-gray-200-rgb: 38, 39, 47;
  --bs-gray-300: #363843;
  --bs-gray-300-rgb: 54, 56, 67;
  --bs-gray-400: #464852;
  --bs-gray-400-rgb: 70, 72, 82;
  --bs-gray-500: #636674;
  --bs-gray-500-rgb: 99, 102, 116;
  --bs-gray-600: #808290;
  --bs-gray-600-rgb: 128, 130, 144;
  --bs-gray-700: #9A9CAE;
  --bs-gray-700-rgb: 154, 156, 174;
  --bs-gray-800: #B5B7C8;
  --bs-gray-800-rgb: 181, 183, 200;
  --bs-gray-900: #F5F5F5;
  --bs-gray-900-rgb: 245, 245, 245;
  --bs-light: #1B1C22;
  --bs-primary: #1F8280;
  --bs-secondary: #41528C;
  --bs-success: #00A261;
  --bs-info: #883FFF;
  --bs-warning: #C59A00;
  --bs-danger: #E42855;
  --bs-dark: #272A34;
  --bs-primary-active: #2AA29E;
  --bs-secondary-active: #4C64AF;
  --bs-light-active: #1F212A;
  --bs-success-active: #01BF73;
  --bs-info-active: #9E63FF;
  --bs-warning-active: #D9AA00;
  --bs-danger-active: #FF3767;
  --bs-dark-active: #2D2F39;
  --bs-primary-light: #1B4646;
  --bs-secondary-light: #ADC5E3;
  --bs-success-light: #1F212A;
  --bs-info-light: #272134;
  --bs-warning-light: #242320;
  --bs-danger-light: #302024;
  --bs-dark-light: #1E2027;
  --bs-light-light: #1F212A;
  --bs-primary-inverse: #ffffff;
  --bs-secondary-inverse: #ffffff;
  --bs-light-inverse: #808290;
  --bs-success-inverse: #ffffff;
  --bs-info-inverse: #ffffff;
  --bs-warning-inverse: #ffffff;
  --bs-danger-inverse: #ffffff;
  --bs-dark-inverse: #ffffff;
  --bs-primary-clarity: rgba(31, 130, 128, 0.2);
  --bs-secondary-clarity: rgba(65, 82, 140, 0.2);
  --bs-success-clarity: rgba(0, 162, 97, 0.2);
  --bs-info-clarity: rgba(136, 63, 255, 0.2);
  --bs-warning-clarity: rgba(197, 154, 0, 0.2);
  --bs-danger-clarity: rgba(228, 40, 85, 0.2);
  --bs-dark-clarity: rgba(39, 42, 52, 0.2);
  --bs-light-clarity: rgba(31, 33, 42, 0.2);
  --bs-light-rgb: 27, 28, 34;
  --bs-primary-rgb: 31, 130, 128;
  --bs-secondary-rgb: 65, 82, 140;
  --bs-success-rgb: 0, 162, 97;
  --bs-info-rgb: 136, 63, 255;
  --bs-warning-rgb: 197, 154, 0;
  --bs-danger-rgb: 228, 40, 85;
  --bs-dark-rgb: 39, 42, 52;
  --bs-text-white: #ffffff;
  --bs-text-primary: #1F8280;
  --bs-text-secondary: #41528C;
  --bs-text-light: #1B1C22;
  --bs-text-success: #00A261;
  --bs-text-info: #883FFF;
  --bs-text-warning: #C59A00;
  --bs-text-danger: #E42855;
  --bs-text-dark: #272A34;
  --bs-text-muted: #636674;
  --bs-text-gray-100: #1B1C22;
  --bs-text-gray-200: #26272F;
  --bs-text-gray-300: #363843;
  --bs-text-gray-400: #464852;
  --bs-text-gray-500: #636674;
  --bs-text-gray-600: #808290;
  --bs-text-gray-700: #9A9CAE;
  --bs-text-gray-800: #B5B7C8;
  --bs-text-gray-900: #F5F5F5;
  --bs-border-color: #26272F;
  --bs-border-dashed-color: #363843;
  --bs-component-active-color: #ffffff;
  --bs-component-active-bg: #1F8280;
  --bs-component-hover-color: #1F8280;
  --bs-component-hover-bg: #1B1C22;
  --bs-component-checked-color: #ffffff;
  --bs-component-checked-bg: #1F8280;
  --bs-box-shadow-xs: 0 0.1rem 0.75rem 0.25rem rgba(0, 0, 0, 0.05);
  --bs-box-shadow-sm: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.05);
  --bs-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  --bs-box-shadow-lg: 0 1rem 2rem 1rem rgba(0, 0, 0, 0.1);
  --bs-input-color: var(--bs-gray-700);
  --bs-input-bg: var(--bs-body-bg);
  --bs-input-solid-color: var(--bs-gray-700);
  --bs-input-solid-bg: var(--bs-gray-100);
  --bs-input-solid-bg-focus: var(--bs-gray-200);
  --bs-input-solid-placeholder-color: var(--bs-gray-500);
  --bs-tooltip-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.15);
  --bs-root-card-box-shadow: none;
  --bs-root-card-border-color: #1E2027;
  --bs-table-striped-bg: rgba(27, 28, 34, 0.75);
  --bs-table-loading-message-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.3);
  --bs-dropdown-bg: #1C1D22;
  --bs-dropdown-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.3);
  --bs-code-bg: #2b2b40;
  --bs-code-shadow: rgba(0, 0, 0, 0.08) 0px 3px 9px 0px;
  --bs-code-border-color: transparent;
  --bs-code-color: #b93993;
  --bs-symbol-label-color: #B5B7C8;
  --bs-symbol-label-bg: #1B1C22;
  --bs-symbol-border-color: rgba(255, 255, 255, 0.5);
  --bs-bullet-bg-color: #464852;
  --bs-scrolltop-opacity: 0;
  --bs-scrolltop-opacity-on: 0.3;
  --bs-scrolltop-opacity-hover: 1;
  --bs-scrolltop-box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  --bs-scrolltop-bg-color: #2AA29E;
  --bs-scrolltop-bg-color-hover: #2AA29E;
  --bs-scrolltop-icon-color: #ffffff;
  --bs-scrolltop-icon-color-hover: #ffffff;
  --bs-drawer-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  --bs-drawer-bg-color: #1C1D22;
  --bs-drawer-overlay-bg-color: rgba(0, 0, 0, 0.4);
  --bs-menu-dropdown-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.3);
  --bs-menu-dropdown-bg-color: #1C1D22;
  --bs-menu-heading-color: #636674;
  --bs-menu-link-color-hover: #1F8280;
  --bs-menu-link-color-show: #1F8280;
  --bs-menu-link-color-here: #1F8280;
  --bs-menu-link-color-active: #1F8280;
  --bs-menu-link-bg-color-hover: #1B1C22;
  --bs-menu-link-bg-color-show: #1B1C22;
  --bs-menu-link-bg-color-here: #1B1C22;
  --bs-menu-link-bg-color-active: #1B1C22;
  --bs-scrollbar-color: #26272F;
  --bs-scrollbar-hover-color: #363843;
  --bs-overlay-bg: rgba(255, 255, 255, 0.05);
  --bs-blockui-overlay-bg: rgba(255, 255, 255, 0.05);
  --bs-rating-color-default: #464852;
  --bs-rating-color-active: #FFAD0F;
  --bs-ribbon-label-box-shadow: 0px -1px 5px 0px rgba(255, 255, 255, 0.1);
  --bs-ribbon-label-bg: #2AA29E;
  --bs-ribbon-label-border-color: #15514f;
  --bs-ribbon-clip-bg: #F9F9F9;
  --bs-engage-btn-bg: #26272F;
  --bs-engage-btn-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.3);
  --bs-engage-btn-border-color: #26272F;
  --bs-engage-btn-color: #B5B7C8;
  --bs-engage-btn-icon-color: #808290;
  --bs-engage-btn-color-active: #B5B7C8;
}

h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  outline: 0;
}

[data-kt-theme-mode-switching=true] * {
  transition: none !important;
}

[data-bs-theme=light] .theme-dark-show {
  display: none !important;
}

[data-bs-theme=dark] .theme-light-show {
  display: none !important;
}

.animation {
  animation-duration: 1s;
  animation-fill-mode: both;
}

@keyframes animationSlideInDown {
  from {
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}
.animation-slide-in-down {
  animation-name: animationSlideInDown;
}

@keyframes animationSlideInUp {
  from {
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  to {
    transform: translate3d(0, 0, 0);
  }
}

@keyframes animationFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes animationFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes animationBlink {
  to {
    visibility: hidden;
  }
}

.alert-info {
  color: var(--bs-info);
  border-color: var(--bs-info);
  background-color: var(--bs-info-light);
}

[data-bs-theme=dark] .dropdown-menu {
  box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.3);
}

[data-bs-theme=dark] .toast {
  --bs-toast-bg: #26272F;
  --bs-toast-header-bg: #26272F;
  --bs-toast-header-border-color: #363843;
}

.pagination {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 0;
}

.page-item {
  margin-right: 0.5rem;
  /*rtl:end:ignore*/
}
.page-item:last-child {
  margin-right: 0;
}
.page-item .page-link {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0.85rem;
  height: 2.5rem;
  min-width: 2.5rem;
  font-weight: 500;
  font-size: 1.075rem;
  /*rtl:options:{"autoRename":false}*/
  /*rtl:end:ignore*/
  /*rtl:options:{"autoRename":false}*/
  /*rtl:options:{"autoRename":false}*/
}
.page-item .page-link i {
  font-size: 0.85rem;
}
.page-item .page-link .previous,
.page-item .page-link .next {
  display: block;
  height: 0.875rem;
  width: 0.875rem;
  /*rtl:raw:transform: rotateZ(-180deg);*/
}
.page-item .page-link .first {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-gray-700);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-700%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-700%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
}
.page-item .page-link .previous {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-gray-700);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-700%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-700%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
}
.page-item .page-link .next {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-gray-700);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-700%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-700%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
}
.page-item .page-link .last {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-gray-700);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-700%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-700%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
}
.page-item:focus .page-link {
  color: var(--bs-pagination-focus-color);
  /*rtl:options:{"autoRename":false}*/
}

.page-item:focus .page-link i {
  color: var(--bs-pagination-focus-color);
}
.page-item:focus .page-link .previous {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-pagination-focus-color);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-focus-color%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-focus-color%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
}
.page-item:focus .page-link .next {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-pagination-focus-color);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-focus-color%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-focus-color%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
}
.page-item:hover:not(.active):not(.offset):not(.disabled) .page-link {
  color: var(--bs-pagination-hover-color);
  /*rtl:options:{"autoRename":false}*/
}

.page-item:hover:not(.active):not(.offset):not(.disabled) .page-link i {
  color: var(--bs-pagination-hover-color);
}
.page-item:hover:not(.active):not(.offset):not(.disabled) .page-link .previous {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-pagination-hover-color);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-hover-color%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-hover-color%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
}
.page-item:hover:not(.active):not(.offset):not(.disabled) .page-link .next {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-pagination-hover-color);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-hover-color%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-hover-color%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
}
.page-item.active .page-link {
  color: var(--bs-pagination-active-color);
  /*rtl:options:{"autoRename":false}*/
}

.page-item.active .page-link i {
  color: var(--bs-pagination-active-color);
}
.page-item.active .page-link .previous {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-pagination-active-color);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-active-color%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-active-color%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
}
.page-item.active .page-link .next {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-pagination-active-color);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-active-color%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-active-color%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
}
.page-item.disabled .page-link {
  color: var(--bs-pagination-disabled-color);
  /*rtl:options:{"autoRename":false}*/
}

.page-item.disabled .page-link i {
  color: var(--bs-pagination-disabled-color);
}
.page-item.disabled .page-link .previous {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-pagination-disabled-color);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-disabled-color%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-disabled-color%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
}
.page-item.disabled .page-link .next {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-pagination-disabled-color);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-disabled-color%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-pagination-disabled-color%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
}

@media (max-width: 991.98px) {
  .page-item {
    margin-right: 0.25rem;
  }
  .page-item:last-child {
    margin-right: 0;
  }
}
.separator {
  display: block;
  height: 0;
  border-bottom: 1px solid var(--bs-border-color);
}

.menu {
  display: flex;
  padding: 0;
  margin: 0;
  list-style: none;
}

.menu-sub {
  display: none;
  padding: 0;
  margin: 0;
  list-style: none;
  flex-direction: column;
}

.menu-item {
  display: block;
  padding: 0.15rem 0;
}
.menu-item .menu-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0;
  flex: 0 0 100%;
  padding: 0.65rem 1rem;
  transition: none;
  outline: none !important;
}
.menu-item .menu-link .menu-title {
  display: flex;
  align-items: center;
  flex-grow: 1;
}
.menu-item .menu-link .menu-arrow {
  display: flex;
  align-items: stretch;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  margin-left: 5px;
  width: 9px;
  height: 9px;
}
.menu-item .menu-link .menu-arrow:after {
  display: block;
  width: 100%;
  content: " ";
  will-change: transform;
  background-size: 100% 100%;
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-text-muted);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-text-muted%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-text-muted%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  /*rtl:begin:remove*/
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-text-muted);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-text-muted%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-text-muted%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  /*rtl:end:remove*/
}
.menu-item .menu-content {
  padding: 0.65rem 1rem;
}

.menu-item.show .menu-link .menu-arrow:after {
  backface-visibility: hidden;
  transition: transform 0.3s ease;
}

.menu-sub-dropdown {
  display: none;
  border-radius: 0.85rem;
  background-color: var(--bs-menu-dropdown-bg-color);
  box-shadow: var(--bs-menu-dropdown-box-shadow);
  z-index: 107;
}
.show.menu-dropdown > .menu-sub-dropdown, .menu-sub-dropdown.menu.show, .menu-sub-dropdown.show[data-popper-placement] {
  display: flex;
  will-change: transform;
  animation: menu-sub-dropdown-animation-fade-in 0.3s ease 1, menu-sub-dropdown-animation-move-up 0.3s ease 1;
}

.menu-column {
  flex-direction: column;
  width: 100%;
}

.menu-rounded .menu-link {
  border-radius: 0.85rem;
}

@keyframes menu-sub-dropdown-animation-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes menu-sub-dropdown-animation-move-up {
  from {
    margin-top: 0.75rem;
  }
  to {
    margin-top: 0;
  }
}
@keyframes menu-sub-dropdown-animation-move-down {
  from {
    margin-bottom: 0.75rem;
  }
  to {
    margin-bottom: 0;
  }
}

.menu-gray-800 .menu-item .menu-link {
  color: var(--bs-gray-800);
}
.menu-gray-800 .menu-item .menu-link .menu-title {
  color: var(--bs-gray-800);
}
.menu-gray-800 .menu-item .menu-link .menu-arrow:after {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-gray-800);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-800%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-800%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  /*rtl:begin:remove*/
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-gray-800);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-800%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-gray-800%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  /*rtl:end:remove*/
}

.menu-state-bg-light-primary .menu-item.show > .menu-link {
  transition: color 0.2s ease;
  background-color: var(--bs-primary-light);
  color: var(--bs-primary);
}
.menu-state-bg-light-primary .menu-item.show > .menu-link .menu-title {
  color: var(--bs-primary);
}
.menu-state-bg-light-primary .menu-item.show > .menu-link .menu-arrow:after {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-primary);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  /*rtl:begin:remove*/
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-primary);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  /*rtl:end:remove*/
}
.menu-state-bg-light-primary .menu-item.here > .menu-link {
  transition: color 0.2s ease;
  background-color: var(--bs-primary-light);
  color: var(--bs-primary);
}
.menu-state-bg-light-primary .menu-item.here > .menu-link .menu-title {
  color: var(--bs-primary);
}
.menu-state-bg-light-primary .menu-item.here > .menu-link .menu-arrow:after {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-primary);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  /*rtl:begin:remove*/
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-primary);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  /*rtl:end:remove*/
}
.menu-state-bg-light-primary .menu-item.hover:not(.here) > .menu-link:not(.disabled):not(.active):not(.here), .menu-state-bg-light-primary .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) {
  transition: color 0.2s ease;
  background-color: var(--bs-primary-light);
  color: var(--bs-primary);
}
.menu-state-bg-light-primary .menu-item.hover:not(.here) > .menu-link:not(.disabled):not(.active):not(.here) .menu-title, .menu-state-bg-light-primary .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-title {
  color: var(--bs-primary);
}
.menu-state-bg-light-primary .menu-item.hover:not(.here) > .menu-link:not(.disabled):not(.active):not(.here) .menu-arrow:after, .menu-state-bg-light-primary .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) .menu-arrow:after {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-primary);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  /*rtl:begin:remove*/
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-primary);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  /*rtl:end:remove*/
}
.menu-state-bg-light-primary .menu-item .menu-link.active {
  transition: color 0.2s ease;
  background-color: var(--bs-primary-light);
  color: var(--bs-primary);
}
.menu-state-bg-light-primary .menu-item .menu-link.active .menu-title {
  color: var(--bs-primary);
}
.menu-state-bg-light-primary .menu-item .menu-link.active .menu-arrow:after {
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-primary);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M2.72011 2.76429L4.46358 1.02083C4.63618 0.848244 4.63617 0.568419 4.46358 0.395831C4.29099 0.223244 4.01118 0.223244 3.83861 0.395831L1.52904 2.70537C1.36629 2.86808 1.36629 3.13191 1.52904 3.29462L3.83861 5.60419C4.01117 5.77675 4.29099 5.77675 4.46358 5.60419C4.63617 5.43156 4.63617 5.15175 4.46358 4.97919L2.72011 3.23571C2.58994 3.10554 2.58994 2.89446 2.72011 2.76429Z'/%3e%3c/svg%3e");
  /*rtl:begin:remove*/
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-primary);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 6 6' fill='var%28--bs-primary%29'%3e%3cpath d='M3.27989 3.23571L1.53642 4.97917C1.36382 5.15176 1.36383 5.43158 1.53642 5.60417C1.70901 5.77676 1.98882 5.77676 2.16139 5.60417L4.47096 3.29463C4.63371 3.13192 4.63371 2.86809 4.47096 2.70538L2.16139 0.395812C1.98883 0.22325 1.70901 0.22325 1.53642 0.395812C1.36383 0.568437 1.36383 0.84825 1.53642 1.02081L3.27989 2.76429C3.41006 2.89446 3.41006 3.10554 3.27989 3.23571Z'/%3e%3c/svg%3e");
  /*rtl:end:remove*/
}

.anchor {
  display: flex;
  align-items: center;
}
.anchor a {
  position: relative;
  display: none;
  align-items: center;
  justify-content: flex-start;
  height: 1em;
  width: 1.25em;
  margin-left: -1.25em;
  font-weight: 500;
  font-size: 0.8em;
  color: var(--bs-text-muted);
  transition: all 0.2s ease-in-out;
}
.anchor a:before {
  content: "#";
}
.anchor:hover a {
  display: flex;
}
.anchor:hover a:hover {
  color: var(--bs-primary);
  transition: all 0.2s ease-in-out;
}

.card {
  --bs-card-box-shadow: var(--bs-root-card-box-shadow);
  --bs-card-border-color: var(--bs-root-card-border-color);
  border: 1px solid var(--bs-card-border-color);
}
.card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  flex-wrap: wrap;
  min-height: 70px;
  padding: 0 2.25rem;
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-bottom: 1px solid var(--bs-card-border-color);
}
.card .card-header .card-title {
  display: flex;
  align-items: center;
  margin: 0.5rem;
  margin-left: 0;
}
.card .card-header .card-title.flex-column {
  align-items: flex-start;
  justify-content: center;
}
.card .card-header .card-title,
.card .card-header .card-title .card-label {
  font-weight: 500;
  font-size: 1.275rem;
  color: var(--bs-text-gray-900);
}
.card .card-header .card-title .card-label {
  margin: 0 0.75rem 0 0;
  flex-wrap: wrap;
}
.card .card-header .card-title small, .card .card-header .card-title .small {
  color: var(--bs-text-muted);
  font-size: 1rem;
}
.card .card-header .card-title h1, .card .card-header .card-title .h1, .card .card-header .card-title h2, .card .card-header .card-title .h2, .card .card-header .card-title h3, .card .card-header .card-title .h3, .card .card-header .card-title h4, .card .card-header .card-title .h4, .card .card-header .card-title h5, .card .card-header .card-title .h5, .card .card-header .card-title h6, .card .card-header .card-title .h6 {
  margin-bottom: 0;
}
.card .card-header .card-toolbar {
  display: flex;
  align-items: center;
  margin: 0.5rem 0;
  flex-wrap: wrap;
}
.card .card-body {
  padding: 2rem 2.25rem;
  color: var(--bs-card-color);
}
.card .card-footer {
  padding: 2rem 2.25rem;
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-top: 1px solid var(--bs-card-border-color);
}
.card.card-bordered {
  box-shadow: none;
  border: 1px solid #F1F1F4;
}
.card.card-flush > .card-header {
  border-bottom: 0 !important;
}
.card.card-flush > .card-footer {
  border-top: 0 !important;
}
.card.card-shadow {
  box-shadow: var(--bs-card-box-shadow);
  border: 0;
}
.card.card-reset {
  border: 0 !important;
  box-shadow: none !important;
  background-color: transparent !important;
}
.card.card-reset > .card-header {
  border-bottom: 0 !important;
}
.card.card-reset > .card-footer {
  border-top: 0 !important;
}

.card-shadow {
  box-shadow: var(--bs-card-box-shadow);
}

.card-rounded {
  border-radius: 1rem;
}

@media (max-width: 767.98px) {
  .card > .card-header:not(.flex-nowrap) {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
[data-bs-theme=dark] .card {
  --bs-card-box-shadow: none;
}

.btn {
  --bs-btn-color: var(--bs-body-color);
  outline: none !important;
}
.btn:not(.btn-shadow):not(.shadow):not(.shadow-sm):not(.shadow-lg):not(.shadow-xs) {
  box-shadow: none;
}
.btn:not(.btn-outline):not(.btn-dashed):not(.btn-bordered):not(.border-hover):not(.border-active):not(.btn-flush):not(.btn-icon):not(.btn-hover-outline) {
  border: 0;
  padding: calc(0.775rem + 1px) calc(1.5rem + 1px);
}
.btn:not(.btn-outline):not(.btn-dashed):not(.btn-bordered):not(.border-hover):not(.border-active):not(.btn-flush):not(.btn-icon):not(.btn-hover-outline).btn-lg {
  padding: calc(0.825rem + 1px) calc(1.75rem + 1px);
}
.btn:not(.btn-outline):not(.btn-dashed):not(.btn-bordered):not(.border-hover):not(.border-active):not(.btn-flush):not(.btn-icon):not(.btn-hover-outline).btn-sm {
  padding: calc(0.55rem + 1px) calc(1rem + 1px);
}
.btn.btn-outline:not(.btn-outline-dashed) {
  border: 1px solid var(--bs-gray-300);
}

.btn > i {
  display: inline-flex;
  font-size: 1rem;
  padding-right: 0.35rem;
  vertical-align: middle;
}
.btn.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  height: calc(1.5em + 1.55rem + 2px);
  width: calc(1.5em + 1.55rem + 2px);
  line-height: 1;
}
.btn.btn-icon i {
  padding-right: 0;
}
.btn.btn-icon:not(.btn-outline):not(.btn-dashed):not(.border-hover):not(.border-active):not(.btn-flush) {
  border: 0;
}
.btn.btn-icon.btn-sm {
  height: calc(1.5em + 1.1rem + 2px);
  width: calc(1.5em + 1.1rem + 2px);
}
.btn.btn-icon.btn-lg {
  height: calc(1.5em + 1.65rem + 2px);
  width: calc(1.5em + 1.65rem + 2px);
}
.btn.btn-icon.btn-circle {
  border-radius: 50%;
}

.btn.btn-light {
  color: var(--bs-light-inverse);
  border-color: var(--bs-light);
  background-color: var(--bs-light);
}
.btn.btn-light i {
  color: var(--bs-light-inverse);
}
.btn.btn-light.dropdown-toggle:after {
  color: var(--bs-light-inverse);
}
 .btn.btn-light:focus:not(.btn-active), .btn.btn-light:hover:not(.btn-active), .btn.btn-light:active:not(.btn-active), .btn.btn-light.active, .btn.btn-light.show, .show > .btn.btn-light {
  color: var(--bs-light-inverse);
  border-color: var(--bs-light-active);
  background-color: var(--bs-light-active) !important;
}
 .btn.btn-light:focus:not(.btn-active) i, .btn.btn-light:hover:not(.btn-active) i, .btn.btn-light:active:not(.btn-active) i, .btn.btn-light.active i, .btn.btn-light.show i, .show > .btn.btn-light i {
  color: var(--bs-light-inverse);
}
 .btn.btn-light:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-light:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-light:active:not(.btn-active).dropdown-toggle:after, .btn.btn-light.active.dropdown-toggle:after, .btn.btn-light.show.dropdown-toggle:after, .show > .btn.btn-light.dropdown-toggle:after {
  color: var(--bs-light-inverse);
}

.btn.btn-bg-light {
  border-color: var(--bs-light);
  background-color: var(--bs-light);
}
 .btn.btn-active-light:focus:not(.btn-active), .btn.btn-active-light:hover:not(.btn-active), .btn.btn-active-light:active:not(.btn-active), .btn.btn-active-light.active, .btn.btn-active-light.show, .show > .btn.btn-active-light {
  color: var(--bs-light-inverse);
  border-color: var(--bs-light);
  background-color: var(--bs-light) !important;
}
 .btn.btn-active-light:focus:not(.btn-active) i, .btn.btn-active-light:hover:not(.btn-active) i, .btn.btn-active-light:active:not(.btn-active) i, .btn.btn-active-light.active i, .btn.btn-active-light.show i, .show > .btn.btn-active-light i {
  color: var(--bs-light-inverse);
}
 .btn.btn-active-light:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light:active:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light.active.dropdown-toggle:after, .btn.btn-active-light.show.dropdown-toggle:after, .show > .btn.btn-active-light.dropdown-toggle:after {
  color: var(--bs-light-inverse);
}

.btn.btn-primary {
  color: var(--bs-primary-inverse);
  border-color: var(--bs-primary);
  background-color: var(--bs-primary);
}
.btn.btn-primary i {
  color: var(--bs-primary-inverse);
}
.btn.btn-primary.dropdown-toggle:after {
  color: var(--bs-primary-inverse);
}
 .btn.btn-primary:focus:not(.btn-active), .btn.btn-primary:hover:not(.btn-active), .btn.btn-primary:active:not(.btn-active), .btn.btn-primary.active, .btn.btn-primary.show, .show > .btn.btn-primary {
  color: var(--bs-primary-inverse);
  border-color: var(--bs-primary-active);
  background-color: var(--bs-primary-active) !important;
}
 .btn.btn-primary:focus:not(.btn-active) i, .btn.btn-primary:hover:not(.btn-active) i, .btn.btn-primary:active:not(.btn-active) i, .btn.btn-primary.active i, .btn.btn-primary.show i, .show > .btn.btn-primary i {
  color: var(--bs-primary-inverse);
}
 .btn.btn-primary:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-primary:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-primary:active:not(.btn-active).dropdown-toggle:after, .btn.btn-primary.active.dropdown-toggle:after, .btn.btn-primary.show.dropdown-toggle:after, .show > .btn.btn-primary.dropdown-toggle:after {
  color: var(--bs-primary-inverse);
}

.btn.btn-light-primary {
  color: var(--bs-primary);
  border-color: var(--bs-primary-light);
  background-color: var(--bs-primary-light);
}
.btn.btn-light-primary i {
  color: var(--bs-primary);
}
.btn.btn-light-primary.dropdown-toggle:after {
  color: var(--bs-primary);
}
 .btn.btn-light-primary:focus:not(.btn-active), .btn.btn-light-primary:hover:not(.btn-active), .btn.btn-light-primary:active:not(.btn-active), .btn.btn-light-primary.active, .btn.btn-light-primary.show, .show > .btn.btn-light-primary {
  color: var(--bs-primary-inverse);
  border-color: var(--bs-primary);
  background-color: var(--bs-primary) !important;
}
 .btn.btn-light-primary:focus:not(.btn-active) i, .btn.btn-light-primary:hover:not(.btn-active) i, .btn.btn-light-primary:active:not(.btn-active) i, .btn.btn-light-primary.active i, .btn.btn-light-primary.show i, .show > .btn.btn-light-primary i {
  color: var(--bs-primary-inverse);
}
 .btn.btn-light-primary:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-light-primary:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-light-primary:active:not(.btn-active).dropdown-toggle:after, .btn.btn-light-primary.active.dropdown-toggle:after, .btn.btn-light-primary.show.dropdown-toggle:after, .show > .btn.btn-light-primary.dropdown-toggle:after {
  color: var(--bs-primary-inverse);
}

 .btn.btn-active-light-primary:focus:not(.btn-active), .btn.btn-active-light-primary:hover:not(.btn-active), .btn.btn-active-light-primary:active:not(.btn-active), .btn.btn-active-light-primary.active, .btn.btn-active-light-primary.show, .show > .btn.btn-active-light-primary {
  color: var(--bs-primary);
  border-color: var(--bs-primary-light);
  background-color: var(--bs-primary-light) !important;
}
 .btn.btn-active-light-primary:focus:not(.btn-active) i, .btn.btn-active-light-primary:hover:not(.btn-active) i, .btn.btn-active-light-primary:active:not(.btn-active) i, .btn.btn-active-light-primary.active i, .btn.btn-active-light-primary.show i, .show > .btn.btn-active-light-primary i {
  color: var(--bs-primary);
}
 .btn.btn-active-light-primary:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light-primary:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light-primary:active:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light-primary.active.dropdown-toggle:after, .btn.btn-active-light-primary.show.dropdown-toggle:after, .show > .btn.btn-active-light-primary.dropdown-toggle:after {
  color: var(--bs-primary);
}

.btn.btn-secondary {
  color: var(--bs-secondary-inverse);
  border-color: var(--bs-secondary);
  background-color: var(--bs-secondary);
}
.btn.btn-secondary i {
  color: var(--bs-secondary-inverse);
}
.btn.btn-secondary.dropdown-toggle:after {
  color: var(--bs-secondary-inverse);
}
 .btn.btn-secondary:focus:not(.btn-active), .btn.btn-secondary:hover:not(.btn-active), .btn.btn-secondary:active:not(.btn-active), .btn.btn-secondary.active, .btn.btn-secondary.show, .show > .btn.btn-secondary {
  color: var(--bs-secondary-inverse);
  border-color: var(--bs-secondary-active);
  background-color: var(--bs-secondary-active) !important;
}
 .btn.btn-secondary:focus:not(.btn-active) i, .btn.btn-secondary:hover:not(.btn-active) i, .btn.btn-secondary:active:not(.btn-active) i, .btn.btn-secondary.active i, .btn.btn-secondary.show i, .show > .btn.btn-secondary i {
  color: var(--bs-secondary-inverse);
}
 .btn.btn-secondary:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-secondary:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-secondary:active:not(.btn-active).dropdown-toggle:after, .btn.btn-secondary.active.dropdown-toggle:after, .btn.btn-secondary.show.dropdown-toggle:after, .show > .btn.btn-secondary.dropdown-toggle:after {
  color: var(--bs-secondary-inverse);
}

.btn.btn-success {
  color: var(--bs-success-inverse);
  border-color: var(--bs-success);
  background-color: var(--bs-success);
}
.btn.btn-success i {
  color: var(--bs-success-inverse);
}
.btn.btn-success.dropdown-toggle:after {
  color: var(--bs-success-inverse);
}
 .btn.btn-success:focus:not(.btn-active), .btn.btn-success:hover:not(.btn-active), .btn.btn-success:active:not(.btn-active), .btn.btn-success.active, .btn.btn-success.show, .show > .btn.btn-success {
  color: var(--bs-success-inverse);
  border-color: var(--bs-success-active);
  background-color: var(--bs-success-active) !important;
}
 .btn.btn-success:focus:not(.btn-active) i, .btn.btn-success:hover:not(.btn-active) i, .btn.btn-success:active:not(.btn-active) i, .btn.btn-success.active i, .btn.btn-success.show i, .show > .btn.btn-success i {
  color: var(--bs-success-inverse);
}
 .btn.btn-success:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-success:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-success:active:not(.btn-active).dropdown-toggle:after, .btn.btn-success.active.dropdown-toggle:after, .btn.btn-success.show.dropdown-toggle:after, .show > .btn.btn-success.dropdown-toggle:after {
  color: var(--bs-success-inverse);
}

.btn.btn-info {
  color: var(--bs-info-inverse);
  border-color: var(--bs-info);
  background-color: var(--bs-info);
}
.btn.btn-info i {
  color: var(--bs-info-inverse);
}
.btn.btn-info.dropdown-toggle:after {
  color: var(--bs-info-inverse);
}
 .btn.btn-info:focus:not(.btn-active), .btn.btn-info:hover:not(.btn-active), .btn.btn-info:active:not(.btn-active), .btn.btn-info.active, .btn.btn-info.show, .show > .btn.btn-info {
  color: var(--bs-info-inverse);
  border-color: var(--bs-info-active);
  background-color: var(--bs-info-active) !important;
}
 .btn.btn-info:focus:not(.btn-active) i, .btn.btn-info:hover:not(.btn-active) i, .btn.btn-info:active:not(.btn-active) i, .btn.btn-info.active i, .btn.btn-info.show i, .show > .btn.btn-info i {
  color: var(--bs-info-inverse);
}
 .btn.btn-info:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-info:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-info:active:not(.btn-active).dropdown-toggle:after, .btn.btn-info.active.dropdown-toggle:after, .btn.btn-info.show.dropdown-toggle:after, .show > .btn.btn-info.dropdown-toggle:after {
  color: var(--bs-info-inverse);
}

.btn.btn-outline.btn-outline-info {
  color: var(--bs-info);
  border-color: var(--bs-info);
  background-color: transparent;
}
.btn.btn-outline.btn-outline-info i {
  color: var(--bs-info);
}
.btn.btn-outline.btn-outline-info.dropdown-toggle:after {
  color: var(--bs-info);
}
 .btn.btn-outline.btn-outline-info:focus:not(.btn-active), .btn.btn-outline.btn-outline-info:hover:not(.btn-active), .btn.btn-outline.btn-outline-info:active:not(.btn-active), .btn.btn-outline.btn-outline-info.active, .btn.btn-outline.btn-outline-info.show, .show > .btn.btn-outline.btn-outline-info {
  color: var(--bs-info-active);
  border-color: var(--bs-info);
  background-color: var(--bs-info-light) !important;
}
 .btn.btn-outline.btn-outline-info:focus:not(.btn-active) i, .btn.btn-outline.btn-outline-info:hover:not(.btn-active) i, .btn.btn-outline.btn-outline-info:active:not(.btn-active) i, .btn.btn-outline.btn-outline-info.active i, .btn.btn-outline.btn-outline-info.show i, .show > .btn.btn-outline.btn-outline-info i {
  color: var(--bs-info-active);
}
 .btn.btn-outline.btn-outline-info:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-outline.btn-outline-info:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-outline.btn-outline-info:active:not(.btn-active).dropdown-toggle:after, .btn.btn-outline.btn-outline-info.active.dropdown-toggle:after, .btn.btn-outline.btn-outline-info.show.dropdown-toggle:after, .show > .btn.btn-outline.btn-outline-info.dropdown-toggle:after {
  color: var(--bs-info-active);
}

.btn.btn-warning {
  color: var(--bs-warning-inverse);
  border-color: var(--bs-warning);
  background-color: var(--bs-warning);
}
.btn.btn-warning i {
  color: var(--bs-warning-inverse);
}
.btn.btn-warning.dropdown-toggle:after {
  color: var(--bs-warning-inverse);
}
 .btn.btn-warning:focus:not(.btn-active), .btn.btn-warning:hover:not(.btn-active), .btn.btn-warning:active:not(.btn-active), .btn.btn-warning.active, .btn.btn-warning.show, .show > .btn.btn-warning {
  color: var(--bs-warning-inverse);
  border-color: var(--bs-warning-active);
  background-color: var(--bs-warning-active) !important;
}
 .btn.btn-warning:focus:not(.btn-active) i, .btn.btn-warning:hover:not(.btn-active) i, .btn.btn-warning:active:not(.btn-active) i, .btn.btn-warning.active i, .btn.btn-warning.show i, .show > .btn.btn-warning i {
  color: var(--bs-warning-inverse);
}
 .btn.btn-warning:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-warning:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-warning:active:not(.btn-active).dropdown-toggle:after, .btn.btn-warning.active.dropdown-toggle:after, .btn.btn-warning.show.dropdown-toggle:after, .show > .btn.btn-warning.dropdown-toggle:after {
  color: var(--bs-warning-inverse);
}

 .btn.btn-active-light-warning:focus:not(.btn-active), .btn.btn-active-light-warning:hover:not(.btn-active), .btn.btn-active-light-warning:active:not(.btn-active), .btn.btn-active-light-warning.active, .btn.btn-active-light-warning.show, .show > .btn.btn-active-light-warning {
  color: var(--bs-warning);
  border-color: var(--bs-warning-light);
  background-color: var(--bs-warning-light) !important;
}
 .btn.btn-active-light-warning:focus:not(.btn-active) i, .btn.btn-active-light-warning:hover:not(.btn-active) i, .btn.btn-active-light-warning:active:not(.btn-active) i, .btn.btn-active-light-warning.active i, .btn.btn-active-light-warning.show i, .show > .btn.btn-active-light-warning i {
  color: var(--bs-warning);
}
 .btn.btn-active-light-warning:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light-warning:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light-warning:active:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light-warning.active.dropdown-toggle:after, .btn.btn-active-light-warning.show.dropdown-toggle:after, .show > .btn.btn-active-light-warning.dropdown-toggle:after {
  color: var(--bs-warning);
}

.btn.btn-danger {
  color: var(--bs-danger-inverse);
  border-color: var(--bs-danger);
  background-color: var(--bs-danger);
}
.btn.btn-danger i {
  color: var(--bs-danger-inverse);
}
.btn.btn-danger.dropdown-toggle:after {
  color: var(--bs-danger-inverse);
}
 .btn.btn-danger:focus:not(.btn-active), .btn.btn-danger:hover:not(.btn-active), .btn.btn-danger:active:not(.btn-active), .btn.btn-danger.active, .btn.btn-danger.show, .show > .btn.btn-danger {
  color: var(--bs-danger-inverse);
  border-color: var(--bs-danger-active);
  background-color: var(--bs-danger-active) !important;
}
 .btn.btn-danger:focus:not(.btn-active) i, .btn.btn-danger:hover:not(.btn-active) i, .btn.btn-danger:active:not(.btn-active) i, .btn.btn-danger.active i, .btn.btn-danger.show i, .show > .btn.btn-danger i {
  color: var(--bs-danger-inverse);
}
 .btn.btn-danger:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-danger:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-danger:active:not(.btn-active).dropdown-toggle:after, .btn.btn-danger.active.dropdown-toggle:after, .btn.btn-danger.show.dropdown-toggle:after, .show > .btn.btn-danger.dropdown-toggle:after {
  color: var(--bs-danger-inverse);
}

.btn.btn-light-danger {
  color: var(--bs-danger);
  border-color: var(--bs-danger-light);
  background-color: var(--bs-danger-light);
}
.btn.btn-light-danger i {
  color: var(--bs-danger);
}
.btn.btn-light-danger.dropdown-toggle:after {
  color: var(--bs-danger);
}
 .btn.btn-light-danger:focus:not(.btn-active), .btn.btn-light-danger:hover:not(.btn-active), .btn.btn-light-danger:active:not(.btn-active), .btn.btn-light-danger.active, .btn.btn-light-danger.show, .show > .btn.btn-light-danger {
  color: var(--bs-danger-inverse);
  border-color: var(--bs-danger);
  background-color: var(--bs-danger) !important;
}
 .btn.btn-light-danger:focus:not(.btn-active) i, .btn.btn-light-danger:hover:not(.btn-active) i, .btn.btn-light-danger:active:not(.btn-active) i, .btn.btn-light-danger.active i, .btn.btn-light-danger.show i, .show > .btn.btn-light-danger i {
  color: var(--bs-danger-inverse);
}
 .btn.btn-light-danger:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-light-danger:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-light-danger:active:not(.btn-active).dropdown-toggle:after, .btn.btn-light-danger.active.dropdown-toggle:after, .btn.btn-light-danger.show.dropdown-toggle:after, .show > .btn.btn-light-danger.dropdown-toggle:after {
  color: var(--bs-danger-inverse);
}

 .btn.btn-active-light-danger:focus:not(.btn-active), .btn.btn-active-light-danger:hover:not(.btn-active), .btn.btn-active-light-danger:active:not(.btn-active), .btn.btn-active-light-danger.active, .btn.btn-active-light-danger.show, .show > .btn.btn-active-light-danger {
  color: var(--bs-danger);
  border-color: var(--bs-danger-light);
  background-color: var(--bs-danger-light) !important;
}
 .btn.btn-active-light-danger:focus:not(.btn-active) i, .btn.btn-active-light-danger:hover:not(.btn-active) i, .btn.btn-active-light-danger:active:not(.btn-active) i, .btn.btn-active-light-danger.active i, .btn.btn-active-light-danger.show i, .show > .btn.btn-active-light-danger i {
  color: var(--bs-danger);
}
 .btn.btn-active-light-danger:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light-danger:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light-danger:active:not(.btn-active).dropdown-toggle:after, .btn.btn-active-light-danger.active.dropdown-toggle:after, .btn.btn-active-light-danger.show.dropdown-toggle:after, .show > .btn.btn-active-light-danger.dropdown-toggle:after {
  color: var(--bs-danger);
}

.btn.btn-dark {
  color: var(--bs-dark-inverse);
  border-color: var(--bs-dark);
  background-color: var(--bs-dark);
}
.btn.btn-dark i {
  color: var(--bs-dark-inverse);
}
.btn.btn-dark.dropdown-toggle:after {
  color: var(--bs-dark-inverse);
}
 .btn.btn-dark:focus:not(.btn-active), .btn.btn-dark:hover:not(.btn-active), .btn.btn-dark:active:not(.btn-active), .btn.btn-dark.active, .btn.btn-dark.show, .show > .btn.btn-dark {
  color: var(--bs-dark-inverse);
  border-color: var(--bs-dark-active);
  background-color: var(--bs-dark-active) !important;
}
 .btn.btn-dark:focus:not(.btn-active) i, .btn.btn-dark:hover:not(.btn-active) i, .btn.btn-dark:active:not(.btn-active) i, .btn.btn-dark.active i, .btn.btn-dark.show i, .show > .btn.btn-dark i {
  color: var(--bs-dark-inverse);
}
 .btn.btn-dark:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-dark:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-dark:active:not(.btn-active).dropdown-toggle:after, .btn.btn-dark.active.dropdown-toggle:after, .btn.btn-dark.show.dropdown-toggle:after, .show > .btn.btn-dark.dropdown-toggle:after {
  color: var(--bs-dark-inverse);
}

.btn.btn-outline.btn-outline-dark {
  color: var(--bs-dark);
  border-color: var(--bs-dark);
  background-color: transparent;
}
.btn.btn-outline.btn-outline-dark i {
  color: var(--bs-dark);
}
.btn.btn-outline.btn-outline-dark.dropdown-toggle:after {
  color: var(--bs-dark);
}
 .btn.btn-outline.btn-outline-dark:focus:not(.btn-active), .btn.btn-outline.btn-outline-dark:hover:not(.btn-active), .btn.btn-outline.btn-outline-dark:active:not(.btn-active), .btn.btn-outline.btn-outline-dark.active, .btn.btn-outline.btn-outline-dark.show, .show > .btn.btn-outline.btn-outline-dark {
  color: var(--bs-dark-active);
  border-color: var(--bs-dark);
  background-color: var(--bs-dark-light) !important;
}
 .btn.btn-outline.btn-outline-dark:focus:not(.btn-active) i, .btn.btn-outline.btn-outline-dark:hover:not(.btn-active) i, .btn.btn-outline.btn-outline-dark:active:not(.btn-active) i, .btn.btn-outline.btn-outline-dark.active i, .btn.btn-outline.btn-outline-dark.show i, .show > .btn.btn-outline.btn-outline-dark i {
  color: var(--bs-dark-active);
}
 .btn.btn-outline.btn-outline-dark:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-outline.btn-outline-dark:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-outline.btn-outline-dark:active:not(.btn-active).dropdown-toggle:after, .btn.btn-outline.btn-outline-dark.active.dropdown-toggle:after, .btn.btn-outline.btn-outline-dark.show.dropdown-toggle:after, .show > .btn.btn-outline.btn-outline-dark.dropdown-toggle:after {
  color: var(--bs-dark-active);
}
 .btn.btn-active-color-primary:focus:not(.btn-active), .btn.btn-active-color-primary:hover:not(.btn-active), .btn.btn-active-color-primary:active:not(.btn-active), .btn.btn-active-color-primary.active, .btn.btn-active-color-primary.show, .show > .btn.btn-active-color-primary {
  color: var(--bs-text-primary);
}
 .btn.btn-active-color-primary:focus:not(.btn-active) i, .btn.btn-active-color-primary:hover:not(.btn-active) i, .btn.btn-active-color-primary:active:not(.btn-active) i, .btn.btn-active-color-primary.active i, .btn.btn-active-color-primary.show i, .show > .btn.btn-active-color-primary i {
  color: var(--bs-text-primary);
}
 .btn.btn-active-color-primary:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-active-color-primary:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-active-color-primary:active:not(.btn-active).dropdown-toggle:after, .btn.btn-active-color-primary.active.dropdown-toggle:after, .btn.btn-active-color-primary.show.dropdown-toggle:after, .show > .btn.btn-active-color-primary.dropdown-toggle:after {
  color: var(--bs-text-primary);
}

.btn.btn-color-gray-400 {
  color: var(--bs-text-gray-400);
}
.btn.btn-color-gray-400 i {
  color: var(--bs-text-gray-400);
}
.btn.btn-color-gray-400.dropdown-toggle:after {
  color: var(--bs-text-gray-400);
}

.btn.btn-light-facebook {
  color: var(--bs-facebook);
  color: #3b5998;
  border-color: rgba(59, 89, 152, 0.1);
  background-color: rgba(59, 89, 152, 0.1);
}
.btn.btn-light-facebook i {
  color: #3b5998;
}
.btn.btn-light-facebook.dropdown-toggle:after {
  color: #3b5998;
}
 .btn.btn-light-facebook:focus:not(.btn-active), .btn.btn-light-facebook:hover:not(.btn-active), .btn.btn-light-facebook:active:not(.btn-active), .btn.btn-light-facebook.active, .btn.btn-light-facebook.show, .show > .btn.btn-light-facebook {
  color: #ffffff;
  border-color: #3b5998;
  background-color: #3b5998 !important;
}
 .btn.btn-light-facebook:focus:not(.btn-active) i, .btn.btn-light-facebook:hover:not(.btn-active) i, .btn.btn-light-facebook:active:not(.btn-active) i, .btn.btn-light-facebook.active i, .btn.btn-light-facebook.show i, .show > .btn.btn-light-facebook i {
  color: #ffffff;
}
 .btn.btn-light-facebook:focus:not(.btn-active).dropdown-toggle:after, .btn.btn-light-facebook:hover:not(.btn-active).dropdown-toggle:after, .btn.btn-light-facebook:active:not(.btn-active).dropdown-toggle:after, .btn.btn-light-facebook.active.dropdown-toggle:after, .btn.btn-light-facebook.show.dropdown-toggle:after, .show > .btn.btn-light-facebook.dropdown-toggle:after {
  color: #ffffff;
}

[data-bs-theme=dark] .btn.btn-dark {
  color: #B5B7C8;
  background-color: #464852;
}
[data-bs-theme=dark] .btn.btn-dark i {
  color: #B5B7C8;
}
[data-bs-theme=dark] .btn.btn-dark.dropdown-toggle:after {
  color: #B5B7C8;
}
 [data-bs-theme=dark] .btn.btn-dark:focus:not(.btn-active), [data-bs-theme=dark] .btn.btn-dark:hover:not(.btn-active), [data-bs-theme=dark] .btn.btn-dark:active:not(.btn-active), [data-bs-theme=dark] .btn.btn-dark.active, [data-bs-theme=dark] .btn.btn-dark.show, .show > [data-bs-theme=dark] .btn.btn-dark {
  color: #bec0cf;
  background-color: #4d4f5a !important;
}
 [data-bs-theme=dark] .btn.btn-dark:focus:not(.btn-active) i, [data-bs-theme=dark] .btn.btn-dark:hover:not(.btn-active) i, [data-bs-theme=dark] .btn.btn-dark:active:not(.btn-active) i, [data-bs-theme=dark] .btn.btn-dark.active i, [data-bs-theme=dark] .btn.btn-dark.show i, .show > [data-bs-theme=dark] .btn.btn-dark i {
  color: #bec0cf;
}
 [data-bs-theme=dark] .btn.btn-dark:focus:not(.btn-active).dropdown-toggle:after, [data-bs-theme=dark] .btn.btn-dark:hover:not(.btn-active).dropdown-toggle:after, [data-bs-theme=dark] .btn.btn-dark:active:not(.btn-active).dropdown-toggle:after, [data-bs-theme=dark] .btn.btn-dark.active.dropdown-toggle:after, [data-bs-theme=dark] .btn.btn-dark.show.dropdown-toggle:after, .show > [data-bs-theme=dark] .btn.btn-dark.dropdown-toggle:after {
  color: #bec0cf;
}

.modal-header {
  justify-content: space-between;
}

code:not([class*=language-]) {
  font-weight: 400;
  color: var(--bs-code-color);
  border: 1px solid var(--bs-code-border-color);
  background-color: var(--bs-code-bg);
  border-radius: 0.3rem;
  line-height: inherit;
  font-size: 1rem;
  padding: 0.1rem 0.4rem;
  margin: 0 0.5rem;
  box-shadow: 0px 3px 9px rgba(0, 0, 0, 0.08);
}

.col-form-label {
  font-size: 1.05rem;
}

.form-control.form-control-solid {
  background-color: var(--bs-gray-100);
  border-color: var(--bs-gray-100);
  color: var(--bs-gray-700);
  transition: color 0.2s ease;
}
.form-control.form-control-solid::placeholder {
  color: var(--bs-gray-500);
}
.form-control.form-control-solid::-moz-placeholder {
  color: var(--bs-gray-500);
  opacity: 1;
}
.dropdown.show > .form-control.form-control-solid, .form-control.form-control-solid:active, .form-control.form-control-solid.active, .form-control.form-control-solid:focus, .form-control.form-control-solid.focus {
  background-color: var(--bs-gray-200);
  border-color: var(--bs-gray-200);
  color: var(--bs-gray-700);
  transition: color 0.2s ease;
}

.form-floating .form-control.form-control-solid::placeholder {
  color: transparent;
}

.form-floating > :disabled ~ label::after,
.form-floating > :focus ~ label::after {
  background-color: transparent !important;
}

.form-select {
  appearance: none;
}
.form-select:focus {
  border-color: var(--bs-gray-400);
  box-shadow: false, 0 0 0 0.25rem rgba(var(--bs-component-active-bg), 0.25);
}
.form-select:disabled {
  color: var(--bs-gray-500);
  background-color: var(--bs-gray-200);
  border-color: var(--bs-gray-300);
}
.form-select:-moz-focusring {
  text-shadow: 0 0 0 var(--bs-gray-700);
}
.form-select.form-select-solid {
  background-color: var(--bs-gray-100);
  border-color: var(--bs-gray-100);
  color: var(--bs-gray-700);
  transition: color 0.2s ease;
}
.form-select.form-select-solid::placeholder {
  color: var(--bs-gray-500);
}
.form-select.form-select-solid::-moz-placeholder {
  color: var(--bs-gray-500);
  opacity: 1;
}
.dropdown.show > .form-select.form-select-solid, .form-select.form-select-solid:active, .form-select.form-select-solid.active, .form-select.form-select-solid:focus, .form-select.form-select-solid.focus {
  background-color: var(--bs-gray-200);
  border-color: var(--bs-gray-200) !important;
  color: var(--bs-gray-700);
  transition: color 0.2s ease;
}

.form-check:not(.form-switch) .form-check-input[type=checkbox] {
  background-size: 60% 60%;
}
.form-check.form-check-sm .form-check-input {
  height: 1.55rem;
  width: 1.55rem;
}
.form-check.form-check-solid .form-check-input {
  border: 0;
}
.form-check.form-check-solid .form-check-input:not(:checked) {
  background-color: var(--bs-gray-200);
}
.form-check.form-check-solid .form-check-input[type=checkbox]:indeterminate {
  background-color: #2AA29E;
}
.form-check.form-check-success .form-check-input:checked {
  background-color: var(--bs-success);
}

.form-check-custom {
  display: flex;
  align-items: center;
  padding-left: 0;
  margin: 0;
}
.form-check-custom .form-check-input {
  margin: 0;
  float: none;
  flex-shrink: 0;
}
.form-check-custom .form-check-label {
  margin-left: 0.55rem;
}

.form-switch.form-check-custom .form-check-input {
  height: 2.25rem;
}
.form-switch.form-check-custom.form-switch-lg .form-check-input {
  height: 2.75rem;
  width: 3.75rem;
}
.form-switch.form-check-solid .form-check-input {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
}
.form-switch.form-check-solid .form-check-input:not(:checked) {
  background-color: var(--bs-gray-200);
}

[data-bs-theme=dark] .form-switch .form-check-input:focus:not(:checked) {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255, 255, 255, 0.25%29'/%3e%3c/svg%3e");
}

.form-check-image {
  position: relative;
  overflow: hidden;
}
.form-check-image img {
  max-width: 100%;
}
.form-check-image .form-check-label {
  font-weight: 600;
  margin-left: 0.5rem;
}
.form-check-image.disabled {
  opacity: 0.65;
}

.form-floating .form-control.form-control-solid::placeholder {
  color: transparent;
}

.form-floating > :disabled ~ label::after,
.form-floating > :focus ~ label::after {
  background-color: transparent !important;
}

.required:after {
  content: "*";
  position: relative;
  font-size: inherit;
  color: var(--bs-danger);
  padding-left: 0.25rem;
  font-weight: 600;
}

.table:not(.table-bordered) > :not(:first-child) {
  border-color: transparent;
  border-width: 0;
  border-style: none;
}
.table:not(.table-bordered) > :not(:last-child) > :last-child > * {
  border-bottom-color: inherit;
}
.table:not(.table-bordered) tr, .table:not(.table-bordered) th, .table:not(.table-bordered) td {
  border-color: inherit;
  border-width: inherit;
  border-style: inherit;
  text-transform: inherit;
  font-weight: inherit;
  font-size: inherit;
  color: inherit;
  height: inherit;
  min-height: inherit;
}
.table:not(.table-bordered) tr:first-child, .table:not(.table-bordered) th:first-child, .table:not(.table-bordered) td:first-child {
  padding-left: 0;
}
.table:not(.table-bordered) tr:last-child, .table:not(.table-bordered) th:last-child, .table:not(.table-bordered) td:last-child {
  padding-right: 0;
}
.table:not(.table-bordered) tfoot tr:last-child,
.table:not(.table-bordered) tbody tr:last-child {
  border-bottom: 0 !important;
}
.table:not(.table-bordered) tfoot tr:last-child th, .table:not(.table-bordered) tfoot tr:last-child td,
.table:not(.table-bordered) tbody tr:last-child th,
.table:not(.table-bordered) tbody tr:last-child td {
  border-bottom: 0 !important;
}
.table:not(.table-bordered) tfoot th, .table:not(.table-bordered) tfoot td {
  border-top: inherit;
}
.table:not(.table-bordered).table-row-bordered tr {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: var(--bs-border-color);
}
.table:not(.table-bordered).table-row-bordered tfoot th, .table:not(.table-bordered).table-row-bordered tfoot td {
  border-top-width: 1px !important;
}
.table:not(.table-bordered).table-row-dashed tr {
  border-bottom-width: 1px;
  border-bottom-style: dashed;
  border-bottom-color: var(--bs-border-color);
}
.table:not(.table-bordered).table-row-dashed tfoot th, .table:not(.table-bordered).table-row-dashed tfoot td {
  border-top-width: 1px !important;
}

.table.gs-0 th:first-child, .table.gs-0 td:first-child {
  padding-left: 0rem;
}
.table.gs-0 th:last-child, .table.gs-0 td:last-child {
  padding-right: 0rem;
}
.table.gs-0 th.dtr-control:first-child, .table.gs-0 td.dtr-control:first-child {
  padding-left: 0rem !important;
}

.table.g-1 th, .table.g-1 td {
  padding: 0.25rem;
}
.table.g-1 th.dtr-control, .table.g-1 td.dtr-control {
  padding-left: 0.25rem !important;
}

.table.g-2 th, .table.g-2 td {
  padding: 0.5rem;
}
.table.g-2 th.dtr-control, .table.g-2 td.dtr-control {
  padding-left: 0.5rem !important;
}

.table.g-3 th, .table.g-3 td {
  padding: 0.75rem;
}
.table.g-3 th.dtr-control, .table.g-3 td.dtr-control {
  padding-left: 0.75rem !important;
}

.table.gy-4 th, .table.gy-4 td {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.table.g-5 th, .table.g-5 td {
  padding: 1.25rem;
}
.table.g-5 th.dtr-control, .table.g-5 td.dtr-control {
  padding-left: 1.25rem !important;
}

.table.gy-5 th, .table.gy-5 td {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.table.g-6 th, .table.g-6 td {
  padding: 1.5rem;
}
.table.g-6 th.dtr-control, .table.g-6 td.dtr-control {
  padding-left: 1.5rem !important;
}

.table.gy-6 th, .table.gy-6 td {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.table.gs-9 th:first-child, .table.gs-9 td:first-child {
  padding-left: 2.25rem;
}
.table.gs-9 th:last-child, .table.gs-9 td:last-child {
  padding-right: 2.25rem;
}
.table.gs-9 th.dtr-control:first-child, .table.gs-9 td.dtr-control:first-child {
  padding-left: 2.25rem !important;
}
@media (min-width: 992px) {
  .table.g-lg-9 th, .table.g-lg-9 td {
    padding: 2.25rem;
  }
  .table.g-lg-9 th.dtr-control, .table.g-lg-9 td.dtr-control {
    padding-left: 2.25rem !important;
  }
}
@media (min-width: 1200px) {
  .table.g-xl-9 th, .table.g-xl-9 td {
    padding: 2.25rem;
  }
  .table.g-xl-9 th.dtr-control, .table.g-xl-9 td.dtr-control {
    padding-left: 2.25rem !important;
  }
}
@media (min-width: 1400px) {
  .table.g-xxl-10 th, .table.g-xxl-10 td {
    padding: 2.5rem;
  }
  .table.g-xxl-10 th.dtr-control, .table.g-xxl-10 td.dtr-control {
    padding-left: 2.5rem !important;
  }
}
.popover {
  --bs-popover-header-border-color: #F1F1F4;
}
.popover .popover-header {
  font-size: 1rem;
  font-weight: 500;
  border-bottom: 1px solid var(--bs-popover-header-border-color);
}
.popover .popover-dismiss {
  position: absolute;
  top: 0.85rem;
  right: 0.85rem;
  height: 1.25rem;
  width: 1.25rem;
  mask-repeat: no-repeat;
  mask-position: center;
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  background-color: var(--bs-gray-500);
  -webkit-mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='var%28--bs-gray-500%29'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e");
  mask-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='var%28--bs-gray-500%29'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e");
  mask-size: 50%;
  -webkit-mask-size: 50%;
}
.popover .popover-dismiss:hover {
  background-color: var(--bs-primary);
}
.popover .popover-dismiss + .popover-header {
  padding-right: 2.5rem;
}

[data-bs-theme=dark] .popover:not(.popover-inverse) {
  --bs-popover-bg: #26272F;
  --bs-popover-border-color: #26272F;
  --bs-popover-header-bg: #26272F;
  --bs-popover-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.3);
  --bs-popover-header-border-color: #363843;
}

.tooltip .tooltip-inner {
  box-shadow: var(--bs-tooltip-box-shadow);
}

[data-bs-theme=dark] .tooltip:not(.tooltip-inverse) {
  --bs-tooltip-bg: #26272F;
  --bs-tooltip-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.15);
}

.image-input {
  position: relative;
  display: inline-block;
  border-radius: 0.85rem;
  background-repeat: no-repeat;
  background-size: cover;
}
.image-input:not(.image-input-empty) {
  background-image: none !important;
}
.image-input .image-input-wrapper {
  width: 120px;
  height: 120px;
  border-radius: 0.85rem;
  background-repeat: no-repeat;
  background-size: cover;
}
.image-input [data-kt-image-input-action] {
  cursor: pointer;
  position: absolute;
  transform: translate(-50%, -50%);
}
.image-input [data-kt-image-input-action=change] {
  left: 100%;
  top: 0;
}
.image-input [data-kt-image-input-action=change] input {
  width: 0 !important;
  height: 0 !important;
  overflow: hidden;
  opacity: 0;
}
.image-input [data-kt-image-input-action=cancel],
.image-input [data-kt-image-input-action=remove] {
  position: absolute;
  left: 100%;
  top: 100%;
}
.image-input [data-kt-image-input-action=cancel] {
  display: none;
}
.image-input.image-input-changed [data-kt-image-input-action=cancel] {
  display: flex;
}
.image-input.image-input-changed [data-kt-image-input-action=remove] {
  display: none;
}
.image-input.image-input-empty [data-kt-image-input-action=remove],
.image-input.image-input-empty [data-kt-image-input-action=cancel] {
  display: none;
}
.image-input.image-input-outline .image-input-wrapper {
  border: 3px solid var(--bs-body-bg);
  box-shadow: var(--bs-box-shadow);
}

.symbol {
  display: inline-block;
  flex-shrink: 0;
  position: relative;
  border-radius: 0.85rem;
}
.symbol .symbol-label {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: var(--bs-symbol-label-color);
  background-color: var(--bs-symbol-label-bg);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  border-radius: 0.85rem;
}
.symbol .symbol-label:after {
  border-radius: 0.85rem;
}
.symbol > img {
  width: 100%;
  flex-shrink: 0;
  display: inline-block;
  border-radius: 0.85rem;
}
.symbol.symbol-circle,
.symbol.symbol-circle > img,
.symbol.symbol-circle .symbol-label {
  border-radius: 50%;
}
.symbol.symbol-circle:after,
.symbol.symbol-circle > img:after,
.symbol.symbol-circle .symbol-label:after {
  border-radius: 50%;
}
.symbol > img {
  width: 50px;
  height: 50px;
}
.symbol .symbol-label {
  width: 50px;
  height: 50px;
}
.symbol.symbol-25px > img {
  width: 25px;
  height: 25px;
}
.symbol.symbol-25px .symbol-label {
  width: 25px;
  height: 25px;
}
.symbol.symbol-35px > img {
  width: 35px;
  height: 35px;
}
.symbol.symbol-35px .symbol-label {
  width: 35px;
  height: 35px;
}
.symbol.symbol-75px > img {
  width: 75px;
  height: 75px;
}
.symbol.symbol-75px .symbol-label {
  width: 75px;
  height: 75px;
}
@media (min-width: 992px) {
  .symbol.symbol-lg-45px > img {
    width: 45px;
    height: 45px;
  }
  .symbol.symbol-lg-45px .symbol-label {
    width: 45px;
    height: 45px;
  }
}

.symbol-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 10px;
}
.symbol-group .symbol {
  position: relative;
  z-index: 0;
  margin-left: -10px;
  transition: all 0.3s ease;
}
.symbol-group .symbol:hover {
  transition: all 0.3s ease;
  z-index: 1;
}
.symbol-group .symbol-label {
  position: relative;
}
.symbol-group .symbol-label:after {
  display: block;
  content: " ";
  border-radius: inherit;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  border: 2px solid var(--bs-symbol-border-color);
  -webkit-background-clip: padding-box; /* for Safari */
  background-clip: padding-box; /* for IE9+, Firefox 4+, Opera, Chrome */
}
.symbol-group.symbol-hover .symbol {
  cursor: pointer;
}

@keyframes animation-pulse {
  0% {
    -webkit-transform: scale(0.1, 0.1);
    opacity: 0;
  }
  60% {
    -webkit-transform: scale(0.1, 0.1);
    opacity: 0;
  }
  65% {
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(1.2, 1.2);
    opacity: 0;
  }
}
[data-kt-app-page-loading=on] *,
.page-loading * {
  transition: none !important;
}

.scrolltop {
  position: fixed;
  display: none;
  cursor: pointer;
  z-index: 105;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  bottom: 43px;
  right: 7px;
  background-color: var(--bs-scrolltop-bg-color);
  box-shadow: var(--bs-scrolltop-box-shadow);
  opacity: 0;
  transition: color 0.2s ease;
  border-radius: 0.85rem;
}
.scrolltop > i {
  font-size: 1.3rem;
  color: var(--bs-scrolltop-icon-color);
}
.scrolltop:hover {
  background-color: var(--bs-scrolltop-bg-color-hover);
}

.scrolltop:hover i {
  color: var(--bs-scrolltop-icon-color-hover);
}
[data-kt-scrolltop=on] .scrolltop {
  opacity: var(--bs-scrolltop-opacity-on);
  animation: animation-scrolltop 0.4s ease-out 1;
  display: flex;
}
[data-kt-scrolltop=on] .scrolltop:hover {
  transition: color 0.2s ease;
  opacity: var(--bs-scrolltop-opacity-hover);
}

@media (max-width: 991.98px) {
  .scrolltop {
    bottom: 23px;
    right: 5px;
    width: 30px;
    height: 30px;
  }
}
@keyframes animation-scrolltop {
  from {
    margin-bottom: -15px;
  }
  to {
    margin-bottom: 0;
  }
}

.fixed-top {
  position: fixed;
  z-index: 101;
  top: 0;
  left: 0;
  right: 0;
}
.timeline {
  --bs-timeline-icon-size: 38px;
  --bs-timeline-icon-space: 0.35rem;
}

.overlay {
  position: relative;
}

.bullet {
  display: inline-block;
  background-color: var(--bs-bullet-bg-color);
  border-radius: 6px;
  width: 8px;
  height: 4px;
  flex-shrink: 0;
}

.drawer {
  display: flex !important;
  overflow: auto;
  z-index: 110;
  position: fixed;
  top: 0;
  bottom: 0;
  background-color: var(--bs-drawer-bg-color);
  transition: transform 0.3s ease-in-out !important;
}
.drawer.drawer-on {
  transform: none;
  box-shadow: var(--bs-drawer-box-shadow);
  transition: transform 0.3s ease-in-out !important;
}

.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  overflow: hidden;
  z-index: 109;
  background-color: var(--bs-drawer-overlay-bg-color);
  animation: animation-drawer-fade-in 0.3s ease-in-out 1;
}

[data-kt-drawer=true] {
  display: none;
}

@keyframes animation-drawer-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@media (max-width: 991.98px) {
  body[data-kt-drawer=on] {
    overflow: hidden;
  }
}
.badge {
  display: inline-flex;
  align-items: center;
}

.badge-light-primary {
  background-color: var(--bs-primary-light);
  color: var(--bs-primary);
}

.badge-success {
  color: var(--bs-success-inverse);
  background-color: var(--bs-success);
}
.badge-success.badge-outline {
  border: 1px solid var(--bs-success);
  background-color: transparent;
  color: var(--bs-success);
}

.badge-light-success {
  background-color: var(--bs-success-light);
  color: var(--bs-success);
}

.badge-warning {
  color: var(--bs-warning-inverse);
  background-color: var(--bs-warning);
}
.badge-warning.badge-outline {
  border: 1px solid var(--bs-warning);
  background-color: transparent;
  color: var(--bs-warning);
}

.badge-light-warning {
  background-color: var(--bs-warning-light);
  color: var(--bs-warning);
}

.badge-danger {
  color: var(--bs-danger-inverse);
  background-color: var(--bs-danger);
}
.badge-danger.badge-outline {
  border: 1px solid var(--bs-danger);
  background-color: transparent;
  color: var(--bs-danger);
}

.indicator-progress {
  display: none;
}
[data-kt-indicator=on] > .indicator-progress {
  display: inline-block;
}

[data-kt-indicator=on] > .indicator-label {
  display: none;
}

.hover-elevate-up {
  transition: transform 0.3s ease;
}
.hover-elevate-up:hover {
  transform: translateY(-2.5%);
  transition: transform 0.3s ease;
  will-change: transform;
}

.rotate {
  display: inline-flex;
  align-items: center;
}

@media (min-width: 992px) {
  main,
  span,
  ol,
  ul,
  pre,
  div {
    scrollbar-width: thin;
    scrollbar-color: var(--bs-scrollbar-color) transparent;
  }
  main::-webkit-scrollbar,
  span::-webkit-scrollbar,
  ol::-webkit-scrollbar,
  ul::-webkit-scrollbar,
  pre::-webkit-scrollbar,
  div::-webkit-scrollbar {
    width: var(--bs-scrollbar-size);
    height: var(--bs-scrollbar-size);
  }
  main ::-webkit-scrollbar-track,
  span ::-webkit-scrollbar-track,
  ol ::-webkit-scrollbar-track,
  ul ::-webkit-scrollbar-track,
  pre ::-webkit-scrollbar-track,
  div ::-webkit-scrollbar-track {
    background-color: transparent;
  }
  main ::-webkit-scrollbar-thumb,
  span ::-webkit-scrollbar-thumb,
  ol ::-webkit-scrollbar-thumb,
  ul ::-webkit-scrollbar-thumb,
  pre ::-webkit-scrollbar-thumb,
  div ::-webkit-scrollbar-thumb {
    border-radius: var(--bs-scrollbar-size);
  }
  main::-webkit-scrollbar-thumb,
  span::-webkit-scrollbar-thumb,
  ol::-webkit-scrollbar-thumb,
  ul::-webkit-scrollbar-thumb,
  pre::-webkit-scrollbar-thumb,
  div::-webkit-scrollbar-thumb {
    background-color: var(--bs-scrollbar-color);
  }
  main::-webkit-scrollbar-corner,
  span::-webkit-scrollbar-corner,
  ol::-webkit-scrollbar-corner,
  ul::-webkit-scrollbar-corner,
  pre::-webkit-scrollbar-corner,
  div::-webkit-scrollbar-corner {
    background-color: transparent;
  }
  main:hover,
  span:hover,
  ol:hover,
  ul:hover,
  pre:hover,
  div:hover {
    scrollbar-color: var(--bs-scrollbar-hover-color) transparent;
  }
  main:hover::-webkit-scrollbar-thumb,
  span:hover::-webkit-scrollbar-thumb,
  ol:hover::-webkit-scrollbar-thumb,
  ul:hover::-webkit-scrollbar-thumb,
  pre:hover::-webkit-scrollbar-thumb,
  div:hover::-webkit-scrollbar-thumb {
    background-color: var(--bs-scrollbar-hover-color);
  }
  main:hover::-webkit-scrollbar-corner,
  span:hover::-webkit-scrollbar-corner,
  ol:hover::-webkit-scrollbar-corner,
  ul:hover::-webkit-scrollbar-corner,
  pre:hover::-webkit-scrollbar-corner,
  div:hover::-webkit-scrollbar-corner {
    background-color: transparent;
  }
}
.scroll {
  overflow: scroll;
  position: relative;
}
@media (max-width: 991.98px) {
  .scroll {
    overflow: auto;
  }
}
.rating {
  display: flex;
  align-items: center;
}

.stepper [data-kt-stepper-element=info],
.stepper [data-kt-stepper-element=content] {
  display: none;
}
.stepper [data-kt-stepper-element=info].current,
.stepper [data-kt-stepper-element=content].current {
  display: flex;
}
.stepper [data-kt-stepper-action=final] {
  display: none;
}
.stepper [data-kt-stepper-action=previous] {
  display: none;
}
.stepper [data-kt-stepper-action=next] {
  display: inline-block;
}
.stepper [data-kt-stepper-action=submit] {
  display: none;
}
.stepper.first [data-kt-stepper-action=previous] {
  display: none;
}
.stepper.first [data-kt-stepper-action=next] {
  display: inline-block;
}
.stepper.first [data-kt-stepper-action=submit] {
  display: none;
}
.stepper.between [data-kt-stepper-action=previous] {
  display: inline-block;
}
.stepper.between [data-kt-stepper-action=next] {
  display: inline-block;
}
.stepper.between [data-kt-stepper-action=submit] {
  display: none;
}
.stepper.last [data-kt-stepper-action=final] {
  display: inline-block;
}
.stepper.last [data-kt-stepper-action=previous] {
  display: inline-block;
}
.stepper.last [data-kt-stepper-action=next] {
  display: none;
}
.stepper.last [data-kt-stepper-action=submit] {
  display: inline-block;
}

.blockui {
  position: relative;
}
.blockui .blockui-overlay {
  transition: all 0.3s ease;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bs-blockui-overlay-bg);
}
.blockui .blockui-overlay .spinner-border {
  height: 1.35rem;
  width: 1.35rem;
}

[data-bs-theme=dark] .blockui {
  --bs-blockui-overlay-bg: rgba(255, 255, 255, 0.05);
  --bs-blockui-message-bg: #26272F;
  --bs-blockui-message-box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.15);
}

.cookiealert {
  background: inherit;
  color: inherit;
}
.bg-white {
  --bs-bg-rgb-color: var(--bs-white-bg-rgb);
  background-color: #ffffff !important;
}

.bg-body {
  --bs-bg-rgb-color: var(--bs-body-bg-rgb);
  background-color: var(--bs-body-bg) !important;
}

.bg-primary {
  --bs-bg-rgb-color: var(--bs-primary-rgb);
  background-color: var(--bs-primary) !important;
}

.bg-success {
  --bs-bg-rgb-color: var(--bs-success-rgb);
  background-color: var(--bs-success) !important;
}

.bg-info {
  --bs-bg-rgb-color: var(--bs-info-rgb);
  background-color: var(--bs-info) !important;
}

.bg-warning {
  --bs-bg-rgb-color: var(--bs-warning-rgb);
  background-color: var(--bs-warning) !important;
}

.bg-light-danger {
  background-color: var(--bs-danger-light) !important;
}

.bg-danger {
  --bs-bg-rgb-color: var(--bs-danger-rgb);
  background-color: var(--bs-danger) !important;
}

.bg-dark {
  --bs-bg-rgb-color: var(--bs-dark-rgb);
  background-color: var(--bs-dark) !important;
}

.bg-gray-100 {
  --bs-bg-rgb-color: var(--bs-gray-100-rgb);
  background-color: var(--bs-gray-100);
}

.bg-gray-200 {
  --bs-bg-rgb-color: var(--bs-gray-200-rgb);
  background-color: var(--bs-gray-200);
}

.bg-gray-300 {
  --bs-bg-rgb-color: var(--bs-gray-300-rgb);
  background-color: var(--bs-gray-300);
}

.bg-gray-400 {
  --bs-bg-rgb-color: var(--bs-gray-400-rgb);
  background-color: var(--bs-gray-400);
}

.border-gray-200 {
  border-color: var(--bs-gray-200) !important;
}

.border-gray-300 {
  border-color: var(--bs-gray-300) !important;
}

.border-dashed {
  border-style: dashed !important;
  border-color: var(--bs-border-dashed-color);
}

.rounded-top-0 {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.rounded-bottom-0 {
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.rounded-start-0 {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.rounded-end-0 {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.flex-row-fluid {
  flex: 1 auto;
  min-width: 0;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-start {
  justify-content: start;
  align-items: start;
}

.flex-end {
  justify-content: flex-end;
  align-items: flex-end;
}

.flex-stack {
  justify-content: space-between;
  align-items: center;
}

.shadow {
  box-shadow: var(--bs-box-shadow);
}

.text-white {
  color: var(--bs-text-white) !important;
}

.text-primary {
  color: var(--bs-text-primary) !important;
}

.text-hover-primary {
  transition: color 0.2s ease;
}
.text-hover-primary i {
  transition: color 0.2s ease;
}
.text-hover-primary:hover {
  transition: color 0.2s ease;
  color: var(--bs-text-primary) !important;
}
.text-hover-primary:hover i {
  transition: color 0.2s ease;
  color: var(--bs-text-primary) !important;
}

.text-light {
  color: var(--bs-text-light) !important;
}

.text-success {
  color: var(--bs-text-success) !important;
}

.text-inverse-success {
  color: var(--bs-success-inverse) !important;
}

.text-info {
  color: var(--bs-text-info) !important;
}

.text-inverse-info {
  color: var(--bs-info-inverse) !important;
}

.text-warning {
  color: var(--bs-text-warning) !important;
}

.text-danger {
  color: var(--bs-text-danger) !important;
}

.text-dark {
  color: var(--bs-text-dark) !important;
}

.text-hover-dark {
  transition: color 0.2s ease;
}
.text-hover-dark i {
  transition: color 0.2s ease;
}
.text-hover-dark:hover {
  transition: color 0.2s ease;
  color: var(--bs-text-dark) !important;
}
.text-hover-dark:hover i {
  transition: color 0.2s ease;
  color: var(--bs-text-dark) !important;
}

.text-muted {
  color: var(--bs-text-muted) !important;
}

.text-gray-400 {
  color: var(--bs-text-gray-400) !important;
}

.text-gray-500 {
  color: var(--bs-text-gray-500) !important;
}

.text-gray-600 {
  color: var(--bs-text-gray-600) !important;
}

.text-gray-700 {
  color: var(--bs-text-gray-700) !important;
}

.text-gray-800 {
  color: var(--bs-text-gray-800) !important;
}

.text-gray-900 {
  color: var(--bs-text-gray-900) !important;
}

.cursor-pointer {
  cursor: pointer;
}

i.bi, i[class^=fa-], i[class*=" fa-"] {
  line-height: 1;
  font-size: 1rem;
  color: var(--bs-text-muted);
}

a {
  transition: color 0.2s ease;
}
a:hover {
  transition: color 0.2s ease;
}

.tree {
  --bs-tree-icon-size: 16px;
  --bs-tree-icon-gap: 14px;
  --bs-tree-icon-color-open: var(--bs-success);
  --bs-tree-icon-color-default: var(--bs-gray-500);
  --bs-tree-icon-color-close: var(--bs-gray-500);
  --bs-tree-line-color: var(--bs-gray-200);
}
[data-bs-theme=light] {
  --bs-app-bg-color: #F1F1F4;
  --bs-app-blank-bg-color: #F1F1F4;
  --bs-app-header-base-bg-color: #F1F1F4;
  --bs-app-header-base-border-bottom: 1px solid #F1F1F4;
  --bs-app-toolbar-base-bg-color: #ffffff;
  --bs-app-toolbar-sticky-bg-color: #ffffff;
  --bs-app-toolbar-sticky-box-shadow: 0px 10px 30px 0px rgba(82, 63, 105, 0.05);
  --bs-app-sidebar-base-bg-color: transparent;
  --bs-app-sidebar-panel-base-bg-color: #ffffff;
}

[data-bs-theme=dark] {
  --bs-app-bg-color: #0F1014;
  --bs-app-blank-bg-color: #0F1014;
  --bs-app-header-base-bg-color: #0F1014;
  --bs-app-header-base-border-bottom: 1px solid #F9F9F9;
  --bs-app-toolbar-base-bg-color: #15171C;
  --bs-app-toolbar-sticky-bg-color: #1e1e2d;
  --bs-app-toolbar-sticky-box-shadow: none;
  --bs-app-sidebar-base-bg-color: transparent;
  --bs-app-sidebar-panel-base-bg-color: #15171C;
}

html {
  font-family: sans-serif;
  text-size-adjust: 100%;
}

html,
body {
  height: 100%;
  margin: 0px;
  padding: 0px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 13px !important;
  font-weight: 400;
  font-family: Inter, Helvetica, "sans-serif";
}
@media (max-width: 991.98px) {
  html,
  body {
    font-size: 12px !important;
  }
}
@media (max-width: 767.98px) {
  html,
  body {
    font-size: 12px !important;
  }
}

body {
  display: flex;
  flex-direction: column;
}
body a:hover,
body a:active,
body a:focus {
  text-decoration: none !important;
}

canvas {
  user-select: none;
}

body {
  background-color: var(--bs-app-bg-color);
}

[data-kt-app-page-loading=on] {
  overflow: hidden;
}

[data-kt-app-page-loading=on] * {
  transition: none !important;
}

@media (min-width: 992px) {
  :root {
    --bs-app-header-height: 100px;
    --bs-app-header-height-actual: 100px;
  }
}
@media (max-width: 991.98px) {
  :root {
    --bs-app-header-height: 80px;
  }
}

@media (min-width: 992px) {
  :root {
    --bs-app-toolbar-height: ;
    --bs-app-toolbar-height-actual: ;
  }
}
@media (max-width: 991.98px) {
  :root {
    --bs-app-toolbar-height: ;
  }
}

@media (min-width: 992px) {
  :root {
    --bs-app-sidebar-width: 120px;
    --bs-app-sidebar-width-actual: 120px;
    --bs-app-sidebar-gap-start: 0px;
    --bs-app-sidebar-gap-end: 0px;
    --bs-app-sidebar-gap-top: 0px;
    --bs-app-sidebar-gap-bottom: 0px;
  }
}
@media (max-width: 991.98px) {
  :root {
    --bs-app-sidebar-width: 70px;
    --bs-app-sidebar-width-actual: 70px;
    --bs-app-sidebar-gap-start: 0px;
    --bs-app-sidebar-gap-end: 0px;
    --bs-app-sidebar-gap-top: 0px;
    --bs-app-sidebar-gap-bottom: 0px;
  }
}

@media (min-width: 992px) {
  :root {
    --bs-app-sidebar-primary-width-actual: 100px;
  }
}
@media (max-width: 991.98px) {
  :root {
    --bs-app-sidebar-primary-width-actual: 100px;
  }
}

@media (min-width: 992px) {
  :root {
    --bs-app-sidebar-panel-width: 300px;
    --bs-app-sidebar-panel-width-actual: 300px;
    --bs-app-sidebar-panel-gap-start: 0px;
    --bs-app-sidebar-panel-gap-end: 0px;
    --bs-app-sidebar-panel-gap-top: 30px;
    --bs-app-sidebar-panel-gap-bottom: 30px;
  }
}
@media (max-width: 991.98px) {
  :root {
    --bs-app-sidebar-panel-width: 300px;
    --bs-app-sidebar-panel-width-actual: 300px;
    --bs-app-sidebar-panel-gap-start: 0px;
    --bs-app-sidebar-panel-gap-end: 0px;
    --bs-app-sidebar-panel-gap-top: 0px;
    --bs-app-sidebar-panel-gap-bottom: 0px;
  }
}

@media (min-width: 992px) {
  :root {
    --bs-app-aside-width: 300px;
    --bs-app-aside-width-actual: 300px;
    --bs-app-aside-gap-start: 0px;
    --bs-app-aside-gap-end: 0px;
    --bs-app-aside-gap-top: 0px;
    --bs-app-aside-gap-bottom: 0px;
  }
}
@media (max-width: 991.98px) {
  :root {
    --bs-app-aside-width: 275px;
    --bs-app-aside-width-actual: 275px;
    --bs-app-aside-gap-start: 0px;
    --bs-app-aside-gap-end: 0px;
    --bs-app-aside-gap-top: 0px;
    --bs-app-aside-gap-bottom: 0px;
  }
}

@media (min-width: 992px) {
  :root {
    --bs-app-footer-height: auto;
  }
}
@media (max-width: 991.98px) {
  body {
    --bs-app-footer-height: auto;
  }
}