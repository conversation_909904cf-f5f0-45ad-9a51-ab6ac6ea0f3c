!function(){"use strict";var e=document.querySelector(".cookiealert"),t=document.querySelector(".acceptcookies");e&&(e.offsetHeight,function(e){for(var t="acceptCookies=",o=decodeURIComponent(document.cookie).split(";"),c=0;c<o.length;c++){for(var n=o[c];" "===n.charAt(0);)n=n.substring(1);if(0===n.indexOf(t))return n.substring(t.length,n.length)}return""}()||e.classList.add("show"),t.addEventListener("click",(function(){!function(e,t,o){var c=new Date;c.setTime(c.getTime()+31536e6);var n="expires="+c.toUTCString();document.cookie="acceptCookies=true;"+n+";path=/"}(),e.classList.remove("show"),window.dispatchEvent(new Event("cookieAlertAccept"))})))}();