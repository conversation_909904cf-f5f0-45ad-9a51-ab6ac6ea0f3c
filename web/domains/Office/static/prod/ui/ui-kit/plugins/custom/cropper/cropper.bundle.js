/*!
 * Cropper.js v1.6.2
 * https://fengyuanchen.github.io/cropperjs
 *
 * Copyright 2015-present <PERSON>
 * Released under the MIT license
 *
 * Date: 2024-04-21T07:43:05.335Z
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Cropper=e()}(this,(function(){"use strict";function t(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function e(e){for(var i=1;i<arguments.length;i++){var a=null!=arguments[i]?arguments[i]:{};i%2?t(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function i(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var a=i.call(t,e);if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t,"string");return"symbol"==typeof e?e:e+""}function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,e){for(var a=0;a<e.length;a++){var n=e[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,i(n.key),n)}}function r(t,e,a){return(e=i(e))in t?Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[e]=a,t}function h(t){return function(t){if(Array.isArray(t))return s(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return s(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?s(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,a=new Array(e);i<e;i++)a[i]=t[i];return a}var c="undefined"!=typeof window&&void 0!==window.document,l=c?window:{},d=!(!c||!l.document.documentElement)&&"ontouchstart"in l.document.documentElement,p=!!c&&"PointerEvent"in l,m="cropper",u="all",g="crop",f="move",v="zoom",w="e",b="w",y="s",x="n",M="ne",C="nw",D="se",B="sw",k="".concat(m,"-crop"),O="".concat(m,"-disabled"),T="".concat(m,"-hidden"),E="".concat(m,"-hide"),W="".concat(m,"-invisible"),H="".concat(m,"-modal"),N="".concat(m,"-move"),L="".concat(m,"Action"),z="".concat(m,"Preview"),Y="crop",X="move",R="none",S="crop",j="cropend",A="cropmove",P="cropstart",I="dblclick",U=p?"pointerdown":d?"touchstart":"mousedown",q=p?"pointermove":d?"touchmove":"mousemove",$=p?"pointerup pointercancel":d?"touchend touchcancel":"mouseup",Q="ready",K="resize",Z="wheel",G="zoom",V="image/jpeg",F=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,J=/^data:/,_=/^data:image\/jpeg;base64,/,tt=/^img|canvas$/i,et={viewMode:0,dragMode:Y,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},it=Number.isNaN||l.isNaN;function at(t){return"number"==typeof t&&!it(t)}var nt=function(t){return t>0&&t<1/0};function ot(t){return void 0===t}function rt(t){return"object"===a(t)&&null!==t}var ht=Object.prototype.hasOwnProperty;function st(t){if(!rt(t))return!1;try{var e=t.constructor,i=e.prototype;return e&&i&&ht.call(i,"isPrototypeOf")}catch(t){return!1}}function ct(t){return"function"==typeof t}var lt=Array.prototype.slice;function dt(t){return Array.from?Array.from(t):lt.call(t)}function pt(t,e){return t&&ct(e)&&(Array.isArray(t)||at(t.length)?dt(t).forEach((function(i,a){e.call(t,i,a,t)})):rt(t)&&Object.keys(t).forEach((function(i){e.call(t,t[i],i,t)}))),t}var mt=Object.assign||function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),a=1;a<e;a++)i[a-1]=arguments[a];return rt(t)&&i.length>0&&i.forEach((function(e){rt(e)&&Object.keys(e).forEach((function(i){t[i]=e[i]}))})),t},ut=/\.\d*(?:0|9){12}\d*$/;function gt(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return ut.test(t)?Math.round(t*e)/e:t}var ft=/^width|height|left|top|marginLeft|marginTop$/;function vt(t,e){var i=t.style;pt(e,(function(t,e){ft.test(e)&&at(t)&&(t="".concat(t,"px")),i[e]=t}))}function wt(t,e){if(e)if(at(t.length))pt(t,(function(t){wt(t,e)}));else if(t.classList)t.classList.add(e);else{var i=t.className.trim();i?i.indexOf(e)<0&&(t.className="".concat(i," ").concat(e)):t.className=e}}function bt(t,e){e&&(at(t.length)?pt(t,(function(t){bt(t,e)})):t.classList?t.classList.remove(e):t.className.indexOf(e)>=0&&(t.className=t.className.replace(e,"")))}function yt(t,e,i){e&&(at(t.length)?pt(t,(function(t){yt(t,e,i)})):i?wt(t,e):bt(t,e))}var xt=/([a-z\d])([A-Z])/g;function Mt(t){return t.replace(xt,"$1-$2").toLowerCase()}function Ct(t,e){return rt(t[e])?t[e]:t.dataset?t.dataset[e]:t.getAttribute("data-".concat(Mt(e)))}function Dt(t,e,i){rt(i)?t[e]=i:t.dataset?t.dataset[e]=i:t.setAttribute("data-".concat(Mt(e)),i)}var Bt=/\s\s*/,kt=function(){var t=!1;if(c){var e=!1,i=function(){},a=Object.defineProperty({},"once",{get:function(){return t=!0,e},set:function(t){e=t}});l.addEventListener("test",i,a),l.removeEventListener("test",i,a)}return t}();function Ot(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(Bt).forEach((function(e){if(!kt){var o=t.listeners;o&&o[e]&&o[e][i]&&(n=o[e][i],delete o[e][i],0===Object.keys(o[e]).length&&delete o[e],0===Object.keys(o).length&&delete t.listeners)}t.removeEventListener(e,n,a)}))}function Tt(t,e,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},n=i;e.trim().split(Bt).forEach((function(e){if(a.once&&!kt){var o=t.listeners,r=void 0===o?{}:o;n=function(){delete r[e][i],t.removeEventListener(e,n,a);for(var o=arguments.length,h=new Array(o),s=0;s<o;s++)h[s]=arguments[s];i.apply(t,h)},r[e]||(r[e]={}),r[e][i]&&t.removeEventListener(e,r[e][i],a),r[e][i]=n,t.listeners=r}t.addEventListener(e,n,a)}))}function Et(t,e,i){var a;return ct(Event)&&ct(CustomEvent)?a=new CustomEvent(e,{detail:i,bubbles:!0,cancelable:!0}):(a=document.createEvent("CustomEvent")).initCustomEvent(e,!0,!0,i),t.dispatchEvent(a)}function Wt(t){var e=t.getBoundingClientRect();return{left:e.left+(window.pageXOffset-document.documentElement.clientLeft),top:e.top+(window.pageYOffset-document.documentElement.clientTop)}}var Ht=l.location,Nt=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Lt(t){var e=t.match(Nt);return null!==e&&(e[1]!==Ht.protocol||e[2]!==Ht.hostname||e[3]!==Ht.port)}function zt(t){var e="timestamp=".concat((new Date).getTime());return t+(-1===t.indexOf("?")?"?":"&")+e}function Yt(t){var e=t.rotate,i=t.scaleX,a=t.scaleY,n=t.translateX,o=t.translateY,r=[];at(n)&&0!==n&&r.push("translateX(".concat(n,"px)")),at(o)&&0!==o&&r.push("translateY(".concat(o,"px)")),at(e)&&0!==e&&r.push("rotate(".concat(e,"deg)")),at(i)&&1!==i&&r.push("scaleX(".concat(i,")")),at(a)&&1!==a&&r.push("scaleY(".concat(a,")"));var h=r.length?r.join(" "):"none";return{WebkitTransform:h,msTransform:h,transform:h}}function Xt(t,i){var a=t.pageX,n=t.pageY,o={endX:a,endY:n};return i?o:e({startX:a,startY:n},o)}function Rt(t){var e=t.aspectRatio,i=t.height,a=t.width,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"contain",o=nt(a),r=nt(i);if(o&&r){var h=i*e;"contain"===n&&h>a||"cover"===n&&h<a?i=a/e:a=i*e}else o?i=a/e:r&&(a=i*e);return{width:a,height:i}}function St(t,e,i,a){var n=e.aspectRatio,o=e.naturalWidth,r=e.naturalHeight,s=e.rotate,c=void 0===s?0:s,l=e.scaleX,d=void 0===l?1:l,p=e.scaleY,m=void 0===p?1:p,u=i.aspectRatio,g=i.naturalWidth,f=i.naturalHeight,v=a.fillColor,w=void 0===v?"transparent":v,b=a.imageSmoothingEnabled,y=void 0===b||b,x=a.imageSmoothingQuality,M=void 0===x?"low":x,C=a.maxWidth,D=void 0===C?1/0:C,B=a.maxHeight,k=void 0===B?1/0:B,O=a.minWidth,T=void 0===O?0:O,E=a.minHeight,W=void 0===E?0:E,H=document.createElement("canvas"),N=H.getContext("2d"),L=Rt({aspectRatio:u,width:D,height:k}),z=Rt({aspectRatio:u,width:T,height:W},"cover"),Y=Math.min(L.width,Math.max(z.width,g)),X=Math.min(L.height,Math.max(z.height,f)),R=Rt({aspectRatio:n,width:D,height:k}),S=Rt({aspectRatio:n,width:T,height:W},"cover"),j=Math.min(R.width,Math.max(S.width,o)),A=Math.min(R.height,Math.max(S.height,r)),P=[-j/2,-A/2,j,A];return H.width=gt(Y),H.height=gt(X),N.fillStyle=w,N.fillRect(0,0,Y,X),N.save(),N.translate(Y/2,X/2),N.rotate(c*Math.PI/180),N.scale(d,m),N.imageSmoothingEnabled=y,N.imageSmoothingQuality=M,N.drawImage.apply(N,[t].concat(h(P.map((function(t){return Math.floor(gt(t))}))))),N.restore(),H}var jt=String.fromCharCode,At=/^data:.*,/;var Pt={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,e=this.options,i=this.container,a=this.cropper,n=Number(e.minContainerWidth),o=Number(e.minContainerHeight);wt(a,T),bt(t,T);var r={width:Math.max(i.offsetWidth,n>=0?n:200),height:Math.max(i.offsetHeight,o>=0?o:100)};this.containerData=r,vt(a,{width:r.width,height:r.height}),wt(t,T),bt(a,T)},initCanvas:function(){var t=this.containerData,e=this.imageData,i=this.options.viewMode,a=Math.abs(e.rotate)%180==90,n=a?e.naturalHeight:e.naturalWidth,o=a?e.naturalWidth:e.naturalHeight,r=n/o,h=t.width,s=t.height;t.height*r>t.width?3===i?h=t.height*r:s=t.width/r:3===i?s=t.width/r:h=t.height*r;var c={aspectRatio:r,naturalWidth:n,naturalHeight:o,width:h,height:s};this.canvasData=c,this.limited=1===i||2===i,this.limitCanvas(!0,!0),c.width=Math.min(Math.max(c.width,c.minWidth),c.maxWidth),c.height=Math.min(Math.max(c.height,c.minHeight),c.maxHeight),c.left=(t.width-c.width)/2,c.top=(t.height-c.height)/2,c.oldLeft=c.left,c.oldTop=c.top,this.initialCanvasData=mt({},c)},limitCanvas:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,o=this.cropBoxData,r=i.viewMode,h=n.aspectRatio,s=this.cropped&&o;if(t){var c=Number(i.minCanvasWidth)||0,l=Number(i.minCanvasHeight)||0;r>1?(c=Math.max(c,a.width),l=Math.max(l,a.height),3===r&&(l*h>c?c=l*h:l=c/h)):r>0&&(c?c=Math.max(c,s?o.width:0):l?l=Math.max(l,s?o.height:0):s&&(c=o.width,(l=o.height)*h>c?c=l*h:l=c/h));var d=Rt({aspectRatio:h,width:c,height:l});c=d.width,l=d.height,n.minWidth=c,n.minHeight=l,n.maxWidth=1/0,n.maxHeight=1/0}if(e)if(r>(s?0:1)){var p=a.width-n.width,m=a.height-n.height;n.minLeft=Math.min(0,p),n.minTop=Math.min(0,m),n.maxLeft=Math.max(0,p),n.maxTop=Math.max(0,m),s&&this.limited&&(n.minLeft=Math.min(o.left,o.left+(o.width-n.width)),n.minTop=Math.min(o.top,o.top+(o.height-n.height)),n.maxLeft=o.left,n.maxTop=o.top,2===r&&(n.width>=a.width&&(n.minLeft=Math.min(0,p),n.maxLeft=Math.max(0,p)),n.height>=a.height&&(n.minTop=Math.min(0,m),n.maxTop=Math.max(0,m))))}else n.minLeft=-n.width,n.minTop=-n.height,n.maxLeft=a.width,n.maxTop=a.height},renderCanvas:function(t,e){var i=this.canvasData,a=this.imageData;if(e){var n=function(t){var e=t.width,i=t.height,a=t.degree;if(90==(a=Math.abs(a)%180))return{width:i,height:e};var n=a%90*Math.PI/180,o=Math.sin(n),r=Math.cos(n),h=e*r+i*o,s=e*o+i*r;return a>90?{width:s,height:h}:{width:h,height:s}}({width:a.naturalWidth*Math.abs(a.scaleX||1),height:a.naturalHeight*Math.abs(a.scaleY||1),degree:a.rotate||0}),o=n.width,r=n.height,h=i.width*(o/i.naturalWidth),s=i.height*(r/i.naturalHeight);i.left-=(h-i.width)/2,i.top-=(s-i.height)/2,i.width=h,i.height=s,i.aspectRatio=o/r,i.naturalWidth=o,i.naturalHeight=r,this.limitCanvas(!0,!1)}(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCanvas(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,vt(this.canvas,mt({width:i.width,height:i.height},Yt({translateX:i.left,translateY:i.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var e=this.canvasData,i=this.imageData,a=i.naturalWidth*(e.width/e.naturalWidth),n=i.naturalHeight*(e.height/e.naturalHeight);mt(i,{width:a,height:n,left:(e.width-a)/2,top:(e.height-n)/2}),vt(this.image,mt({width:i.width,height:i.height},Yt(mt({translateX:i.left,translateY:i.top},i)))),t&&this.output()},initCropBox:function(){var t=this.options,e=this.canvasData,i=t.aspectRatio||t.initialAspectRatio,a=Number(t.autoCropArea)||.8,n={width:e.width,height:e.height};i&&(e.height*i>e.width?n.height=n.width/i:n.width=n.height*i),this.cropBoxData=n,this.limitCropBox(!0,!0),n.width=Math.min(Math.max(n.width,n.minWidth),n.maxWidth),n.height=Math.min(Math.max(n.height,n.minHeight),n.maxHeight),n.width=Math.max(n.minWidth,n.width*a),n.height=Math.max(n.minHeight,n.height*a),n.left=e.left+(e.width-n.width)/2,n.top=e.top+(e.height-n.height)/2,n.oldLeft=n.left,n.oldTop=n.top,this.initialCropBoxData=mt({},n)},limitCropBox:function(t,e){var i=this.options,a=this.containerData,n=this.canvasData,o=this.cropBoxData,r=this.limited,h=i.aspectRatio;if(t){var s=Number(i.minCropBoxWidth)||0,c=Number(i.minCropBoxHeight)||0,l=r?Math.min(a.width,n.width,n.width+n.left,a.width-n.left):a.width,d=r?Math.min(a.height,n.height,n.height+n.top,a.height-n.top):a.height;s=Math.min(s,a.width),c=Math.min(c,a.height),h&&(s&&c?c*h>s?c=s/h:s=c*h:s?c=s/h:c&&(s=c*h),d*h>l?d=l/h:l=d*h),o.minWidth=Math.min(s,l),o.minHeight=Math.min(c,d),o.maxWidth=l,o.maxHeight=d}e&&(r?(o.minLeft=Math.max(0,n.left),o.minTop=Math.max(0,n.top),o.maxLeft=Math.min(a.width,n.left+n.width)-o.width,o.maxTop=Math.min(a.height,n.top+n.height)-o.height):(o.minLeft=0,o.minTop=0,o.maxLeft=a.width-o.width,o.maxTop=a.height-o.height))},renderCropBox:function(){var t=this.options,e=this.containerData,i=this.cropBoxData;(i.width>i.maxWidth||i.width<i.minWidth)&&(i.left=i.oldLeft),(i.height>i.maxHeight||i.height<i.minHeight)&&(i.top=i.oldTop),i.width=Math.min(Math.max(i.width,i.minWidth),i.maxWidth),i.height=Math.min(Math.max(i.height,i.minHeight),i.maxHeight),this.limitCropBox(!1,!0),i.left=Math.min(Math.max(i.left,i.minLeft),i.maxLeft),i.top=Math.min(Math.max(i.top,i.minTop),i.maxTop),i.oldLeft=i.left,i.oldTop=i.top,t.movable&&t.cropBoxMovable&&Dt(this.face,L,i.width>=e.width&&i.height>=e.height?f:u),vt(this.cropBox,mt({width:i.width,height:i.height},Yt({translateX:i.left,translateY:i.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),Et(this.element,S,this.getData())}},It={initPreview:function(){var t=this.element,e=this.crossOrigin,i=this.options.preview,a=e?this.crossOriginUrl:this.url,n=t.alt||"The image to preview",o=document.createElement("img");if(e&&(o.crossOrigin=e),o.src=a,o.alt=n,this.viewBox.appendChild(o),this.viewBoxImage=o,i){var r=i;"string"==typeof i?r=t.ownerDocument.querySelectorAll(i):i.querySelector&&(r=[i]),this.previews=r,pt(r,(function(t){var i=document.createElement("img");Dt(t,z,{width:t.offsetWidth,height:t.offsetHeight,html:t.innerHTML}),e&&(i.crossOrigin=e),i.src=a,i.alt=n,i.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',t.innerHTML="",t.appendChild(i)}))}},resetPreview:function(){pt(this.previews,(function(t){var e=Ct(t,z);vt(t,{width:e.width,height:e.height}),t.innerHTML=e.html,function(t,e){if(rt(t[e]))try{delete t[e]}catch(i){t[e]=void 0}else if(t.dataset)try{delete t.dataset[e]}catch(i){t.dataset[e]=void 0}else t.removeAttribute("data-".concat(Mt(e)))}(t,z)}))},preview:function(){var t=this.imageData,e=this.canvasData,i=this.cropBoxData,a=i.width,n=i.height,o=t.width,r=t.height,h=i.left-e.left-t.left,s=i.top-e.top-t.top;this.cropped&&!this.disabled&&(vt(this.viewBoxImage,mt({width:o,height:r},Yt(mt({translateX:-h,translateY:-s},t)))),pt(this.previews,(function(e){var i=Ct(e,z),c=i.width,l=i.height,d=c,p=l,m=1;a&&(p=n*(m=c/a)),n&&p>l&&(d=a*(m=l/n),p=l),vt(e,{width:d,height:p}),vt(e.getElementsByTagName("img")[0],mt({width:o*m,height:r*m},Yt(mt({translateX:-h*m,translateY:-s*m},t))))})))}},Ut={bind:function(){var t=this.element,e=this.options,i=this.cropper;ct(e.cropstart)&&Tt(t,P,e.cropstart),ct(e.cropmove)&&Tt(t,A,e.cropmove),ct(e.cropend)&&Tt(t,j,e.cropend),ct(e.crop)&&Tt(t,S,e.crop),ct(e.zoom)&&Tt(t,G,e.zoom),Tt(i,U,this.onCropStart=this.cropStart.bind(this)),e.zoomable&&e.zoomOnWheel&&Tt(i,Z,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&Tt(i,I,this.onDblclick=this.dblclick.bind(this)),Tt(t.ownerDocument,q,this.onCropMove=this.cropMove.bind(this)),Tt(t.ownerDocument,$,this.onCropEnd=this.cropEnd.bind(this)),e.responsive&&Tt(window,K,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,e=this.options,i=this.cropper;ct(e.cropstart)&&Ot(t,P,e.cropstart),ct(e.cropmove)&&Ot(t,A,e.cropmove),ct(e.cropend)&&Ot(t,j,e.cropend),ct(e.crop)&&Ot(t,S,e.crop),ct(e.zoom)&&Ot(t,G,e.zoom),Ot(i,U,this.onCropStart),e.zoomable&&e.zoomOnWheel&&Ot(i,Z,this.onWheel,{passive:!1,capture:!0}),e.toggleDragModeOnDblclick&&Ot(i,I,this.onDblclick),Ot(t.ownerDocument,q,this.onCropMove),Ot(t.ownerDocument,$,this.onCropEnd),e.responsive&&Ot(window,K,this.onResize)}},qt={resize:function(){if(!this.disabled){var t,e,i=this.options,a=this.container,n=this.containerData,o=a.offsetWidth/n.width,r=a.offsetHeight/n.height,h=Math.abs(o-1)>Math.abs(r-1)?o:r;1!==h&&(i.restore&&(t=this.getCanvasData(),e=this.getCropBoxData()),this.render(),i.restore&&(this.setCanvasData(pt(t,(function(e,i){t[i]=e*h}))),this.setCropBoxData(pt(e,(function(t,i){e[i]=t*h})))))}},dblclick:function(){var t,e;this.disabled||this.options.dragMode===R||this.setDragMode((t=this.dragBox,e=k,(t.classList?t.classList.contains(e):t.className.indexOf(e)>-1)?X:Y))},wheel:function(t){var e=this,i=Number(this.options.wheelZoomRatio)||.1,a=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout((function(){e.wheeling=!1}),50),t.deltaY?a=t.deltaY>0?1:-1:t.wheelDelta?a=-t.wheelDelta/120:t.detail&&(a=t.detail>0?1:-1),this.zoom(-a*i,t)))},cropStart:function(t){var e=t.buttons,i=t.button;if(!(this.disabled||("mousedown"===t.type||"pointerdown"===t.type&&"mouse"===t.pointerType)&&(at(e)&&1!==e||at(i)&&0!==i||t.ctrlKey))){var a,n=this.options,o=this.pointers;t.changedTouches?pt(t.changedTouches,(function(t){o[t.identifier]=Xt(t)})):o[t.pointerId||0]=Xt(t),a=Object.keys(o).length>1&&n.zoomable&&n.zoomOnTouch?v:Ct(t.target,L),F.test(a)&&!1!==Et(this.element,P,{originalEvent:t,action:a})&&(t.preventDefault(),this.action=a,this.cropping=!1,a===g&&(this.cropping=!0,wt(this.dragBox,H)))}},cropMove:function(t){var e=this.action;if(!this.disabled&&e){var i=this.pointers;t.preventDefault(),!1!==Et(this.element,A,{originalEvent:t,action:e})&&(t.changedTouches?pt(t.changedTouches,(function(t){mt(i[t.identifier]||{},Xt(t,!0))})):mt(i[t.pointerId||0]||{},Xt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var e=this.action,i=this.pointers;t.changedTouches?pt(t.changedTouches,(function(t){delete i[t.identifier]})):delete i[t.pointerId||0],e&&(t.preventDefault(),Object.keys(i).length||(this.action=""),this.cropping&&(this.cropping=!1,yt(this.dragBox,H,this.cropped&&this.options.modal)),Et(this.element,j,{originalEvent:t,action:e}))}}},$t={change:function(t){var i,a=this.options,n=this.canvasData,o=this.containerData,r=this.cropBoxData,h=this.pointers,s=this.action,c=a.aspectRatio,l=r.left,d=r.top,p=r.width,m=r.height,k=l+p,O=d+m,E=0,W=0,H=o.width,N=o.height,L=!0;!c&&t.shiftKey&&(c=p&&m?p/m:1),this.limited&&(E=r.minLeft,W=r.minTop,H=E+Math.min(o.width,n.width,n.left+n.width),N=W+Math.min(o.height,n.height,n.top+n.height));var z=h[Object.keys(h)[0]],Y={x:z.endX-z.startX,y:z.endY-z.startY},X=function(t){switch(t){case w:k+Y.x>H&&(Y.x=H-k);break;case b:l+Y.x<E&&(Y.x=E-l);break;case x:d+Y.y<W&&(Y.y=W-d);break;case y:O+Y.y>N&&(Y.y=N-O)}};switch(s){case u:l+=Y.x,d+=Y.y;break;case w:if(Y.x>=0&&(k>=H||c&&(d<=W||O>=N))){L=!1;break}X(w),(p+=Y.x)<0&&(s=b,l-=p=-p),c&&(m=p/c,d+=(r.height-m)/2);break;case x:if(Y.y<=0&&(d<=W||c&&(l<=E||k>=H))){L=!1;break}X(x),m-=Y.y,d+=Y.y,m<0&&(s=y,d-=m=-m),c&&(p=m*c,l+=(r.width-p)/2);break;case b:if(Y.x<=0&&(l<=E||c&&(d<=W||O>=N))){L=!1;break}X(b),p-=Y.x,l+=Y.x,p<0&&(s=w,l-=p=-p),c&&(m=p/c,d+=(r.height-m)/2);break;case y:if(Y.y>=0&&(O>=N||c&&(l<=E||k>=H))){L=!1;break}X(y),(m+=Y.y)<0&&(s=x,d-=m=-m),c&&(p=m*c,l+=(r.width-p)/2);break;case M:if(c){if(Y.y<=0&&(d<=W||k>=H)){L=!1;break}X(x),m-=Y.y,d+=Y.y,p=m*c}else X(x),X(w),Y.x>=0?k<H?p+=Y.x:Y.y<=0&&d<=W&&(L=!1):p+=Y.x,Y.y<=0?d>W&&(m-=Y.y,d+=Y.y):(m-=Y.y,d+=Y.y);p<0&&m<0?(s=B,d-=m=-m,l-=p=-p):p<0?(s=C,l-=p=-p):m<0&&(s=D,d-=m=-m);break;case C:if(c){if(Y.y<=0&&(d<=W||l<=E)){L=!1;break}X(x),m-=Y.y,d+=Y.y,p=m*c,l+=r.width-p}else X(x),X(b),Y.x<=0?l>E?(p-=Y.x,l+=Y.x):Y.y<=0&&d<=W&&(L=!1):(p-=Y.x,l+=Y.x),Y.y<=0?d>W&&(m-=Y.y,d+=Y.y):(m-=Y.y,d+=Y.y);p<0&&m<0?(s=D,d-=m=-m,l-=p=-p):p<0?(s=M,l-=p=-p):m<0&&(s=B,d-=m=-m);break;case B:if(c){if(Y.x<=0&&(l<=E||O>=N)){L=!1;break}X(b),p-=Y.x,l+=Y.x,m=p/c}else X(y),X(b),Y.x<=0?l>E?(p-=Y.x,l+=Y.x):Y.y>=0&&O>=N&&(L=!1):(p-=Y.x,l+=Y.x),Y.y>=0?O<N&&(m+=Y.y):m+=Y.y;p<0&&m<0?(s=M,d-=m=-m,l-=p=-p):p<0?(s=D,l-=p=-p):m<0&&(s=C,d-=m=-m);break;case D:if(c){if(Y.x>=0&&(k>=H||O>=N)){L=!1;break}X(w),m=(p+=Y.x)/c}else X(y),X(w),Y.x>=0?k<H?p+=Y.x:Y.y>=0&&O>=N&&(L=!1):p+=Y.x,Y.y>=0?O<N&&(m+=Y.y):m+=Y.y;p<0&&m<0?(s=C,d-=m=-m,l-=p=-p):p<0?(s=B,l-=p=-p):m<0&&(s=M,d-=m=-m);break;case f:this.move(Y.x,Y.y),L=!1;break;case v:this.zoom(function(t){var i=e({},t),a=0;return pt(t,(function(t,e){delete i[e],pt(i,(function(e){var i=Math.abs(t.startX-e.startX),n=Math.abs(t.startY-e.startY),o=Math.abs(t.endX-e.endX),r=Math.abs(t.endY-e.endY),h=Math.sqrt(i*i+n*n),s=(Math.sqrt(o*o+r*r)-h)/h;Math.abs(s)>Math.abs(a)&&(a=s)}))})),a}(h),t),L=!1;break;case g:if(!Y.x||!Y.y){L=!1;break}i=Wt(this.cropper),l=z.startX-i.left,d=z.startY-i.top,p=r.minWidth,m=r.minHeight,Y.x>0?s=Y.y>0?D:M:Y.x<0&&(l-=p,s=Y.y>0?B:C),Y.y<0&&(d-=m),this.cropped||(bt(this.cropBox,T),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}L&&(r.width=p,r.height=m,r.left=l,r.top=d,this.action=s,this.renderCropBox()),pt(h,(function(t){t.startX=t.endX,t.startY=t.endY}))}},Qt={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&wt(this.dragBox,H),bt(this.cropBox,T),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=mt({},this.initialImageData),this.canvasData=mt({},this.initialCanvasData),this.cropBoxData=mt({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(mt(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),bt(this.dragBox,H),wt(this.cropBox,T)),this},replace:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),e?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,pt(this.previews,(function(e){e.getElementsByTagName("img")[0].src=t})))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,bt(this.cropper,O)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,wt(this.cropper,O)),this},destroy:function(){var t=this.element;return t.cropper?(t.cropper=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=i.left,n=i.top;return this.moveTo(ot(t)?t:a+Number(t),ot(e)?e:n+Number(e))},moveTo:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.canvasData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.movable&&(at(t)&&(i.left=t,a=!0),at(e)&&(i.top=e,a=!0),a&&this.renderCanvas(!0)),this},zoom:function(t,e){var i=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(i.width*t/i.naturalWidth,null,e)},zoomTo:function(t,e,i){var a=this.options,n=this.canvasData,o=n.width,r=n.height,h=n.naturalWidth,s=n.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&a.zoomable){var c=h*t,l=s*t;if(!1===Et(this.element,G,{ratio:t,oldRatio:o/h,originalEvent:i}))return this;if(i){var d=this.pointers,p=Wt(this.cropper),m=d&&Object.keys(d).length?function(t){var e=0,i=0,a=0;return pt(t,(function(t){var n=t.startX,o=t.startY;e+=n,i+=o,a+=1})),{pageX:e/=a,pageY:i/=a}}(d):{pageX:i.pageX,pageY:i.pageY};n.left-=(c-o)*((m.pageX-p.left-n.left)/o),n.top-=(l-r)*((m.pageY-p.top-n.top)/r)}else st(e)&&at(e.x)&&at(e.y)?(n.left-=(c-o)*((e.x-n.left)/o),n.top-=(l-r)*((e.y-n.top)/r)):(n.left-=(c-o)/2,n.top-=(l-r)/2);n.width=c,n.height=l,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return at(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var e=this.imageData.scaleY;return this.scale(t,at(e)?e:1)},scaleY:function(t){var e=this.imageData.scaleX;return this.scale(at(e)?e:1,t)},scale:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,i=this.imageData,a=!1;return t=Number(t),e=Number(e),this.ready&&!this.disabled&&this.options.scalable&&(at(t)&&(i.scaleX=t,a=!0),at(e)&&(i.scaleY=e,a=!0),a&&this.renderCanvas(!0,!0)),this},getData:function(){var t,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.options,a=this.imageData,n=this.canvasData,o=this.cropBoxData;if(this.ready&&this.cropped){t={x:o.left-n.left,y:o.top-n.top,width:o.width,height:o.height};var r=a.width/a.naturalWidth;if(pt(t,(function(e,i){t[i]=e/r})),e){var h=Math.round(t.y+t.height),s=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=s-t.x,t.height=h-t.y}}else t={x:0,y:0,width:0,height:0};return i.rotatable&&(t.rotate=a.rotate||0),i.scalable&&(t.scaleX=a.scaleX||1,t.scaleY=a.scaleY||1),t},setData:function(t){var e=this.options,i=this.imageData,a=this.canvasData,n={};if(this.ready&&!this.disabled&&st(t)){var o=!1;e.rotatable&&at(t.rotate)&&t.rotate!==i.rotate&&(i.rotate=t.rotate,o=!0),e.scalable&&(at(t.scaleX)&&t.scaleX!==i.scaleX&&(i.scaleX=t.scaleX,o=!0),at(t.scaleY)&&t.scaleY!==i.scaleY&&(i.scaleY=t.scaleY,o=!0)),o&&this.renderCanvas(!0,!0);var r=i.width/i.naturalWidth;at(t.x)&&(n.left=t.x*r+a.left),at(t.y)&&(n.top=t.y*r+a.top),at(t.width)&&(n.width=t.width*r),at(t.height)&&(n.height=t.height*r),this.setCropBoxData(n)}return this},getContainerData:function(){return this.ready?mt({},this.containerData):{}},getImageData:function(){return this.sized?mt({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,e={};return this.ready&&pt(["left","top","width","height","naturalWidth","naturalHeight"],(function(i){e[i]=t[i]})),e},setCanvasData:function(t){var e=this.canvasData,i=e.aspectRatio;return this.ready&&!this.disabled&&st(t)&&(at(t.left)&&(e.left=t.left),at(t.top)&&(e.top=t.top),at(t.width)?(e.width=t.width,e.height=t.width/i):at(t.height)&&(e.height=t.height,e.width=t.height*i),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,e=this.cropBoxData;return this.ready&&this.cropped&&(t={left:e.left,top:e.top,width:e.width,height:e.height}),t||{}},setCropBoxData:function(t){var e,i,a=this.cropBoxData,n=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&st(t)&&(at(t.left)&&(a.left=t.left),at(t.top)&&(a.top=t.top),at(t.width)&&t.width!==a.width&&(e=!0,a.width=t.width),at(t.height)&&t.height!==a.height&&(i=!0,a.height=t.height),n&&(e?a.height=a.width/n:i&&(a.width=a.height*n)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var e=this.canvasData,i=St(this.image,this.imageData,e,t);if(!this.cropped)return i;var a=this.getData(t.rounded),n=a.x,o=a.y,r=a.width,s=a.height,c=i.width/Math.floor(e.naturalWidth);1!==c&&(n*=c,o*=c,r*=c,s*=c);var l=r/s,d=Rt({aspectRatio:l,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),p=Rt({aspectRatio:l,width:t.minWidth||0,height:t.minHeight||0},"cover"),m=Rt({aspectRatio:l,width:t.width||(1!==c?i.width:r),height:t.height||(1!==c?i.height:s)}),u=m.width,g=m.height;u=Math.min(d.width,Math.max(p.width,u)),g=Math.min(d.height,Math.max(p.height,g));var f=document.createElement("canvas"),v=f.getContext("2d");f.width=gt(u),f.height=gt(g),v.fillStyle=t.fillColor||"transparent",v.fillRect(0,0,u,g);var w=t.imageSmoothingEnabled,b=void 0===w||w,y=t.imageSmoothingQuality;v.imageSmoothingEnabled=b,y&&(v.imageSmoothingQuality=y);var x,M,C,D,B,k,O=i.width,T=i.height,E=n,W=o;E<=-r||E>O?(E=0,x=0,C=0,B=0):E<=0?(C=-E,E=0,B=x=Math.min(O,r+E)):E<=O&&(C=0,B=x=Math.min(r,O-E)),x<=0||W<=-s||W>T?(W=0,M=0,D=0,k=0):W<=0?(D=-W,W=0,k=M=Math.min(T,s+W)):W<=T&&(D=0,k=M=Math.min(s,T-W));var H=[E,W,x,M];if(B>0&&k>0){var N=u/r;H.push(C*N,D*N,B*N,k*N)}return v.drawImage.apply(v,[i].concat(h(H.map((function(t){return Math.floor(gt(t))}))))),f},setAspectRatio:function(t){var e=this.options;return this.disabled||ot(t)||(e.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var e=this.options,i=this.dragBox,a=this.face;if(this.ready&&!this.disabled){var n=t===Y,o=e.movable&&t===X;t=n||o?t:R,e.dragMode=t,Dt(i,L,t),yt(i,k,n),yt(i,N,o),e.cropBoxMovable||(Dt(a,L,t),yt(a,k,n),yt(a,N,o))}return this}},Kt=l.Cropper,Zt=function(){function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(n(this,t),!e||!tt.test(e.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=e,this.options=mt({},et,st(i)&&i),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return e=t,a=[{key:"noConflict",value:function(){return window.Cropper=Kt,t}},{key:"setDefaults",value:function(t){mt(et,st(t)&&t)}}],(i=[{key:"init",value:function(){var t,e=this.element,i=e.tagName.toLowerCase();if(!e.cropper){if(e.cropper=this,"img"===i){if(this.isImg=!0,t=e.getAttribute("src")||"",this.originalUrl=t,!t)return;t=e.src}else"canvas"===i&&window.HTMLCanvasElement&&(t=e.toDataURL());this.load(t)}}},{key:"load",value:function(t){var e=this;if(t){this.url=t,this.imageData={};var i=this.element,a=this.options;if(a.rotatable||a.scalable||(a.checkOrientation=!1),a.checkOrientation&&window.ArrayBuffer)if(J.test(t))_.test(t)?this.read((n=t.replace(At,""),o=atob(n),r=new ArrayBuffer(o.length),pt(h=new Uint8Array(r),(function(t,e){h[e]=o.charCodeAt(e)})),r)):this.clone();else{var n,o,r,h,s=new XMLHttpRequest,c=this.clone.bind(this);this.reloading=!0,this.xhr=s,s.onabort=c,s.onerror=c,s.ontimeout=c,s.onprogress=function(){s.getResponseHeader("content-type")!==V&&s.abort()},s.onload=function(){e.read(s.response)},s.onloadend=function(){e.reloading=!1,e.xhr=null},a.checkCrossOrigin&&Lt(t)&&i.crossOrigin&&(t=zt(t)),s.open("GET",t,!0),s.responseType="arraybuffer",s.withCredentials="use-credentials"===i.crossOrigin,s.send()}else this.clone()}}},{key:"read",value:function(t){var e=this.options,i=this.imageData,a=function(t){var e,i=new DataView(t);try{var a,n,o;if(255===i.getUint8(0)&&216===i.getUint8(1))for(var r=i.byteLength,h=2;h+1<r;){if(255===i.getUint8(h)&&225===i.getUint8(h+1)){n=h;break}h+=1}if(n){var s=n+10;if("Exif"===function(t,e,i){var a="";i+=e;for(var n=e;n<i;n+=1)a+=jt(t.getUint8(n));return a}(i,n+4,4)){var c=i.getUint16(s);if(((a=18761===c)||19789===c)&&42===i.getUint16(s+2,a)){var l=i.getUint32(s+4,a);l>=8&&(o=s+l)}}}if(o){var d,p,m=i.getUint16(o,a);for(p=0;p<m;p+=1)if(d=o+12*p+2,274===i.getUint16(d,a)){d+=8,e=i.getUint16(d,a),i.setUint16(d,1,a);break}}}catch(t){e=1}return e}(t),n=0,o=1,r=1;if(a>1){this.url=function(t,e){for(var i=[],a=new Uint8Array(t);a.length>0;)i.push(jt.apply(null,dt(a.subarray(0,8192)))),a=a.subarray(8192);return"data:".concat("image/jpeg",";base64,").concat(btoa(i.join("")))}(t);var h=function(t){var e=0,i=1,a=1;switch(t){case 2:i=-1;break;case 3:e=-180;break;case 4:a=-1;break;case 5:e=90,a=-1;break;case 6:e=90;break;case 7:e=90,i=-1;break;case 8:e=-90}return{rotate:e,scaleX:i,scaleY:a}}(a);n=h.rotate,o=h.scaleX,r=h.scaleY}e.rotatable&&(i.rotate=n),e.scalable&&(i.scaleX=o,i.scaleY=r),this.clone()}},{key:"clone",value:function(){var t=this.element,e=this.url,i=t.crossOrigin,a=e;this.options.checkCrossOrigin&&Lt(e)&&(i||(i="anonymous"),a=zt(e)),this.crossOrigin=i,this.crossOriginUrl=a;var n=document.createElement("img");i&&(n.crossOrigin=i),n.src=a||e,n.alt=t.alt||"The image to crop",this.image=n,n.onload=this.start.bind(this),n.onerror=this.stop.bind(this),wt(n,E),t.parentNode.insertBefore(n,t.nextSibling)}},{key:"start",value:function(){var t=this,e=this.image;e.onload=null,e.onerror=null,this.sizing=!0;var i=l.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(l.navigator.userAgent),a=function(e,i){mt(t.imageData,{naturalWidth:e,naturalHeight:i,aspectRatio:e/i}),t.initialImageData=mt({},t.imageData),t.sizing=!1,t.sized=!0,t.build()};if(!e.naturalWidth||i){var n=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=n,n.onload=function(){a(n.width,n.height),i||o.removeChild(n)},n.src=e.src,i||(n.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(n))}else a(e.naturalWidth,e.naturalHeight)}},{key:"stop",value:function(){var t=this.image;t.onload=null,t.onerror=null,t.parentNode.removeChild(t),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var t=this.element,e=this.options,i=this.image,a=t.parentNode,n=document.createElement("div");n.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=n.querySelector(".".concat(m,"-container")),r=o.querySelector(".".concat(m,"-canvas")),h=o.querySelector(".".concat(m,"-drag-box")),s=o.querySelector(".".concat(m,"-crop-box")),c=s.querySelector(".".concat(m,"-face"));this.container=a,this.cropper=o,this.canvas=r,this.dragBox=h,this.cropBox=s,this.viewBox=o.querySelector(".".concat(m,"-view-box")),this.face=c,r.appendChild(i),wt(t,T),a.insertBefore(o,t.nextSibling),bt(i,E),this.initPreview(),this.bind(),e.initialAspectRatio=Math.max(0,e.initialAspectRatio)||NaN,e.aspectRatio=Math.max(0,e.aspectRatio)||NaN,e.viewMode=Math.max(0,Math.min(3,Math.round(e.viewMode)))||0,wt(s,T),e.guides||wt(s.getElementsByClassName("".concat(m,"-dashed")),T),e.center||wt(s.getElementsByClassName("".concat(m,"-center")),T),e.background&&wt(o,"".concat(m,"-bg")),e.highlight||wt(c,W),e.cropBoxMovable&&(wt(c,N),Dt(c,L,u)),e.cropBoxResizable||(wt(s.getElementsByClassName("".concat(m,"-line")),T),wt(s.getElementsByClassName("".concat(m,"-point")),T)),this.render(),this.ready=!0,this.setDragMode(e.dragMode),e.autoCrop&&this.crop(),this.setData(e.data),ct(e.ready)&&Tt(t,Q,e.ready,{once:!0}),Et(t,Q)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var t=this.cropper.parentNode;t&&t.removeChild(this.cropper),bt(this.element,T)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}])&&o(e.prototype,i),a&&o(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,i,a}();return mt(Zt.prototype,Pt,It,Ut,qt,$t,Qt),Zt}));