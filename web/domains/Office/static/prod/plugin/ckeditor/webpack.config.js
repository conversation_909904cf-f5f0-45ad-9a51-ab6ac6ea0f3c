/**
 * @license Copyright (c) 2014-2022, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */
"use strict";const path=require("path"),webpack=require("webpack"),{bundler:bundler,styles:styles}=require("@ckeditor/ckeditor5-dev-utils"),CKEditorWebpackPlugin=require("@ckeditor/ckeditor5-dev-webpack-plugin"),TerserWebpackPlugin=require("terser-webpack-plugin");module.exports={devtool:"source-map",performance:{hints:!1},entry:path.resolve(__dirname,"src","ckeditor.js"),output:{library:"ClassicEditor",path:path.resolve(__dirname,"../"),filename:"ckeditor.js",libraryTarget:"umd",libraryExport:"default"},optimization:{minimizer:[new TerserWebpackPlugin({terserOptions:{output:{comments:/^!/}},extractComments:!1})]},plugins:[new CKEditorWebpackPlugin({language:"en"}),new webpack.BannerPlugin({banner:bundler.getLicenseBanner(),raw:!0})],module:{rules:[{test:/\.svg$/,use:["raw-loader"]},{test:/\.css$/,use:[{loader:"style-loader",options:{injectType:"singletonStyleTag",attributes:{"data-cke":!0}}},{loader:"css-loader"},{loader:"postcss-loader",options:{postcssOptions:styles.getPostCssConfig({themeImporter:{themePath:require.resolve("@ckeditor/ckeditor5-theme-lark")},minify:!0})}}]}]}};