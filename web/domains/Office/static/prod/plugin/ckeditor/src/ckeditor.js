/**
 * @license Copyright (c) 2014-2022, CKSource Holding sp. z o.o. All rights reserved.
 * For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
 */
import{ClassicEditor}from"@ckeditor/ckeditor5-editor-classic";import{Bold,Italic,Strikethrough,Subscript,Superscript,Underline}from"@ckeditor/ckeditor5-basic-styles";import{Alignment}from"@ckeditor/ckeditor5-alignment";import{Autoformat}from"@ckeditor/ckeditor5-autoformat";import{AutoLink,Link,LinkImage}from"@ckeditor/ckeditor5-link";import{MediaEmbed}from"@ckeditor/ckeditor5-media-embed";import{BlockQuote}from"@ckeditor/ckeditor5-block-quote";import{Essentials}from"@ckeditor/ckeditor5-essentials";import{FindAndReplace}from"@ckeditor/ckeditor5-find-and-replace";import{Font}from"@ckeditor/ckeditor5-font";import{Heading}from"@ckeditor/ckeditor5-heading";import{Highlight}from"@ckeditor/ckeditor5-highlight";import{AutoImage,Image,ImageCaption,ImageInsert,ImageResize,ImageStyle,ImageToolbar,ImageUpload}from"@ckeditor/ckeditor5-image";import{Indent,IndentBlock}from"@ckeditor/ckeditor5-indent";import{List,ListProperties}from"@ckeditor/ckeditor5-list";import{Paragraph}from"@ckeditor/ckeditor5-paragraph";import{PasteFromOffice}from"@ckeditor/ckeditor5-paste-from-office";import{RemoveFormat}from"@ckeditor/ckeditor5-remove-format";import{ShowBlocks}from"@ckeditor/ckeditor5-show-blocks";import{Table,TableCaption,TableCellProperties,TableProperties,TableToolbar}from"@ckeditor/ckeditor5-table";import{TextTransformation}from"@ckeditor/ckeditor5-typing";import{SourceEditing}from"@ckeditor/ckeditor5-source-editing";import{WordCount}from"@ckeditor/ckeditor5-word-count";import{Markdown}from"@ckeditor/ckeditor5-markdown-gfm";import AdsButton from"./plugins/adsbutton";import PasteParser from"./plugins/paste-parser";import HeadingShortcut from"./plugins/heading-shortcut";import FullScreen from"@pikulinpw/ckeditor5-fullscreen";import sanitizeHtml from"sanitize-html";class Editor extends ClassicEditor{}const IFRAME_SRC="//cdn.iframe.ly/api/iframe",API_KEY="392498a69559d70f999cf3e53bedc87f";Editor.builtinPlugins=[PasteParser,AdsButton,HeadingShortcut,Alignment,Autoformat,AutoImage,AutoLink,BlockQuote,Bold,Essentials,FindAndReplace,FullScreen,Font,Heading,Highlight,Image,ImageCaption,ImageInsert,ImageResize,ImageStyle,ImageToolbar,ImageUpload,Indent,IndentBlock,Italic,Link,LinkImage,List,ListProperties,MediaEmbed,Paragraph,PasteFromOffice,RemoveFormat,ShowBlocks,Strikethrough,Subscript,Superscript,SourceEditing,Table,TableCaption,TableCellProperties,TableProperties,TableToolbar,TextTransformation,Underline,WordCount,Markdown],Editor.defaultConfig={toolbar:["heading","undo","redo","removeFormat","showBlocks","|","bold","underline","italic","|","link","imageInsert","|",{label:"Position",withText:!0,icon:'<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M3,12H15M3,6H21M3,18H15" style="fill: none; stroke: currentColor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 2;"></path></svg>',items:["alignment:left","alignment:center","alignment:right","alignment:justify","outdent","indent"]},"|",{label:"Lists",withText:!0,icon:'<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7 8H21M7 12H21M7 16H21M3 8H3.01M3 12H3.01M3 16H3.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>',items:["bulletedList","numberedList"]},"|",{label:"Font Style",withText:!0,icon:"text",items:["blockQuote","fontBackgroundColor","fontColor","fontSize","strikethrough","highlight","subscript","superscript"]},"|",{label:"Tools",withText:!0,icon:'<svg fill="currentColor" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><path d="M27.783 7.936c0.959 2.313 0.502 5.074-1.379 6.955-2.071 2.071-5.201 2.395-7.634 1.022l-1.759 1.921 1.255 1.26 0.75-0.75c0.383-0.384 1.005-0.384 1.388 0l6.082 6.144c0.384 0.383 0.384 1.005 0 1.388l-2.776 2.776c-0.383 0.384-1.005 0.384-1.388 0l-6.082-6.144c-0.384-0.383-0.384-1.005 0-1.388l0.685-0.685-1.196-1.199-8.411 9.189c-0.767 0.767-2.010 0.767-2.776 0l-0.694-0.694c-0.767-0.767-0.767-2.010 0-2.776l9.582-8.025-6.364-6.381-2.010-0.001-2.326-3.74 1.872-1.875 3.825 2.341 0.025 1.968 6.438 6.463 1.873-1.568c-1.831-2.496-1.64-6.012 0.616-8.268 1.872-1.872 4.618-2.337 6.925-1.396l-4.124 4.067 3.471 3.471 4.132-4.075zM6.15 25.934c-0.383-0.383-1.004-0.383-1.388 0-0.384 0.384-0.384 1.005 0 1.389 0.384 0.383 1.005 0.383 1.388 0 0.384-0.385 0.384-1.006 0-1.389z"></path></svg>',items:["adsButton","findAndReplace","insertTable","sourceEditing","fullscreen"]}],heading:{options:[{model:"paragraph",title:"Paragraph",class:"ck-heading_paragraph"},{model:"headingTwo",view:{name:"h2",classes:"article-heading-2"},title:"Heading 2",class:"ck-heading_heading2"},{model:"headingThree",view:{name:"h3",classes:"article-heading-3"},title:"Heading 3",class:"ck-heading_heading3"}]},mediaEmbed:{previewsInData:!1,providers:[{name:"iframely",url:/.+/,html:e=>{const t=e[0];return`<div class="iframely-embed">\n                        <div class="iframely-responsive">\n                            <iframe src="${IFRAME_SRC+"?app=1&key="+API_KEY+"&omit_css=true&url="+encodeURIComponent(t)}" allow="autoplay; encrypted-media" allowfullscreen></iframe>\n                        </div>\n                    </div>`}}]},htmlEmbed:{showPreviews:!0,sanitizeHtml:e=>({html:sanitizeHtml(e,{allowedTags:["ins"],allowedAttributes:!1}),hasChanged:!0})},language:"en",image:{toolbar:["imageTextAlternative","imageStyle:inline","imageStyle:block","imageStyle:side","linkImage","toggleImageCaption"]},table:{contentToolbar:["tableColumn","tableRow","mergeTableCells","tableCellProperties","tableProperties"]}};export default Editor;