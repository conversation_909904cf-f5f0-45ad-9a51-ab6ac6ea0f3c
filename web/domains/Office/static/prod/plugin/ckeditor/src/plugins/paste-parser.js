import Plugin from"@ckeditor/ckeditor5-core/src/plugin";import{styleObjectToString,styleStringToObject}from"./plugin-utils";export default class PasteParser extends Plugin{static get pluginName(){return"PasteParser"}init(){const e=this.editor;e.plugins.get("ClipboardPipeline").on("inputTransformation",((t,o)=>{const r=o.content;e.editing.view.change((e=>{for(const t of r.getChildren())this._processViewElement(t,e);return e}))}))}_processViewElement(e,t){if(!e.is("element"))return;const o=new Map(e.getAttributes()),r=o.get("style");if(r){const i=styleStringToObject(r);delete i.color,delete i["background-color"];const s=styleObjectToString(i);s?(t.setAttribute("style",s,e),o.set("style",s)):t.removeAttribute("style",e)}e.hasAttribute("color")&&t.removeAttribute("color",e),e.hasAttribute("bgcolor")&&t.removeAttribute("bgcolor",e);for(const o of Array.from(e.getChildren()))this._processViewElement(o,t)}_processHeading(){const e=this.editor.conversion;e.for("upcast").elementToElement({model:"heading2",view:{name:"p",styles:{"font-size":/2[6-9]px|[3-9]\dpx|\d{3,}px/}}}),e.for("upcast").elementToElement({model:"heading3",view:{name:"p",styles:{"font-size":/2[0-5]px/}}})}}