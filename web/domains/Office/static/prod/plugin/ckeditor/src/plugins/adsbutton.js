import Plugin from"@ckeditor/ckeditor5-core/src/plugin";import ButtonView from"@ckeditor/ckeditor5-ui/src/button/buttonview";export default class AdsButton extends Plugin{init(){const t=this.editor;t.ui.componentFactory.add("adsButton",(e=>{const o=new ButtonView(e);return o.set({label:"Ads Placement",withText:!0}),o.on("execute",(()=>{t.model.change((e=>{t.model.insertContent(e.createText("%[ADS_SPACE]%"))}))})),o}))}}