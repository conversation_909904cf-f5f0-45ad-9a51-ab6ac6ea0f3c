import Plugin from"@ckeditor/ckeditor5-core/src/plugin";export default class HeadingShortcut extends Plugin{static get pluginName(){return"HeadingShortcut"}init(){const e=this.editor;e.keystrokes.set("Ctrl+Shift+1",((t,i)=>{e.execute("heading",{value:"headingTwo"}),i()})),e.keystrokes.set("Ctrl+Shift+2",((t,i)=>{e.execute("heading",{value:"headingTwo"}),i()})),e.keystrokes.set("Ctrl+Shift+3",((t,i)=>{e.execute("heading",{value:"headingThree"}),i()})),e.keystrokes.set("Ctrl+Shift+P",((t,i)=>{e.execute("paragraph"),i()}))}}