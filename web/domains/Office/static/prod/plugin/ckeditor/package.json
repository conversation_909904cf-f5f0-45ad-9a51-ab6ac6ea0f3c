{"name": "ckeditor5-custom-build", "author": "CKSource", "description": "A custom CKEditor 5 build made by the CKEditor 5 online builder.", "version": "0.0.1", "license": "SEE LICENSE IN LICENSE.md", "private": true, "main": "./build/ckeditor.js", "dependencies": {"@ckeditor/ckeditor5-alignment": "*", "@ckeditor/ckeditor5-autoformat": "*", "@ckeditor/ckeditor5-basic-styles": "*", "@ckeditor/ckeditor5-block-quote": "*", "@ckeditor/ckeditor5-code-block": "*", "@ckeditor/ckeditor5-dev-webpack-plugin": "*", "@ckeditor/ckeditor5-editor-classic": "*", "@ckeditor/ckeditor5-essentials": "*", "@ckeditor/ckeditor5-find-and-replace": "*", "@ckeditor/ckeditor5-font": "*", "@ckeditor/ckeditor5-heading": "*", "@ckeditor/ckeditor5-highlight": "*", "@ckeditor/ckeditor5-horizontal-line": "*", "@ckeditor/ckeditor5-html-embed": "*", "@ckeditor/ckeditor5-html-support": "*", "@ckeditor/ckeditor5-image": "*", "@ckeditor/ckeditor5-indent": "*", "@ckeditor/ckeditor5-link": "*", "@ckeditor/ckeditor5-list": "*", "@ckeditor/ckeditor5-markdown-gfm": "*", "@ckeditor/ckeditor5-media-embed": "*", "@ckeditor/ckeditor5-paragraph": "*", "@ckeditor/ckeditor5-paste-from-office": "*", "@ckeditor/ckeditor5-remove-format": "*", "@ckeditor/ckeditor5-restricted-editing": "*", "@ckeditor/ckeditor5-source-editing": "*", "@ckeditor/ckeditor5-special-characters": "*", "@ckeditor/ckeditor5-show-blocks": "*", "@ckeditor/ckeditor5-table": "*", "@ckeditor/ckeditor5-typing": "*", "@ckeditor/ckeditor5-word-count": "*", "@pikulinpw/ckeditor5-fullscreen": "^1.0.1"}, "scripts": {"build": "webpack --mode production"}, "devDependencies": {"@ckeditor/ckeditor5-core": "*", "@ckeditor/ckeditor5-dev-utils": "*", "@ckeditor/ckeditor5-theme-lark": "*", "@ckeditor/ckeditor5-ui": "*", "css-loader": "*", "postcss": "*", "postcss-loader": "*", "raw-loader": "*", "sanitize-html": "*", "style-loader": "*", "terser-webpack-plugin": "*", "webpack": "*", "webpack-cli": "*"}}