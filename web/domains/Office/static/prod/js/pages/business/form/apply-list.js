function updateStatus(e){e.$class("del","bg-warning","bg-success","bg-danger");let a="bg-dark";return"NOT_STARTED"===e.value&&(a="bg-danger"),"IN_PROGRESS"===e.value&&(a="bg-warning"),"COMPLETED"===e.value&&(a="bg-success"),""!==e.value&&$curl(genApi("bus/form-app/status/"+e.value),{debounce:700,method:"PUT",data:{id:e.dataset.id},headers:apiHeaders()}).then((()=>{e.closest("tr").remove()})),e.$class("add",a)}!function(){let e={},a={};$curl(genApi("bus/form-app/status"),{headers:apiHeaders()}).then((a=>e=a)),$curl(genApi("/system/all-users/1"),{headers:apiHeaders()}).then((e=>a=e));const s=(e,a,s,n,r)=>osModal({head:e,foot:"",size:"sm",body:`<form>\n                    <div class="mb-10">\n                        <label class="form-label required mb-0">Staff Members</label>\n                        ${t(s)}                            \n                    </div>\n                    <div class="mb-10 text-center">\n                        <label class="form-switch form-switch-lg">\n                            <input class="form-check-input" type="checkbox" name="notify_staff" value="true" >\n                            <span class="form-check-label">Notify Staff</span>\n                        </label>\n                    </div>\n                    <div class="text-center">\n                        <input type="hidden" name="id" value="${n}">\n                        <div class="text-center">${$facades.submitBtn()}</div>\n                    </div>\n                </form>`,then:()=>{$("[name=staff]").select2({dropdownParent:CusWind.get.box}),$on($sel(".submit-form"),"click",((e,s)=>{e.preventDefault(),ajax(s,(()=>$curl(genApi("bus/form-app/"+a),{preload:()=>preloadBtn(s),data:s,method:"PUT",headers:apiHeaders()}).finally((()=>preloadBtn(s,!1))).then((e=>serverResponse(e.code,e.message,r,!1)))))}))}}),t=e=>{let s="<option value=''>Select Staff</option>";return $loop(a,(a=>{s+=`<option value="${a.userId}" ${a.userId===e?"selected":""}>${a.name}</option>`})),`<select class="form-select" name="staff" required>${s}</select>`};$on(".copy-form-link","click",((e,a)=>{$copyToClipboard(a.dataset.link,"Form link copied to clipboard")})),simpleTablePage({api:{list:"bus/form-app/"+$lay.page.urlFull,dateRange:"bus/form-app/"+$lay.page.urlFull},entry:{row:(a,s)=>{let t=a.status;return e&&(t=((a,s)=>{let t="<option value=''>Change Status</option>";$loop(e,(e=>{t+=`<option ${e.id===s?"selected":""} value="${e.id}" ">${e.name}</option>`}));let n="bg-danger";return"IN_PROGRESS"===s&&(n="bg-warning"),"COMPLETED"===s&&(n="bg-success"),`<select class="form-select text-white ${n}" data-id="${a}" onchange="updateStatus(this)" name="form_status">${t}</select>`})(a.id,t)),`<td>${s}</td>\n                    <td>${a.refNum}</td>\n                    <td>${a.name}</td>\n                    <td>${t}</td>\n                    <td>${a.assigned?.name??"-"}</td>\n                    <td>${a.assisted?.name??"-"}</td>\n                    <td>${a.submitted}</td>`},search:(e,{populateTable:a,loadEntries:s})=>{$curl(genApi("/bus/form-app/search?query="+encodeURI(e)),{headers:apiHeaders(),preload:$preloader}).finally((()=>$preloader(!1))).then((e=>a(e)))},anchor:{id:"id",name:"name",edit:!1,actionsFn:({info:e})=>{let a=[{name:"View Details",act:"view"}];return"COMPLETED"!==e.status&&(e.assigned?.id?a.push({name:"Remove Staff-in-Charge",act:"removeStaff",className:"text-danger"}):a.push({name:"Assign Staff-in-Charge",act:"assignStaff"}),e.assisted?.id?a.push({name:"Remove Staff Assistant",act:"removeAssist",className:"text-danger"}):a.push({name:"Assign a Staff Assistant",act:"assignAssist"})),a.push({separator:!0},{className:"btn btn-primary btn-sm",name:"Download PDF",href:"business/pdf/"+e.id,target:"_blank",wrap:!0}),a}}},entryActionFn:({loadEntries:e})=>({view:({id:e,name:a})=>{$curl(genApi("bus/form-app/details/"+e),{preload:$preloader,type:"html"}).finally((()=>$preloader(!1))).then((e=>{if(""===e)return osNote("No details found for applicant");osModal({head:`${a} Details`,foot:"",body:e,size:"lg"})}))},removeStaff:({id:a,info:s})=>{cMsg(`Are you sure you want to remove this staff <b>${s.assigned.name}</b> as the staff in charge?`,(()=>{$curl(genApi("bus/form-app/remove-staff/"+a),{method:"DELETE",preload:$preloader,headers:apiHeaders()}).finally((()=>$preloader(!1))).then((a=>{osNote(a.message,"success"),e()}))}))},removeAssist:({id:a,info:s})=>{cMsg(`Are you sure you want to remove this staff <b>${s.assisted.name}</b> as the assistant staff?`,(()=>{$curl(genApi("bus/form-app/remove-assist/"+a),{method:"DELETE",preload:$preloader,headers:apiHeaders()}).finally((()=>$preloader(!1))).then((a=>{osNote(a.message,"success"),e()}))}))},assignStaff:({id:a,name:t,info:n})=>{s(`Assign a Staff-in-Charge to Applicant ${n.refNum} ${t}`,"staff-assign",n.assigned?.id,a,e)},assignAssist:({id:a,name:t,info:n})=>{s(`Assign An Assistant Staff to ${n.refNum} ${t}`,"staff-assist",n.assist?.id,a,e)}})})}();