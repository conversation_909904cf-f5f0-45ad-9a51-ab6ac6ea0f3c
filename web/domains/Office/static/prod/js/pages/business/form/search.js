!function(){const e=$sel(".nothing-found"),t=$sel(".response-table");$on($sel(".search-far-wide"),"keyup",(s=>{const n=s.target.value;n.length<3?0===n.length&&($class(e,"del","d-none"),$class(t,"add","d-none")):$debounce((()=>function(s){$curl(genApi("/bus/form-app/search?query="+encodeURI(s)),{headers:apiHeaders()}).then((s=>{if(0===s.length)return;let n="";$loop(s,(e=>{let t="danger";switch(e.status){default:break;case"IN_PROGRESS":t="warning";break;case"COMPLETED":t="success"}n+=`<tr>\n                            <td class="text-start">\n                                <span class="badge py-3 px-4 fs-7 badge-light-${t}">${e.status}</span>\n                            </td>\n                            <td class="ps-0 text-start">\n                                <span class="text-gray-800 fw-bold fs-6 d-block blog-title">${e.refNum}</span>\n                            </td>\n                            <td class="text-center">\n                                <span class="text-gray-400 fw-bold fs-6 d-block">${e.name}</span>\n                            </td>\n                            <td class="text-center">\n                                <span class="text-gray-400 fw-bold fs-6 d-block">${e.submitted}</span>\n                            </td>\n                            <td class="text-end">\n                                <div class="d-flex">\n                                    <a data-id="${e.id}" class="btn btn-sm btn-icon btn-primary w-50px h-50px me-3 view-details" title="View Details">\n                                        <i class="ki-outline ki-eye fs-2 text-light"></i>\n                                    </a>\n                                    <a href="business/form/${e.type}/${e.status}?rn=${e.refNum}" target="_blank" class="btn btn-sm btn-icon btn-info w-50px h-50px me-3" title="Open real page">\n                                        <i class="ki-outline ki-share fs-2 text-light"></i>\n                                    </a>\n                                </div>\n                            </td>\n                        </tr>`})),$html($sel(".response-body"),"in",n),$sela(".view-details").$loop((e=>{e.$on("click",((e,t)=>{e.preventDefault(),$curl(genApi("bus/form-app/details/"+t.dataset.id),{preload:$preloader,type:"html"}).finally((()=>$preloader(!1))).then((e=>{if(""===e)return osNote("No details found for applicant");osModal({head:"Applicant Details",foot:"",body:e,size:"lg"})}))}))})),$class(e,"add","d-none"),$class(t,"del","d-none")}))}(n)),500,"search-far-wide-query")}))}();