function ckEditor({selector:e,uploadApi:t}){$id("iframe-embed-style")||$sel("head").$html("beforeend",'<style id="iframe-embed-style">\n            .iframely-responsive {\n              position: relative;\n              top: 0;\n              left: 0;\n              width: 100%;\n              height: 0;\n              padding-bottom: 30%;\n            }\n            .iframely-responsive > * {\n              position: absolute;\n              top: 0;\n              left: 0;\n              width: 100%;\n              height: 100%;\n              border: 0;\n            }\n            </style>');const n=$sel(e).innerText;return $sel(e).$html("del"),ClassicEditor.create($sel(e),{extraPlugins:[function(e){e.plugins.get("FileRepository").createUploadAdapter=e=>new UploadToServer(e,t,getCsrf())}],licenseKey:"GPL"}).then((e=>(e.sourceElement.nextElementSibling.querySelector(".ck-editor__main").appendChild(e.plugins.get("WordCount").wordCountContainer),e.conversion.elementToElement({model:"headingTwo",view:"h2",upcastAlso:e=>{const t=e.getStyle("font-size");if(!t)return null;const n=t.match(/(\d+)\s*px/);return n&&Number(n[1])>26?{name:!0,styles:["font-size"]}:null}}),e.conversion.elementToElement({model:"headingThree",view:"h3",upcastAlso:e=>{const t=e.getStyle("font-size");if(!t)return null;const n=t.match(/(\d+)\s*px/);return n&&Number(n[1])<27?{name:!0,styles:["font-size"]}:null}}),$sel("head").$html("beforeend","<style>\n                .ck.ck-sticky-panel__content {\n                    border-radius: .85rem .85rem 0 0 !important;\n                }\n                .ck.ck-word-count {\n                    padding: 10px;\n                    padding-left: 0;\n                    display: flex;\n                    gap: 1rem\n                }\n                .ck.ck-word-count .ck-word-count__words, .ck.ck-word-count .ck-word-count__characters {\n                    border: solid 1px var(--ck-color-base-border);\n                    padding: 5px;\n                    font-weight: 500;\n                    border-radius: .85rem;\n                }\n                </style>"),e))).then((e=>(e.setData(n),e))).catch((e=>{console.error("Oops, something went wrong!"),console.error("Please, report the following error on https://github.com/ckeditor/ckeditor5/issues with the build id and the error stack trace:"),console.warn("Build id: njfw0ualbho4-60wn6fjw0g7s"),console.error(e)}))}function ckEditorContent(e,t=!0){if(e.updateSourceElement(),""===e.sourceElement.value&&t)return osNote("You need to write something on the editor first")}class UploadToServer{constructor(e,t,n){this.loader=e,this._serverApi=t,this._csrf=n}upload(){return this.loader.file.then((e=>new Promise(((t,n)=>{this._initRequest(),this._initListeners(t,n,e),this._sendRequest(e)})))).then((e=>{if(!e.default)throw e.message;return e}))}abort(){this.xhr&&this.xhr.abort()}_initRequest(){const e=this.xhr=new XMLHttpRequest;e.open("POST",this._serverApi,!0),e.responseType="json"}_initListeners(e,t,n){const r=this.xhr,o=this.loader,s=`Couldn't upload file: ${n.name}.`;r.addEventListener("error",(()=>t(s))),r.addEventListener("abort",(()=>t())),r.addEventListener("load",(()=>{const n=r.response;if(!n||n.error||"success"!==n.status)return t(n&&n.error?n.error.message:s);e({default:n.url??n.data.url,message:n.message??n.message})})),r.upload&&r.upload.addEventListener("progress",(e=>{e.lengthComputable&&(o.uploadTotal=e.total,o.uploaded=e.loaded)}))}_sendRequest(e){const t=new FormData;t.append("text_editor_img",e),t.append("ack","true"),this.xhr.setRequestHeader("X-CSRF-TOKEN",this._csrf),this.xhr.send(t)}}