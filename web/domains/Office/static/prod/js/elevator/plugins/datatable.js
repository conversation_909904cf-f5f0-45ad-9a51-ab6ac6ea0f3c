function dataTable({selector:e="table.data-table",searchTableFn:t,dateRangeObj:n={api:"",headers:{},then:()=>null},singleFilter:a,multiFilter:o,destroy:l,exportColumns:s,checkboxAction:r,checkboxActionUtils:i}){if(l)return void($.fn.DataTable.isDataTable(e)&&$(e).DataTable().destroy());const c=$sel(e).closest(".table-wrap-container"),d="#"+c.$sel("table").id;let u=[];const p=$(e).DataTable({retrieve:!0,order:[],pageLength:25,columnDefs:[{orderable:!1,targets:[0,"no-sort"]}],oLanguage:{sInfo:"_START_ - _END_ of _TOTAL_",sInfoEmpty:"0 entries"}});return p.columns().header().each((e=>u.push(e.innerText.toLowerCase()))),$sel(d).dataset.dtableInit="true",function(){const e=c.$sel("input.search-table[type=search]");e&&$on(e,"keyup",(n=>{if(t&&("Enter"===n.key||13===n.keyCode)){let n=e.value.trim();if(""===n)return osNote("Cannot submit an empty search to server");c.$sel(".reset-table-entries").$class("del","d-none"),t(encodeURIComponent(n),{tableInstance:p,tableContainer:c})}}),"on")}(),function(){const e=c.$sel(".datatable-date-picker");let t=!1;if(!e)return;const a=$(e).flatpickr({altInput:!0,altFormat:"d/m/Y",dateFormat:"Y-m-d",mode:"range",onChange:function(n){t||($.fn.dataTable.ext.search.push((function(t,n,o){const l=new Date(moment(n[e.dataset.column],e.dataset.format));if(isNaN(l)&&!e.dataset.reported)return e.dataset.reported="1",a.close(),a.clear(),osNote(`An invalid date was received! Please check your date format and try again.\n                                    <p class="m-0 p-0">\n                                        Current Format: <b>${e.dataset.format}</b> <br>\n                                        Table Date: <b>${n[e.dataset.column]}</b>\n                                    </p>`,"warn",{duration:-1});const s="__"===e.dataset.min?null:new Date(e.dataset.min),r="__"===e.dataset.max?null:new Date(e.dataset.max);r&&(r.setHours(23),r.setMinutes(59),r.setSeconds(59));const i=!s||s<=l,c=!r||r>=l;return!!(!s&&!r||i&&c)||!(!i||r)||!(s||!c)})),t=!0),e.dataset.min=n[0]??"__",e.dataset.max=n[1]??"__",p.draw()}});$on(c.$sel(".datatable-date-clear"),"click",(t=>{t.preventDefault(),a.clear(),e.dataset.min="__",e.dataset.max="__"}),"on"),$on(c.$sel(".datatable-date-goto-server"),"click",(e=>{if(e.preventDefault(),""===a.input.value.trim())return osNote("Cannot submit an empty date range","warn");let t={preload:$preloader,data:{range:a.input.value}};if(n.headers&&(t.headers=n.headers),!n.api)return osNote("No dateRange api set. Please inspect your code and do the needful","warn");$curl(n.api,t).finally((()=>$preloader("hide"))).then((e=>{if(0===e.length)return osNote("No result found within range");n.then(e)}))}),"on")}(),function(e){const t=c.$sel("[data-filter-column]");if(!t)return;const n=e?.options,a=e?.index??t.dataset.filterColumn;t.$html('<option value="__ALL__">All</option>'+n),$(t).on("change",(e=>{let t=e.target.value;t="__ALL__"===t?"":e.target.options[e.target.selectedIndex].innerText,p.column(a).search(t).draw()}))}(a),function(e){const t=c.$sel("[data-multi-filter]");t&&(t.$sel(".multi-filter-body").$html(" "),$loop(e,((e,n)=>{const a=((e,t)=>{let n=e,a=!1;isNaN(e)||(u.$loop(((t,n)=>{n===e&&(e=t.toUpperCase())})),a=!0);const o=e.toLowerCase(),l=o.trim().replaceAll(" ","-"),s=l.replaceAll("-","_");return isNaN(n)&&!a&&(e=e.toUpperCase(),u.$loop(((e,t)=>{e===o&&(n=t)}))),{columnIndex:n,id:"multi-filter-"+l,body:`<div class="mb-10">\n                        <label class="form-label fs-6 fw-semibold">${e}:</label>\n                        <select\n                            id="multi-filter-${l}" \n                            class="form-select form-select-solid fw-bold" \n                            data-kt-select2="true" \n                            data-placeholder="Select option" \n                            data-allow-clear="true" data-kt-user-table-filter="${l}" \n                            data-hide-search="true"\n                            data-column-index="${n}"\n                            name="${s}"\n                        >\n                            <option value="__ALL__">All</option>\n                            ${t}\n                        </select>\n                    </div>`}})(n,e);t.$sel(".multi-filter-body").$html("beforeend",a.body)})),t.$sel('[data-kt-user-table-filter="reset"]').$on("click",(function(){t.$sel('[data-kt-user-table-filter="form"]').querySelectorAll("select").forEach((e=>$(e).val("__ALL__").trigger("change"))),p.search("").draw()}),"on"),(()=>{const e=c.$sel('[data-kt-user-table-filter="form"]'),t=e.$sel('[data-kt-user-table-filter="filter"]'),n=e.querySelectorAll("select");t.addEventListener("click",(function(){p.search.fixed("fun",((e,t)=>{let a=!0;return n.forEach((e=>{const n=e.value.trim();""!==n&&"__ALL__"!==n&&(a=a&&t[e.dataset.columnIndex].includes(e.value))})),a})).draw()}))})())}(o),function(){const e=c.closest(".table-wrap-container").$sel('[data-kt-user-table-search="search"]');e.$on("input",((e,t)=>{$debounce((()=>{p.search(t.value).draw()}),100)}),"on"),e.dispatchEvent(new Event("input"))}(),function(){const e=$lay.page.title;let t=[],n={},a=[];$id("temp-dtable-img-holder")||$html($doc.body,"beforeend",`<img src="${$sel("[rel='icon']",$lay.page.html).href}" style="display: none" id="temp-dtable-img-holder" alt="Page Icon">`),c.$sela("th").$loop(((e,n)=>e.classList.contains("hide-on-export")?"continue":(t.push(n),0===n?(a.push("5%"),"continue"):void a.push("*")))),s&&(s.pdf&&(a=[]),$loop(s,((e,t)=>{n[t]=[],e.$loop((e=>{if("0"===(e=(e="String"!==$type(e)?e+"":e).toLowerCase())||"sn"===e)return n[t].push(0),s.pdf&&"pdf"===t&&a.push("5%"),"continue";s.pdf&&"pdf"===t&&a.push("*");const o=u.findIndex((t=>t===e));-1!==o&&n[t].push(o)}))}))),new $.fn.dataTable.Buttons(p,{buttons:[{extend:"copyHtml5",title:e,exportOptions:{columns:n.copy??t}},{extend:"excelHtml5",title:e,exportOptions:{columns:n.excel??t,format:{body:function(e,t,n,a){const o=$(a).find("a");return o.length>0&&(e=`=HYPERLINK("${o.attr("href")}", "${o.attr("href")}")`),e}}}},{extend:"csvHtml5",title:e,exportOptions:{columns:n.csv??t,format:{body:function(e,t,n,a){const o=$(a).find("a");return o.length>0&&(e=o.attr("href")+" ("+o.attr("href")+")"),e}}}},{extend:"pdfHtml5",title:e,exportOptions:{columns:n.pdf??t,stripHtml:!1},customize:t=>{t.content[1].table.body.forEach(((e,n)=>{e.forEach(((e,a)=>{if("String"===$type(e.text)&&"<"===e.text.trim().substring(0,1)){const o=(new DOMParser).parseFromString(e.text,"text/html").body;t.content[1].table.body[n][a].text=o.textContent,"HTMLAnchorElement"===$type(o.firstChild)&&(t.content[1].table.body[n][a].text=[{text:o.textContent,link:o.firstElementChild.href??o.firstChild.href,decoration:"underline"}])}}))})),t.content.splice(0,1);const n=new Date,o=n.getDate()+"-"+(n.getMonth()+1)+"-"+n.getFullYear();let l="";(async()=>{l=await $img2blob($id("temp-dtable-img-holder"))})(),t.pageMargins=[20,60,20,30],t.defaultStyle.fontSize=8,t.styles.tableHeader.fontSize=8,t.content[0].table.widths=a,t.header=function(){return{columns:[{image:l,width:24},{alignment:"left",bold:!0,text:$lay.page.html.$sel("[name='author']").content,fontSize:17,margin:[5,0]},{alignment:"right",fontSize:14,text:e}],margin:20}},t.footer=function(e,t){return{columns:[{alignment:"left",text:["Created on: ",{text:o.toString()}]},{alignment:"right",text:["page ",{text:e.toString()}," of ",{text:t.toString()}]}],margin:20}};t.content[0].layout={width:function(e){return 800},hLineWidth:function(e){return.5},vLineWidth:function(e){return.5},hLineColor:function(e){return"#53c3bd"},vLineColor:function(e){return"#53c3bd"},paddingLeft:function(e){return 4},paddingRight:function(e){return 4}}}},"colvis"]}).container().appendTo($("#kt_datatable_example_buttons")),$sela("#kt_datatable_example_export_menu [data-kt-export]").$loop((e=>{e.$on("click",(e=>{e.preventDefault();const t=e.target.getAttribute("data-kt-export");$sel(".dt-buttons .buttons-"+t).click()}),"on")}))}(),function(){if(!c.$sel("table").dataset.hasCheckbox)return;const e=c.$sel('[data-kt-docs-table-toolbar="base"]'),t=c.$sel('[data-kt-docs-table-toolbar="selected"]'),n=c.$sel('[data-kt-docs-table-select="selected_count"]'),a=c.$sel('[data-kt-docs-table-select="batch_action"]'),o=c.$sel(".datatable-check-select-all"),l=c.$sel(".datatable-check-select-all-cache");let s=[];const d=()=>{$debounce((()=>{l.value=JSON.stringify(s)}),700)};function u(a){return a.checked?(s.push(a.value),n.innerHTML=s.length,$class(e,"add","d-none"),void $class(t,"del","d-none")):(s.splice(s.indexOf(a.value),1),0===s.length?($class(e,"del","d-none"),void $class(t,"add","d-none")):void(n.innerHTML=s.length))}c.$sela(".entry-checkbox").$loop((e=>{$on(e,"change",((e,t)=>{u(t),d()}),"on")})),o.$on("change",((e,t)=>{s=[],c.$sela("tbody .entry-checkbox").$loop((e=>{e.checked=t.checked,u(e)})),d()}),"on"),a.$on("click",((e,t)=>{e.preventDefault(),r&&(i.clearSelection=()=>{o.checked=!1,o.dispatchEvent(new Event("change"))},r({data:s,utils:i}))}))}(),{tableInstance:p,tableContainer:c}}function _sndMsg(e){CusWind.closeBox(),Swal.fire({html:e,icon:"success",buttonsStyling:!1,confirmButtonText:"Ok, got it!",customClass:{confirmButton:"btn btn-primary",container:"osai-dialogbox__appear"}})}function _ajax(e,t){e.disabled||t()}async function hookTableOnPage({api:e,form:t,apiHeaders:n,entry:a,entryAction:o,entryActionFn:l,deleteMsg:s,batch:r,enableDelete:i=!0,ctrl:c,fetchOnLoad:d=!0,tableBody:u=$sel(".entry-table-body"),scopedFn:p=null,tools:m}){const f=u.closest(".table-wrap-container").$sel(".add-new-entry");function h(t={closeBox:!0,preload:!0,redraw:!0,page:1}){t.closeOnBlur&&CusWind.closeBox();const o={headers:n};return t.preload&&(o.preload=$preloader),a.response?.prefetch&&a.response.prefetch(),$curl(c+e.list.replace("{page}",t.page),o).finally((()=>$preloader("hide"))).then((e=>{b(e),a.response?.postfetch&&a.response.postfetch()}))}function b(r){if(dataTable({destroy:!0}),r.code&&(!r.data||0===r.data.length)||0===r.length)return void $html(u,"in",'<tr class="dt-no-data-found"><td colspan="100%" style="text-align: center">No data found!</td></tr>');let d="",p=[],f={},$={},y=[],g="";const v=u.closest("table").dataset.hasCheckbox;let x=a?.anchor?.checkbox??!0;$loop(r,((e,t)=>{if(t++,a.filter&&!a.multiFilter){const t=a.filter(e),n=t.name,o=t.value??t.name;p.includes(n)||(p.push(n),g+=`<option value="${o}">${n}</option>`)}a.multiFilter&&$loop(a.multiFilter(e),((e,t)=>{const n=e.name,a=e.value??e.name;y.includes(t)||y.push(t),f[t]||(f[t]=[],$[t]=""),f[t].includes(n)||(f[t].push(n),$[t]+=`<option value="${a}">${n}</option>`)})),a.anchor=a.anchor??{},a.anchor.actions=a.anchor.actions??[];const n=0===a.anchor.actions.length&&"Function"===$type(a.anchor.actionsFn)?a.anchor.actionsFn({id:e[a.anchor.id],info:e}):a.anchor.actions;let o="";a.rowDataset&&$loop(a.rowDataset(e,t),((e,t)=>o+=`data-${t}="${e}" `));let l="";v&&(l+=x?`<td>\n                    <div \n                        class="form-check form-check-sm form-check-custom form-check-solid"\n                    >\n                        <input class="form-check-input entry-checkbox" type="checkbox" value="${e[a.anchor.id]}">\n                    </div>\n                </td>`:""),d+=`<tr ${a.rowAttribute?a.rowAttribute(e,t):null} ${o}>\n                    ${l}\n                    \n                    ${a.row(e,t)}\n                    \n                    ${a.anchor?.id?`<td class="text-end">\n                        ${$lay.fn.rowEntrySave(e)}\n                              \n                        ${function({id:e,name:t,menu:n}){t=t?encodeURIComponent(t):"";const a=n=>{const a=n.separator?'<div class="separator mb-3 opacity-75"></div>':"";if(!n.name)return a;const o=n.href??"javascript:void(0);",l=n.target?`target="${n.target}"`:"",s=n.act?"table-action":"";let r=`<a href="${o}" ${l} class="${n.wrap?"":"menu-link"} px-3 ${s} ${n.className??""}" data-id="${e}" data-name="${t}" data-action="${n.act}">${n.name}</a>`;return r=n.wrap?`<div class="menu-content px-3 pt-0" style="text-align: center">${r}</div>`:r,`<div class="menu-item px-3">${r}</div>${a}`};let o="";return $loop(n,(e=>{let t="";if("Array"===$type(e))return $loop(e.submenu,(e=>t+=a(e))),o+=`<div class="menu-item px-3 text-start" data-kt-menu-trigger="hover" data-kt-menu-placement="right-start">\n                        <a href="#" class="menu-link px-3">\n                            <span class="menu-title">${e.name}</span>\n                            <span class="menu-arrow"></span>\n                        </a>\n                        <div class="menu-sub menu-sub-dropdown w-175px py-4">\n                            ${t}\n                        </div>\n                    </div>`,"continue";o+=a(e)})),`<div class="card-toolbar">\n                \n            <button type="button" class="btn btn-icon btn-color-gray-400 btn-active-color-primary justify-content-end" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">\n                <i class="ki-outline ki-dots-square fs-1 text-gray-400 me-n1"></i>\n            </button>\n            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-auto" data-kt-menu="true">\n                \x3c!--begin::Menu item--\x3e\n                <div class="menu-item px-3">\n                    <div class="menu-content fs-6 fw-bold px-3 py-4 text-center">Actions</div>\n                </div>\n                \n                ${o}\n            </div>\n        </div>`}({id:e[a.anchor.id],name:e[a.anchor.name],menu:[!1===a.anchor.edit?"":a.anchor.edit?a.anchor.edit({id:e[a.anchor.id]}):{name:"Edit Item",act:"edit"},...n,a.anchor.delete||i?{separator:!0}:{},!1===a.anchor.delete?{}:i?{name:"Delete Item",act:"delete",wrap:!0,className:"btn btn-danger btn-sm"}:!1===i?{}:a.anchor.delete({id:e[a.anchor.id],info:e})]})}\n                    </td>`:""}\n                </tr>`})),$html(u,"in",d),a.then&&a.then();const k={};a.filter&&(k.singleFilter={column:a.filter.index,options:g},k.filterColumnIndex=a.filter.index,k.filterColumnOptions=g),a.multiFilter&&(k.multiFilter=$),a.export&&(k.exportColumns=a.export),a.search&&(k.searchTableFn=(e,t)=>{a.search(e,{populateTable:e=>b(e),loadEntries:e=>h(e)}),t.tableContainer.$sel(".reset-table-entries").$on("click",((e,n)=>{e.preventDefault(),h().then((()=>{n.$class("add","d-none"),t.tableContainer.$sel(".search-table").value="",t.tableInstance.search("").draw()}))}),"on")}),e.dateRange&&(k.dateRangeObj={api:c+e.dateRange,headers:n,then:e=>b(e)}),m?.checkboxAction&&(k.checkboxActionUtils={loadEntries:h},k.checkboxAction=m.checkboxAction),dataTable(k),function(){let a={edit:({info:a,name:o,id:l})=>{a.FORM_ACTION="EDIT",osModal({head:"Edit "+o,foot:"",closeOnBlur:!1,size:"lg",body:`<form>\n                            ${t.body(a)}\n                            <div class="text-center">\n                                <input type="hidden" name="id" value="${l}">\n                                <div class="text-center">${$facades.submitBtn()}</div>\n                            </div>\n                        </form>`,then:()=>{t.then&&t.then("EDIT"),$on($sel(".submit-form"),"click",((a,o)=>{a.preventDefault(),t.onSubmit&&!t.onSubmit(o)||ajax(o,(()=>$curl(c+e.edit,{preload:()=>preloadBtn(o),data:o,method:e.useRest&&e.edit===e.add?"PUT":"POST",headers:n}).finally((()=>preloadBtn(o,!1))).then((e=>{_sndMsg(e.message),h()}))))}),"on")}})},delete:({name:t,id:a})=>{i&&Swal.fire({html:s?s(t):"Are you sure you want to delete: <b>"+t+"</b>?",icon:"warning",buttonsStyling:!1,confirmButtonText:"Yeah, do it!",cancelButtonText:"Cancel",customClass:{confirmButton:"btn btn-primary"}}).then((t=>{t.isConfirmed&&$curl(c+e.delete,{preload:$preloader,data:{id:a},method:e.useRest&&e.delete===e.add?"DELETE":"POST",headers:n}).finally((()=>$preloader("hide"))).then((e=>{h(),osNote(e.message,"success")}))}))}};o=l?l({loadEntries:h,populateTable:b}):o,$loop(o,((e,t)=>{a[t]=t=>{t.closure=h,e(t)}})),function(e={}){e.then=KTMenu.createInstances,$lay.fn.rowEntryAction(e)}(a)}()}if(p=m?.scopedFn??p,r=m?.batch??r,i=!!e.delete&&i,e.useRest=e.useRest??!0,f&&$on(f,"click",(a=>{a.preventDefault(),osModal({head:t.head,foot:"",closeOnBlur:!1,size:t.size??"lg",body:`<form>\n                            ${t.body({FORM_ACTION:"ADD"})}\n                            <div class="text-center">\n                                ${$facades.submitBtn()}\n                            </div>\n                        </form>`,then:()=>{t.then&&t.then("ADD"),$on($sel(".submit-form"),"click",((a,o)=>{a.preventDefault(),t.onSubmit&&!t.onSubmit(o)||_ajax(o,(()=>$curl(c+e.add,{preload:()=>preloadBtn(o),data:o,headers:n}).finally((()=>preloadBtn(o,!1))).then((e=>{_sndMsg(e.message),h()}))))}))}})}),"on"),r&&function({newEntryBtn:e,csvSrc:t,csv:a,note:o,api:l,callback:s}){$on(e,"click",(e=>{e.preventDefault(),osModal({head:"Batch Upload",foot:"",closeOnBlur:!1,size:"lg",body:`<form>\n                    <p class="d-flex align-items-center g-2 flex-wrap">\n                        Download the \n                        <a href="${t??CSV_SRC+a}" class="fw-bold text-gray-800 text-hover-primary d-inline-flex align-items-center mx-2" download>\n                            <span class="icon-wrapper">\n                                <i class="ki-outline ki-file fs-2x text-primary me-1"></i>\n                            </span>\n                            template CSV file\n                        </a>\n                        modify and upload below.\n                    </p>\n                    <h5 class="text-warning-emphasis">${o}</h5>\n                    <div class="mb-3 p-3">\n                        <div id="batch-upload-csv"></div>\n                    </div>\n                </form>`,then:()=>{initDropzone({api:l,id:"batch-upload-csv",fileTypes:"text/csv",uploadFn:(e,t,a)=>{const o=new FormData;o.append("file",e);let r=0;$curl(l,{data:o,type:"file",headers:n,progress:e=>{r=Math.floor(e.loaded/e.total*100),a.style.width=r+"%"}}).then((n=>{r>=100&&(t.emit("success",e),t.emit("complete",e)),_sndMsg(n.message),s()}))}})}})}),"on")}({newEntryBtn:$sel(".add-in-batch"),api:c+e.batch,csvSrc:r.csvSrc,csv:r.csv,note:r.note,callback:h}),p&&p({loadEntries:h}),d)return h()}function initDropzone({api:e,id:t="drag-drop-place",maxFileSize:n=1,fileTypes:a,uploadFn:o,parallelUploads:l=10,onQueueComplete:s}){const r=$id(t);r.$html(`<style>\n            .drop-zone-custom {\n                width: 100%;\n                height: 300px;\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                cursor: pointer;\n                background: transparent;\n                color: var(--bs-body-color);\n                font-weight: bold;\n                font-size: 2.5rem;\n                transition: ease all .8s;\n            }\n            .drop-zone-custom:hover {\n                transition: ease all .8s;\n                background: var(--bs-gray-dark);\n                color: var(--bs-light);\n            }\n        </style>\n        <div class="dropzone dropzone-queue mb-2" id="${t}">\n            <div class="dropzone-panel mb-4">\n                <div class="d-flex gap-2">\n                    <a class="dropzone-select dz-button dz-clickable drop-zone-custom">Add Files</a>\n                    <a class="dropzone-upload btn btn-sm btn-light-primary me-2">Upload All</a>\n                    <a class="dropzone-remove-all btn btn-sm btn-light-danger">Remove All</a>\n                </div>\n                \n                <div class="dropzone-items wm-200px">\n                    <div class="dropzone-item p-5" style="display:none">\n                        <div class="dropzone-file">\n                            <div class="dropzone-filename" title="some_image_file_name.jpg">\n                                <span data-dz-name="">some_image_file_name.jpg</span>\n                                <strong>(\n                                    <span data-dz-size="">340kb</span>)</strong>\n                            </div>\n                            <div class="dropzone-error mt-0" data-dz-errormessage=""></div>\n                        </div>\n                        \n                        <div class="dropzone-progress">\n                            <div class="progress bg-gray-300">\n                                <div class="progress-bar bg-primary" role="progressbar" aria-valuemin="0" aria-valuemax="100" aria-valuenow="0" data-dz-uploadprogress=""></div>\n                            </div>\n                        </div>\n                        \n                        <div class="dropzone-toolbar">\n                            <span class="dropzone-start">\n                                <i class="ki-outline ki-to-right fs-1"></i>\n                            </span>\n                            <span class="dropzone-cancel" data-dz-remove="" style="display: none;">\n                                <i class="ki-outline ki-cross fs-2"></i>\n                            </span>\n                            <span class="dropzone-delete" data-dz-remove="">\n                                <i class="ki-outline ki-cross fs-2"></i>\n                            </span>\n                        </div>\n                    </div>\n                    <div class="form-text text-center fs-6 text-muted my-5">Max file size is <b>${n}MB</b> per file.</div>\n                </div>\n            </div>\n        </div>`);const i=r.$sel(".dropzone-item");i.id="",t="#"+t;const c=i.parentNode.innerHTML;i.parentNode.removeChild(i);const d=new Dropzone(t,{url:e,parallelUploads:l,previewTemplate:c,maxFilesize:n,acceptedFiles:a,autoProcessQueue:!1,autoQueue:!1,previewsContainer:t+" .dropzone-items",clickable:t+" .dropzone-select"});d.on("addedfile",(function(e){e.previewElement.querySelector(t+" .dropzone-start").onclick=function(){const t=e.previewElement.querySelector(".progress-bar");t.style.opacity="1",o(e,d,t)},r.querySelectorAll(".dropzone-item").forEach((e=>{e.style.display=""})),r.querySelector(".dropzone-select").classList.remove("drop-zone-custom"),r.querySelector(".dropzone-select").classList.add("btn","btn-sm","btn-primary"),r.querySelector(".dropzone-upload").style.display="inline-block",r.querySelector(".dropzone-remove-all").style.display="inline-block"})),d.on("complete",(function(e){setTimeout((function(){e.previewElement.$sel(".progress-bar").style.opacity="0",e.previewElement.$sel(".progress").style.opacity="0",e.previewElement.$sel(".dropzone-start").style.display="none",e.status===Dropzone.SUCCESS&&(e.previewElement.$sel(".dropzone-toolbar").$html("afterbegin",'<span class="dropzone-start"><i class="ki-outline ki-file-added fs-3 text-success"></i></span>'),e.previewElement.classList.add("dz-success")),d.files.length>0&&d.files.every((e=>e.status===Dropzone.SUCCESS))&&(r.$sel(".dropzone-upload").style.display="none",r.$sel(".dropzone-remove-all").style.display="none",s(d))}),300)})),r.$sel(".dropzone-upload").$on("click",(function(){d.files.forEach((e=>{if(e.status!==Dropzone.SUCCESS&&e.status!==Dropzone.UPLOADING){const t=e.previewElement.querySelector(".progress-bar");t.style.opacity="1",o(e,d,t)}}))})),r.$sel(".dropzone-remove-all").$on("click",(function(){Swal.fire({text:"Are you sure you would like to remove all files?",icon:"warning",showCancelButton:!0,buttonsStyling:!1,confirmButtonText:"Yes, remove it!",cancelButtonText:"No, return",customClass:{confirmButton:"btn btn-primary",cancelButton:"btn btn-active-light",container:"osai-dialogbox__appear"}}).then((function(e){e.value?(r.querySelector(".dropzone-upload").style.display="none",r.querySelector(".dropzone-remove-all").style.display="none",d.removeAllFiles(!0)):"cancel"===e.dismiss&&(osNote("Your files was not removed!."),Swal.fire({text:"Your files was not removed!.",icon:"error",buttonsStyling:!1,confirmButtonText:"Ok, got it!",customClass:{confirmButton:"btn btn-primary",container:"osai-dialogbox__appear"}}))}))})),d.on("queuecomplete",(function(){r.querySelectorAll(".dropzone-upload").forEach((e=>e.style.display="none")),r.$sel(".dropzone-upload").style.display="none",r.$sel(".dropzone-remove-all").style.display="none"})),d.on("removedfile",(function(e){d.files.length<1&&(r.querySelector(".dropzone-select").classList.add("drop-zone-custom"),r.querySelector(".dropzone-select").classList.remove("btn","btn-sm","btn-primary"),r.querySelector(".dropzone-upload").style.display="none",r.querySelector(".dropzone-remove-all").style.display="none")}))}