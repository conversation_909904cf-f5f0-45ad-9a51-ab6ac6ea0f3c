function serverResponse(t,n,o=null,e=!0,r=!0){if(1===t||200===t||201===t){r&&CusWind.closeBox();const t=Swal.fire({html:n,icon:"success",buttonsStyling:!1,confirmButtonText:"Ok, got it!",customClass:{confirmButton:"btn btn-primary",container:"osai-dialogbox__appear"}});return e?t.then((t=>o&&t.isConfirmed&&o())):o()}if(2===t)return Swal.fire({html:n,icon:"warning",buttonsStyling:!1,confirmButtonText:"Oh, okay",customClass:{confirmButton:"btn btn-primary",container:"osai-dialogbox__appear"}});osNote(n,"warn",{position:"center"})}function preloadBtn(t,n=!0){if(n)return t.setAttribute("data-kt-indicator","on"),void(t.disabled=!0);t.removeAttribute("data-kt-indicator"),t.disabled=!1}function ajax(t,n){t.disabled||n()}function monitorXHR(){const t=XMLHttpRequest.prototype.open;XMLHttpRequest.prototype.open=function(){this.addEventListener("load",(function(){try{const t=JSON.parse(this.responseText);if(909===t.code)return Swal.fire({html:t.message+". Click on `Refresh Session` to login on another tab, then return to this tab, to continue your work.",icon:"warning",buttonsStyling:!1,showCancelButton:!0,confirmButtonText:"Refresh Session",cancelButtonText:"Sign out, I'm done",customClass:{confirmButton:"btn btn-info",cancelButton:"btn btn-danger",container:"osai-dialogbox__appear"}}).then((t=>{if(t.isConfirmed)return $win.open($lay.src.base+"sign-in","_blank");location.href=$lay.src.base+"sign-in"}))}catch(t){}})),t.apply(this,arguments)}}function saveCsrf(t){$store.setItem("osaitech_app_token",t)}function getCsrf(){return $store.getItem("osaitech_app_token")}function apiHeaders(t={}){return{"X-CSRF-TOKEN":getCsrf(),...t}}function genApi(t){return $lay.src.api+"v1/"+DASH_PREFIX+"/"+t.replace(/^\//,"")}CusWind.config({head:"color: var(--bs-body-color);",wrapper:"background: var(--bs-app-bg-color); color: var(--bs-body-color);",foot:"color: var(--bs-body-color);"});