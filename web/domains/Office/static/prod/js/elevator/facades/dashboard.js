monitorXHR(),$on($sel(".sign-out-now"),"click",(s=>{s.preventDefault(),Swal.fire({text:"Are you sure you want to sign out?",icon:"warning",buttonsStyling:!1,confirmButtonText:"Yes, logout",customClass:{confirmButton:"btn btn-primary"}}).then((s=>{s.isConfirmed&&(location.href=$lay.src.base+"sign-out")}))})),$on($sel(".change-password"),"click",(()=>{osModal({head:"Change Password",foot:"",closeOnBlur:!1,size:"sm",body:`<form>\n                <div class="mb-10 form-floating">\n                    <input id="form-o-pass" type="password" class="form-control form-control-lg form-control-solid" name="old_password" required />\n                    <label for="form-o-pass" class="form-label required">Old Password</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="form-n-pass" type="password" class="form-control form-control-lg form-control-solid" name="password" required />\n                    <label for="form-n-pass" class="form-label required">New Password</label>\n                </div>\n                \n                <div class="form-check form-switch form-check-custom form-check-solid mb-10 lay-show-password" data-field="#form-o-pass,#form-n-pass" data-event="change">\n                    <input class="form-check-input" type="checkbox" id="flexSwitchChecked" />\n                    <label class="form-check-label" for="flexSwitchChecked">\n                        Show Password\n                    </label>\n                </div>\n                \n                <div class="text-center">\n                    ${$facades.submitBtn()}\n                </div>\n            </form>`,then:()=>{$showPassword(),$on($sel(".submit-form"),"click",((s,o)=>{s.preventDefault(),$curl(genApi("/user/change-password"),{preload:()=>preloadBtn(o),data:o,headers:apiHeaders()}).finally((()=>preloadBtn(o,!1))).then((s=>serverResponse(s.code,s.message)))}))}})}));const $facades={submitBtn:(s="Submit",o="btn-primary submit-form",n="Please wait...")=>`<button class="btn me-2 flex-shrink-0 ${o} w-75">\n                <span class="indicator-label">${s}</span>\n    \n                <span class="indicator-progress">\n                    <span data-kt-translate="general-progress">${n}</span>\n                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>\n                </span>\n            </button>`,date:(s,o="DD MMM, y - h:mm a")=>moment(s,"YYYY-MM-DD hh:mm:ss").format(o)};$id("must-reset-login-pass")&&(osNote("You must change your password to prevent this box from popping up"),$sel(".change-password").click());