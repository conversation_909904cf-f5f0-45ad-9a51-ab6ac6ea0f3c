simpleTablePage({api:{add:"blog/collection/new",edit:"blog/collection/edit",list:"blog/collection/list",delete:"blog/collection/delete"},form:{head:"Blog Collection",body:(e={})=>`<div class="mb-10 form-floating">\n                    <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="name" value="${e.name??""}" placeholder="Name" required />\n                    <label for="form-b-name" class="form-label mb-3 required">Name</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <textarea class="form-control form-control-solid" placeholder="Category Description" id="form-b-desc" name="description" style="height: 200px">${e.desc??""}</textarea>\n                    <label for="form-b-desc" class="form-label">Description</label>\n                </div>`},entry:{row:(e,l)=>`<td>${l}</td>\n                <td>${e.name}</td>\n                <td>${e.desc}</td>`,anchor:{id:"id",name:"name"}},deleteMsg:e=>`Are you sure you want to delete this collection [${e}]? If it has been used, it will remain there, but you won't find it in subsequent lists`});