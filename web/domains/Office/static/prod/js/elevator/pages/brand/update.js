$sela(".save-changes").forEach((e=>{$on(e,"click",((e,a)=>{if(e.preventDefault(),$exceeds($sel(".avatar-dp"),3e6))return osNote("Your image size is too large, try something less than 3mb","warn");ajax(a,(()=>$curl(genApi("/brand/update"),{preload:()=>preloadBtn(a),data:a,headers:apiHeaders()}).finally((()=>preloadBtn(a,!1))).then((e=>{setTimeout((()=>$loc.reload()),3e3),osNote(e.message,"success")}))))}))}));