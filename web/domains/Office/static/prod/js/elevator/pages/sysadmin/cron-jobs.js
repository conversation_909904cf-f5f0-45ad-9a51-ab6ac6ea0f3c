simpleTablePage({api:{add:"sys-arch/cron-jobs/new",list:"sys-arch/cron-jobs/list",edit:"sys-arch/cron-jobs/edit",delete:"sys-arch/cron-jobs/delete"},deleteMsg:e=>`Are you sure you want to delete this job: <b>${e}</b>?`,form:{head:"New Cron Job",body:(e={})=>{let t="checked";return"String"===$type(e.use_php)&&(t="1"===e.use_php?"checked":""),`<h3 class="mb-2 text-center"><a rel="noreferrer" href="https://crontab.guru/" target="_blank">Schedule Examples</a></h3>\n\n                <div class="form-floating">\n                    <input id="form-b-schedule" class="form-control form-control-lg form-control-solid text-center" style="font-size: 2rem; padding: 3rem 1rem 1.5rem" name="schedule" value="${e.schedule??""}" required />\n                    <label for="form-b-schedule" class="form-label mb-3 required">Schedule (* * * * *) [5]</label>\n                </div>\n                <div class="text-muted mb-10 text-center">Schedule Example: 30 18 * * *</div>\n                \n                <div class="form-floating">\n                    <input id="form-b-script" class="form-control form-control-lg form-control-solid text-center" name="script" value="${e.script??""}" required />\n                    <label for="form-b-script" class="form-label mb-3 required">Script</label>\n                </div>\n                <div class="text-muted mb-5 text-center">Script Example: utils/db-backup.php </div>\n                \n                <div class="form-check p-3 mb-10 form-switch form-check-custom form-check-solid d-flex flex-row col-md-2 col-4 justify-content-center w-100 gap-3">\n                    <input class="form-check-input" type="checkbox" ${t} type="checkbox" value="true" id="use-php-script" name="use_php" />\n                    <label class="form-check-label mx-0 mt-2 fw-bold" for="use-php-script">\n                        Run As PHP Script \n                    </label>\n                </div>`},then:()=>{}},entry:{row:e=>{let t='<div class="badge badge-outline badge-success">Running</div>';return"0"===e.active&&(t='<div class="badge badge-outline badge-warning">Paused</div>'),`<td>${e.schedule}</td>\n                <td>${e.script}</td>\n                <td>${t}</td>\n                <td>${e.last_run?$facades.date(e.last_run):"Never"}</td>`},anchor:{id:"id",name:"script",actionsFn:({info:e})=>[{name:"Run Script",act:"run"},"1"===e.active?{name:"Pause Script",act:"pause",className:"text-warning"}:{name:"Play Script",act:"play",className:"text-primary"}]}},entryActionFn:({loadEntries:e})=>({run:({name:t,id:r})=>{Swal.fire({html:`Are you sure you want to run this script? <b>${t}</b>`,icon:"info",buttonsStyling:!1,confirmButtonText:"Run it!",cancelButtonText:"Cancel",customClass:{confirmButton:"btn btn-primary"}}).then((t=>{t.isConfirmed&&$curl(genApi("/sys-arch/cron-jobs/run"),{preload:$preloader,data:{id:r},headers:apiHeaders()}).finally((()=>$preloader("hide"))).then((t=>{e(),osNote(t.message,"success")}))}))},pause:({name:t,id:r})=>{Swal.fire({html:`You are about <b>pause</b> this script? <b>${t}</b>`,icon:"warning",buttonsStyling:!1,confirmButtonText:"Pause it!",cancelButtonText:"Cancel",customClass:{confirmButton:"btn btn-primary"}}).then((t=>{t.isConfirmed&&$curl(genApi("/sys-arch/cron-jobs/pause"),{preload:$preloader,data:{id:r},headers:apiHeaders()}).finally((()=>$preloader("hide"))).then((t=>{e(),osNote(t.message,"success")}))}))},play:({name:t,id:r})=>{Swal.fire({html:`Script will <b>Play</b> if you continue? <b>${t}</b>`,icon:"info",buttonsStyling:!1,confirmButtonText:"Play it!",cancelButtonText:"Cancel",customClass:{confirmButton:"btn btn-primary"}}).then((t=>{t.isConfirmed&&$curl(genApi("/sys-arch/cron-jobs/play"),{preload:$preloader,data:{id:r},headers:apiHeaders()}).finally((()=>$preloader("hide"))).then((t=>{let r="warn";200===t.code&&(r="success",e()),osNote(t.message,r)}))}))}})});