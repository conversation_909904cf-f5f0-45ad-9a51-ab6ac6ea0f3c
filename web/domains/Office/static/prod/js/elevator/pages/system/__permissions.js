let moduleCache=[];const getModules=async()=>$curl(genApi("/system/roles/modules"),{headers:apiHeaders()}).then((e=>moduleCache=e)),permissionTemplate=({permissions:e=null,xPermissions:s=null,disableSelected:l=!1,disableAll:a=!1,isSuperAdmin:c})=>{let n="";return 0===moduleCache.length&&getModules(),c&&(l=!0),$loop(moduleCache,(t=>{let o=c?"checked disabled":"",d="";$loop(t.access,(c=>{$loop(e,(e=>""!==o?"break":(o="",e!==c.value?"continue":void(o="checked"+(l?" disabled":""))))),null!==s&&$loop(s,(e=>""!==o?"break":(o="",e!==c.value?"continue":void(o="checked")))),o+=a?" disabled":"",d+=`<label class="form-check form-check-sm form-check-custom me-5 me-lg-20">\n                    <input class="form-check-input" type="checkbox" name="permissions[]" ${o} value="${c.value}" id="system-module-${t.id}-${c.type}" />\n                    <span class="form-check-label">${c.type}</span>\n                </label>`,o=""})),n+=`<tr>\n                <td class="text-gray-800">${t.name}</td>\n                <td><div class="d-flex">${d}</div></td>\n            </tr>`})),`<div class="fv-row role-resting-place">\n            <label class="fs-5 fw-bold form-label mb-2">Role Permissions</label>\n            \n            <div class="table-responsive">\n                <table class="table align-middle table-row-dashed fs-6 gy-5">\n                    <tbody class="text-gray-600 fw-semibold">\n                        <tr>\n                            <td class="text-gray-800">Administrator Access\n                                <span class="ms-2" data-bs-toggle="popover" data-bs-trigger="hover" data-bs-html="true" data-bs-content="Allows a full access to the system">\n                                    <i class="ki-outline ki-information fs-7"></i>\n                                </span>\n                            </td>\n                            <td>\n                                <label class="form-check form-check-custom me-9">\n                                    <input class="form-check-input" type="checkbox" value="" id="select-all-perm" ${a?"disabled":""} ${c?"checked disabled":""} />\n                                    <span class="form-check-label">Select all</span>\n                                </label>\n                            </td>\n                        </tr>\n                        <div class="row">${n}</div>\n                    </tbody>\n                </table>\n            </div>\n        </div>`},selectAllPermissions=()=>{$on($id("select-all-perm"),"click",((e,s)=>{const l=s.checked;0!==$name("permissions[]").length&&$loop($name("permissions[]"),(e=>e.checked=l))}))};getModules();