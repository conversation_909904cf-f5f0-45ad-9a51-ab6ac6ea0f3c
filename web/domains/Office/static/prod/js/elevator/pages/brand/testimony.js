simpleTablePage({api:{add:"brand/testimony/",batch:"brand/testimony/bulk",edit:"brand/testimony/update",list:"brand/testimony/{page}",delete:"brand/testimony/"},form:{head:"Add a New Testimony",body:(e={})=>{const t=e.dp??DP_PLACEHOLDER;return`<div class="mb-10 d-flex justify-content-center align-items-center flex-column">\n                    <h5 class="mb-5">Client's DP</h5>\n                    <div class="text-center">\n                        <div class="image-input image-input-outline" data-kt-image-input="true" style="background-image: url('${t}')">\n                            <div class="image-input-wrapper w-125px h-125px" style="background-image: url(${t})"></div>\n                    \n                            <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" title="Change avatar">\n                                <i class="ki-outline ki-pencil fs-7"></i>\n                                <input type="file" data-max-size="2500000" name="dp" accept=".png, .jpg, .jpeg" />\n                                <input type="hidden" name="avatar_remove" />\n                            </label>\n                    \n                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title="Cancel avatar">\n                                <i class="ki-outline ki-cross fs-2"></i>\n                            </span>\n                    \n                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" title="Remove avatar">\n                                <i class="ki-outline ki-cross fs-2"></i>\n                            </span>\n                        </div>\n                    \n                        <div class="form-text">Allowed file types: png, jpg, jpeg.</div>\n                    </div>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-name" class="form-control form-control-lg form-control-solid" name="name" value="${e.name??""}" placeholder="Client's Name" required />\n                    <label for="c-name" class="form-label mb-3 required">Name</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-comp" class="form-control form-control-lg form-control-solid" name="company" placeholder="Project" value="${e.company??""}" />\n                    <label for="c-comp" class="form-label mb-3">Company</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-post" class="form-control form-control-lg form-control-solid" name="position" value="${e.position??""}" placeholder="position" />\n                    <label for="c-post" class="form-label mb-3 ">Position</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <textarea class="form-control form-control-solid" placeholder="Client's Testimony" id="product-desc" name="testimony" style="height: 200px" required>${e.message??""}</textarea>\n                    <label for="product-desc" class="required">Message</label>\n                </div>`},then:()=>KTImageInput.init()},batch:{csv:"testimonies.csv",note:"Please note that if you have any existing client, whose name is inside this csv file, the new testimony in the file will overwrite what is already on the database."},entry:{row:(e,t)=>(e.dp=e.dp??DP_PLACEHOLDER,`<td>${t}</td>\n                <td><img src="${e.dp}" alt="dp" style="max-width: 50px" /></td>\n                <td>${e.name}</td>\n                <td>${e.company}</td>\n                <td>${e.position}</td>`),anchor:{id:"id",name:"name"}},deleteMsg:e=>`You are about to delete the testimony of [${e}], are you sure you want to do that?`});