simpleTablePage({api:{add:"brand/faqs/",batch:"brand/faqs/bulk",edit:"brand/faqs/",list:"brand/faqs/{page}",delete:"brand/faqs/"},form:{head:"Add New FAQ",body:(e={})=>`<div class="mb-10 form-floating">\n                <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="question" value="${e.question??""}" placeholder="FAQ" required />\n                <label for="form-b-name" class="form-label mb-3 required">Question</label>\n            </div>\n            <div class="mb-10 form-floating">\n                <textarea class="form-control form-control-solid" placeholder="Answer" id="form-b-desc" name="answer" style="height: 200px" required>${e.answer??""}</textarea>\n                <label for="form-b-desc" class="form-label required">Answer</label>\n            </div>`},batch:{csv:"faqs.csv",note:"Please note that if you have any existing question inside the file, the question will be overwritten with the current answer."},entry:{row:(e,a)=>`<td>${a}</td>\n            <td>${e.question}</td>\n            <td>${e.answer}</td>`,anchor:{id:"id",name:"question"}},deleteMsg:e=>`You are about to delete the question [${e}], are you sure you want to do that?`});