function deleteFrequencyEntry(e){if("1"===$data(e,"confirm"))return e.closest(".workflow-freq").remove();osNote("Click again to delete it","dark",{onClose:()=>{$data(e,"confirm","0"),e.$class("del","btn-warning"),e.$class("add","btn-danger")}}),e.$class("del","btn-danger"),e.$class("add","btn-warning"),$data(e,"confirm","1")}!function(){$sel("head").$html("beforeend","<style>.tagify__inline__suggestions{z-index: 9992}</style>");let e=[],n=[],t=[],a={};const l=["active-workflow-here","border","border-primary","border-2","rounded-3"],o=(n="",a=0)=>{let l="";return e.$loop((e=>l+=`<option ${n?.period===e.period?"selected":""} value="${e.period}">${e.period.replaceAll("_"," ")}</option>`)),$sela(".workflow-freq")&&$sela(".workflow-freq").$loop((e=>a=parseInt($data(e,"index")??a))),a+=1,n&&t.push(n),`<div data-index="${a}" class="workflow-freq d-flex gap-2 mb-5 border-primary pb-2" id="freq-picker-entry-${a}" style="border-bottom: solid 1px">\n                <div class="w-100">\n                    <select name="frequency[${a}]" class="form-select mb-2" required>\n                        <option value="">Frequency Type</option>\n                        ${l}\n                    </select>\n                    \n                    <div class="preview-frequency d-flex flex-wrap gap-2"></div>\n                </div>\n                \n                <button type="button" title="Add frequency" onclick="deleteFrequencyEntry(this)" class="btn h-auto btn-danger btn-icon delete-entry" data-confirm="0"><i class="ki-outline ki-trash"></i></button>\n            </div>`},r=(e=null)=>{if(!e)return o();t=[];let n="";return $loop(e,((e,t)=>n+=o(e,t))),n},i=(n,l,o=!0)=>{if(0===l.selectedIndex)return;const r=e[l.selectedIndex-1],i=l.closest(".workflow-freq"),d=i.$sel(".preview-frequency");r.unit.$loop(((e,l)=>{const o=$data(i,"index"),r="wk-input-"+i.id+e.id+o,s=e.values,c=t[n]?t[n].unit[l].values:"",f=n;return $id(r)?"continue":(e=e.id,d.$html("beforeend",`<input id="${r}" name="values[${o}][${e}]" value="${c}" placeholder="${"Pick "+e.replaceAll("_"," ")}" class="form-control">`),"TIME"===e?$("#"+r).flatpickr({enableTime:!0,noCalendar:!0,dateFormat:"H:i"}):(a[f]||(a[f]=[]),void a[f].push(new Tagify($sel("#"+r),{whitelist:s,originalInputValueFormat:e=>e.map((e=>e.value)).join(","),userInput:!0,enforceWhitelist:!0,dropdown:{maxItems:31,classname:"tagify__inline__suggestions",enabled:0,closeOnSelect:!1}}))))}))},d=e=>{$sela(".workflow-freq select").$loop(((n,t)=>{$on(n,"change",((n,a)=>i(t,a,e))),e&&i(t,n,e)}))};((n=!1)=>{(0===e.length||n)&&$curl(genApi("/ai/workflow/frequency"),{catch:!0}).then((n=>e=n))})(),$curl(genApi("/ai/workflow/predefined"),{catch:!0}).then((e=>n=e)),simpleTablePage({api:{add:"ai/workflow/new",edit:"ai/workflow/edit",list:"ai/workflow/all/{page}"},form:{head:"Create a New Workflow",body:(e={})=>{let t="";return n.$loop((n=>{t+=(e=>`<a href="#" class="col-md-6 col-xxl-4 hover-elevate-up all-pred-workflow" data-id="${e.id}">\n                <div class="card ${e.selected===e.id?l.join(" "):""}">\n                    <div class="card-body d-flex flex-center flex-column p-9">\n                        <div class="mb-5">\n                            <div class="symbol symbol-75px symbol-circle">\n                                <span class="symbol-label bg-light-danger text-danger fs-5 fw-bolder">${e.name.slice(0,2)}</span>\n                            </div>\n                        </div>\n                        \n                        <span class="fs-4 text-gray-800 text-hover-primary fw-bold mb-0">${e.name}</span>\n                        \n                        <div class="border border-gray-300 border-dashed rounded p-3 mt-3">\n                            <p class="text-gray-700 fw-semibold fs-5 m-0">${e.about}</p>\n                        </div>\n                    </div>\n            \n                </div>\n            </a>`)({id:n.id,name:n.name,about:n.about,selected:e.templateId})})),`<div class="mb-10">\n                        <label class="form-label required fs-4 fw-semibold mb-2">Select a Template</label>\n                        <div class="row g-6 g-xl-9">\n                            ${t}\n                            <input name="template" type="hidden" value="${e.templateId??""}">\n                        </div>\n                    </div>\n    \n                    <div class="mb-10">\n                        ${getAgentSelect(e.agentId)}\n                    </div>\n                    \n                    <div class="mb-10 form-floating">\n                        <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="name" value="${e.name??""}" placeholder="Name" required />\n                        <label for="form-b-name" class="form-label mb-3 required">Workflow Name</label>\n                    </div>\n                    \n                    <div class="mb-10 all-container">\n                        <div class="d-flex justify-content-between flex-center mb-4 border border-top-0 border-bottom-0 border-1 border-primary p-4"\n                            style="box-shadow: 0 0 4px -2px #000" >\n                            <h3 class="form-label mb-0 required fs-4">Workflow Frequency</h3>\n                            <button type="button" title="Add frequency" class="btn btn-dark btn-icon new-freq-entry"><i class="ki-outline ki-plus-circle"></i></button>\n                        </div>\n                        ${r(e.frequency)}\n                    </div>`},then:e=>{d("EDIT"===e),$sela(".all-pred-workflow").$loop((e=>{e.$on("click",((e,n)=>{e.preventDefault(),$sel(".active-workflow-here")&&$sel(".active-workflow-here").$class("del",...l),n.$sel(".card").$class("add",...l),$nam("template").value=$data(n,"id")}))})),$sel(".new-freq-entry").$on("click",((n,t)=>{t.closest(".all-container").$html("beforeend",r()),d("EDIT"===e)}))}},entry:{row:(e,n)=>{const t=e.isActive?"Active":"Paused",a=e.isActive?"success":"warning";return`<td>${n}</td>\n                    <td>${e.name}</td>\n                    <td>${e.agent}</td>\n                    <td><div class="badge badge-${a}">${t}</div></td>\n                    <td>${e.dateUpdated}</td>\n                    <td>${e.dateCreated}</td>`},anchor:{edit:!1,id:"flowId",name:"name",actionsFn:({info:e})=>[{name:"Edit Workflow",act:"edit"},{...e.isActive?{name:"Pause Workflow",icon:"ki-outline ki-row-vertical",className:"text-warning"}:{name:"Resume Workflow",icon:"ki-outline ki-to-right",className:"text-primary"},act:"changeStatus"}]}},entryActionFn:({loadEntries:e})=>({changeStatus:({name:n,id:t,info:a})=>{let l=a.isActive?"PAUSE":"RESUME";Swal.fire({html:`You are about to <b>${l}</b> this workflow, are you sure this is what you want to do?`,icon:"warning",buttonsStyling:!1,confirmButtonText:"Yeah, do it!",cancelButtonText:"Cancel",customClass:{confirmButton:"btn btn-primary"}}).then((n=>{n.isConfirmed&&$curl(genApi("/ai/workflow/change-status/"+l.toLowerCase()),{preload:$preloader,data:{id:t},headers:apiHeaders}).finally((()=>$preloader("hide"))).then((n=>{osNote(n.message,"success"),e()}))}))}})})}();