simpleTablePage({api:{list:$lay.page.urlFull,delete:"blog/delete",dateRange:$lay.page.urlFull},entry:{row:(e,t)=>{let a="blog/scheduled"===$lay.page.url?`<td>${e.postSchedule}</td>`:"";return`<td>${t}</td>\n                <td>${e.title}</td>\n                <td>${e.author.name}</td>\n                <td>${e.category.name} (${e.collection})</td>\n                ${a}\n                <td>${e.dateCreated}</td>\n                <td>${e.dateUpdated??"-"}</td>`},anchor:{id:"postId",name:"title",edit:!1,actionsFn:({id:e,info:t})=>[{name:"Copy Post URL",act:"copyPost"},{name:"Edit Blog",href:$lay.src.base+"blog/compose/"+e,target:"_blank"}]},filter:e=>({name:`${e.category} (${e.collection})`,value:e.category})},entryAction:{copyPost:({info:e,name:t})=>{$copyToClipboard(e.slug,t+" Url copied to clipboard")}},deleteMsg:e=>`You are about this blog post [${e}], are you sure you want to do that? It will be deleted from the sitemap and rss channel as well`});