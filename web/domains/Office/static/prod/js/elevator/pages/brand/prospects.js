simpleTablePage({api:{list:"business/prospects/list",delete:"business/prospects/delete"},entry:{row:(e,a)=>`<td>${a}</td>\n            <td>${e.name}</td>\n            <td>${e.email}</td>\n            <td>${e.updated_at??e.created_at}</td>`,anchor:{id:"id",name:"name",edit:()=>"",actions:[{name:"View Details",act:"view"}]}},entryAction:{view:({info:e,name:a})=>{let t=`<a href="mailto:${e.email}" class="fs-2 d-block alert alert-info mb-5 p-2 text-center">${e.email}</a>`;$loop(JSON.parse(e.body),(e=>{e.body=e.body.replaceAll("\\'","'").replaceAll("\\n","<br>"),t+=`<details class="mb-5">\n                        <summary class="title fs-3 fw-bold">${e.subject} [${$facades.date(e.date)}]</summary>\n                        <div class="p-3 bg-gray-100 ms-5 mt-2">\n                            <p>${e.body}</p>\n                        </div>\n                    </details>`})),osModal({head:a+" Messages",foot:"",body:t})}}});