!function(){let e,t="";const a=()=>{""!==$name("title")[0].value.trim()&&""!==$name("category")[0].value.trim()&&$debounce((()=>l(!1)),1e3)};$("#summary").maxlength({warningClass:"badge badge-success",limitReachedClass:"badge badge-warning",limitExceededClass:"badge badge-danger",allowOverMax:!0});const n=ckEditor({selector:"#blog-editor",uploadApi:genApi("/blog/compose/upload-file")});function l(e=!0){const t=$sel(".save-changes");n.then((a=>{if(ckEditorContent(a,!1),""===$name("body_content")[0].value)return;const n=$name("id")[0];let l=""===n.value?"new":"edit";e||(l="autosave"),ajax(t,(()=>$curl(genApi("/blog/compose/"+l),{preload:()=>preloadBtn(t),data:t,headers:apiHeaders()}).finally((()=>preloadBtn(t,!1))).then((t=>{const a=()=>{""!==t.data&&(n.value=t.data.postId,s(t.data.canEditSlug))};if(e)return serverResponse(t.code,t.message,a,!1);a()}))))}))}function o(e,t=!0,a=null){let n={};$loop(e,(e=>{n[e.collection]||(n[e.collection]=[]),n[e.collection].push(e)}));let l=t?"<option></option>":"";return $loop(n,((e,t)=>{l+=`<optgroup label="${t} - Collection">`,$loop(e,(e=>{l+=`<option data-section="${e.collection}" value="${e.id}" ${a===e.id?"selected":""} >${e.name} (${e.collection})</option>`})),l+="</optgroup>"})),l}function s(e=!0){if("0"!==$name("can_edit_url")[0].value){if(!e)return $on($name("title")[0],"input",t,"del"),$name("can_edit_url")[0].value="0",void($id("blog-url").disabled=!0);$on($name("title")[0],"input",t)}function t(e){const t=e.target.value.trim();""!==t&&($id("blog-url").value=t.replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").toLowerCase())}}$on($sel(".save-changes"),"click",(e=>{e.preventDefault(),l()})),$sela(".autosave-on-input").$loop((e=>e.$on("input",a))),$sela(".autosave-on-change").$loop((e=>e.$on("change",a))),n.then((e=>(e.model.document.on("change",a),e))),$sel(".delete-article")&&$sela(".delete-article").$loop((e=>{e.$on("click",((e,t)=>{e.preventDefault(),Swal.fire({html:"Are you sure you want to delete this article? It will no longer be accessible and this process is irreversible!",icon:"warning",buttonsStyling:!1,confirmButtonText:"Yeah, do it!",cancelButtonText:"Cancel",customClass:{confirmButton:"btn btn-primary"}}).then((e=>{e.isConfirmed&&$curl(genApi("/blog/delete"),{preload:$preloader,data:{id:t.dataset.id},headers:apiHeaders()}).finally((()=>$preloader("hide"))).then((e=>{setTimeout((()=>$loc.href=$lay.src.base+"blog/"),3e3),osNote(e.message,"success")}))}))}))})),function a(n=!0){const l=$sel(".refresh-category");$curl(genApi("/blog/category/list"),{preload:()=>preloadBtn(l),headers:apiHeaders()}).finally((()=>preloadBtn(l,!1))).then((a=>{0!==a.length&&(t=a,$html($name("category")[0],"in",o(a,!0,$name("category")[0].dataset.value)),e=$("#blog-category").select2())})),n&&$on(l,"click",(e=>{e.preventDefault(),ajax($sel(".refresh-category"),(()=>a(!1)))}))}(!0),function(){const a=$sel(".gen-with-ai");if(!a)return;let l={};a.$on("click",(()=>{const a=o(t,!1,l.category);osModal({head:"Generate a Blog Post",foot:"",closeOnBlur:!1,body:`<form>\n                        ${getAgentSelect(l.agent)}\n                        <div class="selected-agent-box d-none">\n                            <div class="mb-5 text-center bg-info-subtle p-4 d-table mx-auto">\n                                <p class="agent-personality m-0"></p>\n                            </div>\n                        \n                            <div class="mb-10 form-floating">\n                                <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="ai_title" value="${l.title??""}" placeholder="Title" required />\n                                <label for="form-b-name" class="form-label mb-3 required">Title</label>\n                            </div>\n                            \n                            <select name="ai_category" data-index="0" id="agent-blog-category" class="form-select mb-10" required>\n                                <option value="">Select a Blog Category</option>\n                                ${a}\n                            </select>\n                            \n                            <div class="mb-10">\n                                <label for="form-b-keyw" class="form-label mb-3">Target Keywords</label>\n                                <textarea class="form-control form-control-solid" id="form-b-keyw" \n                                    placeholder="Each Keyword on a new line" \n                                    name="keywords" style="height: 200px">${l.keywords??""}</textarea>\n                            </div>\n                            \n                            <div class="mb-10">\n                                <label for="form-b-desc" class="form-label">Prompt</label>\n                                <textarea class="form-control form-control-solid" id="form-b-desc" \n                                    placeholder="Tweak your post even more with fine-grained prompts" \n                                    name="prompt" style="height: 100px">${l.prompt??""}</textarea>\n                            </div>\n                            \n                            <div class="text-center">\n                                <div class="text-center">${$facades.submitBtn("Generate Post","generate-post-now btn btn-outline btn-outline-dark","Generating post...")}</div>\n                            </div>\n                        </div>\n                    </form>`,then:()=>{const t=(e=null)=>{e=e??$sel(".lay-ai-agent").selectedIndex-1;const t=aiAgentCache[e],a=$sel(".selected-agent-box");a.$sel(".agent-personality").$html(t.personality),a.$class("del","d-none")};l.agent&&t(),$sel(".lay-ai-agent").$on("change",((e,a)=>{if(""===a.value)return $sel(".selected-agent-box").$class("add","d-none");t(a.selectedIndex-1)})),$on($sel(".generate-post-now"),"click",((t,a)=>{t.preventDefault(),l={agent:$name("agent")[0].value,title:$name("ai_title")[0].value,category:$name("ai_category")[0].selectedIndex,keywords:$name("keywords")[0].value,prompt:$name("prompt")[0].value},ajax(a,(()=>$curl(genApi("/ai/actions/blog/gen"),{preload:()=>preloadBtn(a),data:a,headers:apiHeaders()}).finally((()=>preloadBtn(a,!1))).then((t=>{$name("meta_content")[0].value=t.meta_description,$name("tags")[0].value=t.tags,n.then((e=>e.setData(t.body_content))),e.val(e.find("option").eq(l.category).val()).trigger("change"),$name("title")[0].value=l.title,$name("title")[0].dispatchEvent(new Event("input")),$name("slug")[0].value=l.title,CusWind.closeBox(),osNote("Post generated successfully, please review it","success")}))))}))}})}))}(),function(){let e=$sel(".blog-collaborators-field"),t=$id("new-author");$curl(genApi("/blog/utils/collaborators"),{headers:apiHeaders()}).then((t=>{if(0===t.length)return;let a="<option></option>";e=e?JSON.parse(e.innerHTML):[],$loop(t,(t=>{let n="";if(e.length>0&&t.id===$name("updated_by")[0].value)return"continue";$loop(e,((a,l)=>{t.id===a&&(n=!0,delete e[l])})),a+=`<option value="${t.id}" ${n?"selected":""}>${t.name}</option>`})),$html($id("blog-collaborators"),"in",a),$("#blog-collaborators").select2()})),$curl(genApi("/blog/utils/writers"),{headers:apiHeaders()}).then((e=>{if(0===e.length)return;let a="<option></option>";$loop(e,(e=>{a+=`<option value="${e.id}" ${e.id===$data(t,"id")?"selected":""}>${e.name}</option>`})),$html(t,"in",a),$("#new-author").select2()}))}(),function(){const e=$id("blog-status-improvement"),t=$id("blog-status-picker"),a=new Date;$("#blog-status-picker").flatpickr({enableTime:!0,dateFormat:"Y-m-d H:i",minDate:a});const n=$id("blog-status-color"),l=$id("blog-status-select"),o=["bg-success","bg-warning","bg-danger","bg-info","bg-gray-400"],s=a=>{switch(n.classList.remove(...o),t.parentNode.classList.add("d-none"),e.parentNode.classList.add("d-none"),a){case"PUB__REVISE_POST":n.classList.add("bg-danger"),e.parentNode.classList.remove("d-none");break;case"PUB__REQUEST_APPROVAL":n.classList.add("bg-gray-400");break;case"PUBLISH":n.classList.add("bg-success");break;case"SCHEDULE":n.classList.add("bg-info"),t.parentNode.classList.remove("d-none");break;case"DRAFT":n.classList.add("bg-warning")}};s(l.value),$(l).on("change",(e=>s(e.target.value)))}(),function(){const e=$name("feat_img")[0];$on($name("img_type"),"change",((t,a)=>{if("link"===a.value)return e.type="url";e.type="file",e.accept="image/*"})),$media({srcElement:$sel(".image-link"),previewElement:$sel(".preview-image")})}(),s(),new Tagify($sel("#blog-tags"),{whitelist:["trending","buyers guide","historical fact","business"],originalInputValueFormat:e=>e.map((e=>e.value)).join(","),dropdown:{maxItems:20,classname:"tagify__inline__suggestions",enabled:0,closeOnSelect:!1},callbacks:{change:e=>{a()}}}),function(){const e=$sel(".toggle-calendar-info");e&&($sel(".hide-calendar").$on("click",(()=>e.click())),e.$on("click",(t=>{t.preventDefault();const a=$sel(".calendar-info",e.closest(".cal-parent"));a.$class("has","d-none")?a.$class("del","d-none"):a.$class("add","d-none"),$sela(".copy-btn",a).$loop((e=>e.$on("click",((e,t)=>{e.preventDefault(),$copyToClipboard(t.closest("details").$sel(".copy-para").innerText)}))))})))}(),$on($win,"beforeunload",(e=>(e.preventDefault(),"Are you sure you want to leave this page?")))}();