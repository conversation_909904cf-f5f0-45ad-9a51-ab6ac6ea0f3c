simpleTablePage({api:{add:"brand/partner/",edit:"brand/partner/update",list:"brand/partner/{page}",delete:"brand/partner/"},form:{head:"Add a New Partner",body:(e={})=>{const a=e.logo??DP_PLACEHOLDER;return`<div class="mb-10 d-flex justify-content-center align-items-center flex-column">\n                    <h5 class="mb-5">Client's Logo</h5>\n                    <div class="text-center">\n                        <div class="image-input image-input-outline" data-kt-image-input="true" style="background-image: url('${a}')">\n                            <div class="image-input-wrapper w-125px h-125px" style="background-image: url(${a})"></div>\n                    \n                            <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" title="Change avatar">\n                                <i class="ki-outline ki-pencil fs-7"></i>\n                                <input type="file" name="logo" data-max-size="3000000" accept=".png, .jpg, .jpeg, .webp" />\n                                <input type="hidden" name="avatar_remove" />\n                            </label>\n                    \n                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title="Cancel avatar">\n                                <i class="ki-outline ki-cross fs-2"></i>\n                            </span>\n                    \n                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" title="Remove avatar">\n                                <i class="ki-outline ki-cross fs-2"></i>\n                            </span>\n                        </div>\n                    \n                        <div class="form-text">Allowed file types: png, jpg, jpeg.</div>\n                    </div>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-name" class="form-control form-control-lg form-control-solid" name="name" value="${e.name??""}" placeholder="Client's Name" required />\n                    <label for="c-name" class="form-label mb-3 required">Name</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-comp" type="url" class="form-control form-control-lg form-control-solid" name="website" placeholder="Project" value="${e.website??""}" />\n                    <label for="c-comp" class="form-label mb-3">Website</label>\n                </div>`},then:()=>KTImageInput.init()},entry:{row:(e,a)=>(e.logo=e.logo??DP_PLACEHOLDER,`<td>${a}</td>\n                <td><img src="${e.logo}" alt="dp" style="max-width: 50px" /></td>\n                <td>${e.name}</td>\n                <td>${e.website}</td>`),anchor:{id:"id",name:"name"}},deleteMsg:e=>`You are about to delete this partner [${e}], are you sure you want to do that?`});