!function(){const e=genApi("blog/utils/"),t={};let n=".dt-dyna-new-row",a=$sel(".save-changes-now");const o=e=>(e.preventDefault(),a.$class("has","d-none")?null:"Are you sure you want to leave this page?");function l(e=!0){e&&!a.$class("has","d-none")||$on($win,"beforeunload",o,e?"on":"del")}const s=(n,a=null)=>{if(t[n])return t[n];$curl(e+(a??n),{catch:!0,headers:apiHeaders()}).then((e=>t[n]=e))},d=(e,n,a=null,o=!0)=>{let l="",s=n.split("[]").length>1;return t[e].$loop((e=>{let t=`<option value="${e.id}">${e.name}</option>`;const n=$type(a);"String"===n&&(t=`<option ${a===e.id?"selected":""} value="${e.id}">${e.name}</option>`),"NodeList"===n&&(t="",a.$loop((n=>{t+=`<option ${$data(n,"id")===e.id?"selected":""} value="${e.id}">${e.name}</option>`}))),l+=t})),`<select name="${n}" data-placeholder="Pick ${s?"Multiple options":"an option"} ${o?"*":""}" class="form-select" data-control="select2" ${s?'data-allow-clear="true" multiple':""} ${o?"required":""}>\n                <option></option>\n                ${l}\n            </select>`};$curl(genApi("/blog/category/list"),{catch:!0,headers:apiHeaders()}).then((e=>t.categories=e)),s("statusTypes","status-types"),s("goals"),s("kpis"),s("postTypes","post-types"),s("intents"),s("social","social-channels"),s("authors","writers"),simpleTablePage({api:{batch:"blog/calendar/bulk",list:"blog/calendar/{page}",delete:"blog/calendar/delete"},entry:{rowAttribute:(e,t)=>$get("entry")===e.calendarId?"style='background: var(--bs-body-color)'":"",row:(e,t)=>{let n="info";switch(e.postStatusId){default:break;case"NOT_STARTED":n="dark";break;case"DRAFT":n="warning";break;case"PUBLISH":n="success";break;case"PUB__REVISE_POST":n="danger"}return`<td style="min-width: 150px">${e.publishDate}</td>\n                    <td>${e.title}</td>\n                    <td>${e.categories.map((e=>"<div class='row-multi-vals' data-id='"+e.id+"'>"+e.name+"</div>")).join("")}</td>\n                    <td>${e.summary}</td>\n                    <td>${e.audience}</td>\n                    <td data-id="${e.authorId}">${e.author??"-"}</td>\n                    <td>${e.goals.map((e=>"<div class='row-multi-vals' data-id='"+e+"'>"+e+"</div>")).join("")}</td>\n                    <td>${e.intents.map((e=>"<div class='row-multi-vals' data-id='"+e+"'>"+e+"</div>")).join("")}</td>\n                    <td>${e.pryKeyword}</td>\n                    <td>${e.secKeywords.map((e=>""+e)).join("\n")}</td>\n                    <td data-id="${e.postTypeId}">${e.postType}</td>\n                    <td data-id="${e.postStatusId}"><div class="badge badge-${n}">${e.postStatus}</div></td>\n                    <td>${e.wordCount}</td>\n                    <td>${e.cta}</td>\n                    <td>${e.internalLinks.map((e=>""+e)).join("\n")}</td>\n                    <td>${e.externalLinks.map((e=>""+e)).join("\n")}</td>\n                    <td>${e.assetsNeeded.map((e=>""+e)).join("\n")}</td>\n                    <td>${e.socialChannels.map((e=>"<div class='row-multi-vals' data-id='"+e+"'>"+e+"</div>")).join("")}</td>\n                    <td>${e.kpis.map((e=>"<div class='row-multi-vals' data-id='"+e+"'>"+e+"</div>")).join("")}</td>\n                    <td>${e.dateCreated}</td>\n                    <td>${e.dateUpdated??"-"}</td>`},anchor:{id:"calendarId",name:"title",edit:!1,delete:!1,actionsFn:({info:e})=>[{name:"Edit Entry",act:"editEntry"},{name:e.publishPost?"Update Post":"Compose Post",href:"blog/compose/"+(e?.publishPost?.id??"?cal_id="+e.calendarId),target:"_blank"},{name:"Assign to Author",act:"assignAuthor"},{separator:!0},{name:"Delete Entry",act:"delete",wrap:!0,className:"btn btn-danger btn-sm"}]}},deleteMsg:e=>`Are you sure you want to delete this calendar entry [${e}]? This cannot be recovered!`,tools:{batch:{csv:"post-calendar.csv",note:"If you have an existing calendar entry with the same title as one in this file, the existing one will be overwritten"},checkboxAction:({data:e,utils:t})=>{Swal.fire({html:"Are you sure you want to delete all the selected calendar entries? This is irreversible!",icon:"warning",buttonsStyling:!1,confirmButtonText:"Yeah, do it!",cancelButtonText:"Cancel",customClass:{confirmButton:"btn btn-primary"}}).then((n=>{n.isConfirmed&&$curl(genApi("/blog/calendar/delete-bulk"),{preload:$preloader,data:{bulk_id:e},headers:apiHeaders}).finally((()=>$preloader("hide"))).then((e=>{t.loadEntries(),t.clearSelection(),osNote(e.message,"success")}))}))},scopedFn:({loadEntries:e})=>{$sel(".new-entry-to-table").$on("click",(e=>{if(e.preventDefault(),$sela(".dt-dyna-edit-row").length>0)return osNote("You have rows being edited! Save or discard your changes before adding a new entry");n=".dt-dyna-new-row";const t=$sela(".dt-dyna-new-row")?.length??0,o=`<tr class="dt-dyna-new-row">\n                            <td> - </td>\n                            <td> <input name="publish_date[${t}]" class="form-control is-date-picker" placeholder="Pick a date *" required> </td>\n                            <td> <input type="text" class="form-control" name="post_title[${t}]" placeholder="Post Title *" required> </td>\n                            <td> ${d("categories",`post_categories[${t}][]`)} </td>\n                            <td> <textarea rows="3" class="form-control" name="post_summary[${t}]" placeholder="Post Summary *" required></textarea> </td>\n                            <td> <input type="text" class="form-control" name="audience[${t}]" placeholder="Audience"> </td>\n                            <td> ${d("authors",`author[${t}]`,null,!1)} </td>\n                            <td> ${d("goals",`goals[${t}][]`)} </td>\n                            <td> ${d("intents",`search_intents[${t}][]`)} </td>\n                            <td> <input type="text" class="form-control" name="pry_keyword[${t}]" placeholder="Primary Keyword *" required> </td>\n                            <td> <textarea rows="4" class="form-control" name="sec_keywords[${t}]" placeholder="Secondary keywords. New line per keyword"></textarea> </td>\n                            <td> ${d("postTypes",`post_type[${t}]`)} </td>\n                            <td> ${d("statusTypes",`status[${t}]`,"NOT_STARTED")} </td>\n                            <td> <input type="number" class="form-control" name="word_count[${t}]" placeholder="Word Count *" required> </td>\n                            <td> <input type="text" class="form-control" name="cta[${t}]" placeholder="Call to Action" required> </td>\n                            <td> <textarea rows="4" class="form-control" name="internal_links[${t}]" placeholder="Internal Links. New line per link"></textarea> </td>\n                            <td> <textarea rows="4" class="form-control" name="external_links[${t}]" placeholder="External Links. New line per link"></textarea> </td>\n                            <td> <textarea rows="4" class="form-control" name="assets_needed[${t}]" placeholder="Assets the post needs. New line per asset"></textarea> </td>\n                            <td> ${d("social",`social_channels[${t}][]`)} </td>\n                            <td> ${d("kpis",`kpis[${t}][]`)} </td>\n                            <td> - </td>\n                            <td> - </td>\n                            <td class="text-end">\n                                <button class="btn btn-icon btn-danger btn-active-light-danger w-30px h-30px delete-row-now" data-go="0" title="Delete Row">\n                                    <i class="ki-outline ki-trash fs-3"></i>\n                                </button>\n                            </td>\n                        </tr>`,s=$sel(".entry-table-body");s.$sel(".dt-no-data-found")?s.$html("in",o):0!==$sela(".dt-dyna-new-row").length?$sela(".dt-dyna-new-row")[t-1].$html("afterend",o):s.$html("afterbegin",o),$("[data-control=select2]").select2(),$(".is-date-picker").flatpickr({enableTime:!1,dateFormat:"Y-m-d"}),l(),a.$class("del","d-none"),$sel(".delete-row-now",$sel(".dt-dyna-new-row")).$on("click",((e,t)=>{if(e.preventDefault(),"0"===$data(t,"go"))return t.$class("del","btn-danger","btn-active-light-danger"),t.$class("add","btn-warning","btn-active-light-warning"),$data(t,"go","1"),osNote("Click again to delete row!","dark",{onClose:()=>{$data(t,"go","0"),t.$class("add","btn-danger","btn-active-light-danger"),t.$class("del","btn-warning","btn-active-light-warning")}});t.closest("tr").remove(),0===$sela(".dt-dyna-new-row").length&&(a.$class("add","d-none"),l(!1))}))})),$sel(".commit-changes",a).$on("click",(t=>{t.preventDefault(),function(e,t,n=!1){$curl(genApi(n?"blog/calendar/edit":"blog/calendar"),{headers:apiHeaders(),data:e.object,preload:$preloader}).finally((()=>$preloader("hide"))).then((e=>{osNote(e.message,"success"),t(),a.$class("add","d-none"),l(!1)}))}($getForm($sel(n).parentElement,{inForm:!1}),e,".dt-dyna-edit-row"===n)})),$sel(".cancel-changes",a).$on("click",(e=>{e.preventDefault(),cMsg("Are you sure you want discard all changes?",(()=>{$sela(n).$loop((e=>e.remove())),$sela(".editing-it-now").$loop((e=>e.$class("del","d-none","editing-it-now"))),a.$class("add","d-none"),l(!1)}))})),$sel(".new-entry-via-ai").$on("click",(t=>{t.preventDefault(),osModal({head:"Generate Post Calendar With AI",foot:"",body:`<form>\n                                ${getAgentSelect()}\n                                \n                                <div class="selected-agent-box d-none">\n                                    <div class="mb-5 text-center bg-info-subtle p-4 d-table mx-auto">\n                                        <p class="agent-personality m-0"></p>\n                                    </div>\n                                    \n                                    <div class="mb-10 form-floating">\n                                        <input id="form-b-2name" class="form-control form-control-lg form-control-solid" name="month" placeholder="Month" required />\n                                        <label for="form-b-2name" class="form-label mb-3 required">Month (June)</label>\n                                    </div>\n                                    \n                                    <div class="mb-10 form-floating">\n                                        <input type="number" id="form-b-3name" class="form-control form-control-lg form-control-solid" name="year" placeholder="Year" required />\n                                        <label for="form-b-3name" class="form-label mb-3 required">Year (2025)</label>\n                                    </div>\n                                    \n                                    <div class="mb-10 form-floating">\n                                        <input type="number" id="form-b-6name" class="form-control form-control-lg form-control-solid" name="posts_per_week" placeholder="Year" required />\n                                        <label for="form-b-6name" class="form-label mb-3 required">Posts Per Week</label>\n                                    </div>\n                                    \n                                    <div class="mb-10 form-floating">\n                                        <input type="number" id="form-b-4name" class="form-control form-control-lg form-control-solid" name="posts_per_day" placeholder="Year" required />\n                                        <label for="form-b-4name" class="form-label mb-3 required">Posts Per Day</label>\n                                    </div>\n                                    \n                                    <div class="mb-10 form-floating">\n                                        <input id="form-b-5name" class="form-control form-control-lg form-control-solid" name="days_of_week" placeholder="Year" required />\n                                        <label for="form-b-5name" class="form-label mb-3 required">Days of Week (Mon, Tue)</label>\n                                    </div>\n                                    \n                                    <div class="mb-10 text-center">\n                                        <label class="form-switch form-switch-lg">\n                                            <input class="form-check-input" type="checkbox" name="consider_holidays" checked value="true" >\n                                            <span class="form-check-label">Create Posts on Holidays (e.g Christmas)</span>\n                                        </label>\n                                    </div>\n                                </div>\n                                \n                                <div class="agent-response-box"></div>\n                                \n                                <div class="text-center mt-4">${$facades.submitBtn("Generate Calendar","generate-post-now btn btn-outline btn-outline-black","Generating Your Calendar...")}\n                                </div>\n                            </form>`,then:()=>{const t=$sel(".selected-agent-box");$sel(".lay-ai-agent").$on("change",((e,n)=>{if(""===n.value)return t.$class("add","d-none");const a=n.selectedIndex-1,o=aiAgentCache[a];t.$sel(".agent-personality").$html(o.personality),t.$class("del","d-none")})),$on(".generate-post-now","click",((n,a)=>{n.preventDefault(),ajax(a,(()=>$curl(genApi("/ai/actions/blog-calendar/gen"),{preload:()=>preloadBtn(a),data:a,headers:apiHeaders()}).finally((()=>preloadBtn(a,!1))).then((n=>{const o=new EventSource(genApi("/ai/actions/blog-calendar/gen"));preloadBtn(a),t.$class("add","d-none"),o.onerror=e=>{o.close(),preloadBtn(a,!1),t.$class("del","d-none"),osNote("Connection closed due to an error: ","fail",{duration:"pin"})},o.addEventListener("chunked",(e=>{const t=JSON.parse(e.data).message;$sel(".agent-response-box").$html("afterbegin",`<h2 class="title fw-semibold text-info">${t}</h2>`)})),o.addEventListener("complete",(t=>{osNote("Request complete!","success"),preloadBtn(a,!1),e(),CusWind.closeBox(),o.close()}))}))))}))}})}))}},entryActionFn:({loadEntries:e})=>({editEntry:({id:e,info:t,name:o,parentElement:s})=>{if($sela(".dt-dyna-new-row").length>0)return osNote("You have unsaved changes! Save or discard your changes before editing an entry");n=".dt-dyna-edit-row";const r=s.closest("tr");r.$class("add","editing-it-now","d-none");const i=$sela(".dt-dyna-edit-row")?.length??0,c=r.$sela("td");r.$html("beforebegin",`<tr class="dt-dyna-edit-row">\n                        <td> ${c[0].$html()} <input type="hidden" name="calendar_id[${i}]" value="${e}"> </td>\n                        <td> <input name="publish_date[${i}]" class="form-control is-date-picker" placeholder="Pick a date *" required value="${c[1].$html()}"> </td>\n                        <td> <input type="text" class="form-control" name="post_title[${i}]" placeholder="Post Title *" required value="${c[2].$html()}"> </td>\n                        <td> ${d("categories",`post_categories[${i}][]`,c[3].$sela(".row-multi-vals"))} </td>\n                        <td> <textarea rows="3" class="form-control" name="post_summary[${i}]" placeholder="Post Summary *" required>${c[4].$html()}</textarea> </td>\n                        <td> <input type="text" class="form-control" name="audience[${i}]" placeholder="Audience" value="${c[5].$html()}"> </td>\n                        <td> ${d("authors",`author[${i}]`,c[6].$data("id"),!1)} </td>\n                        <td> ${d("goals",`goals[${i}][]`,c[7].$sela(".row-multi-vals"))} </td>\n                        <td> ${d("intents",`search_intents[${i}][]`,c[8].$sela(".row-multi-vals"))} </td>\n                        <td> <input type="text" class="form-control" name="pry_keyword[${i}]" placeholder="Primary Keyword *" required value="${c[9].$html()}"> </td>\n                        <td> <textarea rows="4" class="form-control" name="sec_keywords[${i}]" placeholder="Secondary keywords. New line per keyword">${c[10].$html()}</textarea> </td>\n                        <td> ${d("postTypes",`post_type[${i}]`,c[11].$data("id"))} </td>\n                        <td> ${d("statusTypes",`status[${i}]`,c[12].$data("id"))} </td>\n                        <td> <input type="number" class="form-control" name="word_count[${i}]" placeholder="Word Count *" value="${c[13].$html()}" required> </td>\n                        <td> <input type="text" class="form-control" name="cta[${i}]" placeholder="Call to Action" required value="${c[14].$html()}"> </td>\n                        <td> <textarea rows="4" class="form-control" name="internal_links[${i}]" placeholder="Internal Links. New line per link">${c[15].$html()}</textarea> </td>\n                        <td> <textarea rows="4" class="form-control" name="external_links[${i}]" placeholder="External Links. New line per link">${c[16].$html()}</textarea> </td>\n                        <td> <textarea rows="4" class="form-control" name="assets_needed[${i}]" placeholder="Assets the post needs. New line per asset">${c[17].$html()}</textarea> </td>\n                        <td> ${d("social",`social_channels[${i}][]`,c[18].$sela(".row-multi-vals"))} </td>\n                        <td> ${d("kpis",`kpis[${i}][]`,c[19].$sela(".row-multi-vals"))} </td>\n                        <td> ${c[20].$html()} </td>\n                        <td> ${c[21].$html()} </td>\n                        <td class="text-end">\n                            <button class="btn btn-icon btn-danger btn-active-light-danger w-30px h-30px cancel-change-row-now" data-go="0" title="Discard Changes">\n                                <i class="ki-outline ki-cross fs-3"></i>\n                            </button>\n                        </td>\n                    </tr>`),$("[data-control=select2]").select2(),$(".is-date-picker").flatpickr({enableTime:!1,dateFormat:"Y-m-d"}),l(),a.$class("del","d-none"),$sel(".cancel-change-row-now",r.previousElementSibling).$on("click",((e,t)=>{if(e.preventDefault(),"0"===$data(t,"go"))return t.$class("del","btn-danger","btn-active-light-danger"),t.$class("add","btn-warning","btn-active-light-warning"),$data(t,"go","1"),osNote("Click again to discard changes to row!","dark",{onClose:()=>{$data(t,"go","0"),t.$class("add","btn-danger","btn-active-light-danger"),t.$class("del","btn-warning","btn-active-light-warning")}});t.closest("tr").remove(),r.$class("del","d-none","editing-it-now"),0===$sela(".dt-dyna-edit-row").length&&(a.$class("add","d-none"),l(!1))}))},assignAuthor:({id:t,name:n,info:a})=>{osModal({head:`Assign Post [${n}] To A Writer`,foot:"",body:`<form>\n                            <div class="mb-5">\n                                <label for="c-title" class="form-label mb-3 required">Writers</label>\n                                ${d("authors","author",a.authorId)}\n                            </div>\n                            <div class="mb-10 text-center">\n                                <label class="form-switch form-switch-lg">\n                                    <input class="form-check-input" type="checkbox" name="notify_author" checked value="true" >\n                                    <span class="form-check-label">Notify Author</span>\n                                </label>\n                            </div>\n                            \n                            <div class="text-center">\n                                <input type="hidden" name="id" value="${t}">\n                                <div class="text-center">${$facades.submitBtn()}</div>\n                            </div>\n                        </form>`,then:()=>{$("[data-control=select2]").select2({dropdownParent:CusWind.get.wrap}),$on($sel(".submit-form"),"click",((n,a)=>{n.preventDefault();const o=$getForm(a,!0).object;o.id=t,ajax(a,(()=>$curl(genApi("blog/calendar/assign-author"),{preload:()=>preloadBtn(a),data:o,headers:apiHeaders()}).finally((()=>preloadBtn(a,!1))).then((t=>serverResponse(t.code,t.message,e,!1)))))}))}})}})})}();