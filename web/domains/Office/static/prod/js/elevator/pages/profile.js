!function(){const e=quillEditor("about-editor","Write about your self...");$sela(".save-changes").forEach((a=>{$on(a,"click",((a,t)=>{if(a.preventDefault(),$name("about")[0].value=quillEditorContent(e),$exceeds($sel(".avatar-dp"),2e6))return osNote("Your image size is too large, try something less than 2mb","warn");ajax(t,(()=>$curl(genApi("/user/update-profile"),{preload:()=>preloadBtn(t),data:t,headers:apiHeaders()}).finally((()=>preloadBtn(t,!1))).then((e=>{setTimeout((()=>$loc.reload()),3e3),osNote(e.message,"success")}))))}))}))}();