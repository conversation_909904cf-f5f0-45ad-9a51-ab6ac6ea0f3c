!function(){const e=$sel(".nothing-found"),t=$sel(".response-table");$on($sel(".search-far-wide"),"keyup",(s=>{const n=s.target.value;n.length<3?0===n.length&&($class(e,"del","d-none"),$class(t,"add","d-none")):$debounce((()=>function(s){$curl(genApi("/blog/search?query="+encodeURI(s)),{headers:apiHeaders()}).then((s=>{if(0===s.length)return;let n="";$loop(s,(e=>{let t="success";switch(e.postStatus){default:t="primary";break;case"REVISION":t="danger";break;case"DRAFT":t="warning";break;case"SCHEDULE":t="info";break;case"PUBLISH":t="success"}n+=`<tr>\n                            <td class="text-start">\n                                <span class="badge py-3 px-4 fs-7 badge-light-${t}">${e.postStatusName}</span>\n                            </td>\n                            <td class="ps-0 text-start">\n                                <span class="text-gray-800 fw-bold fs-6 d-block blog-title">${e.title}</span>\n                            </td>\n                            <td class="text-center">\n                                <span class="text-gray-400 fw-bold fs-6 d-block">${e.author}</span>\n                            </td>\n                            <td class="text-end">\n                                <div class="d-flex">\n                                    <a href="${websiteUrl}blog/${e.slug}?mode=preview" class="btn btn-sm btn-icon btn-primary w-50px h-50px me-3">\n                                        <i class="ki-outline ki-eye fs-2 text-light"></i>\n                                    </a>\n                                    <a href="${baseUrl}blog/compose/${e.postId}" class="btn btn-sm btn-icon btn-warning w-50px h-50px me-3">\n                                        <i class="ki-outline ki-pencil fs-2 text-light"></i>\n                                    </a>\n                                    <a href="javascript:void(0)" data-id="${e.postId}" class="btn btn-sm btn-icon btn-bg-light btn-danger w-50px h-50px delete-blog">\n                                        <i class="ki-outline ki-trash fs-2 text-light"></i>\n                                    </a>\n                                </div>\n                            </td>\n                        </tr>`})),$html($sel(".response-body"),"in",n),$sela(".delete-blog").$loop((e=>{e.$on("click",((e,t)=>{e.preventDefault();const s=t.closest("tr");Swal.fire({html:`Are you sure you want to delete <b>${s.$sel(".blog-title").$html()}</b>? This process is irreversible!`,icon:"warning",buttonsStyling:!1,confirmButtonText:"Yeah, do it!",cancelButtonText:"Cancel",customClass:{confirmButton:"btn btn-primary"}}).then((e=>{e.isConfirmed&&$curl(genApi("/blog/delete"),{preload:$preloader,data:{id:t.dataset.id},headers:apiHeaders()}).finally((()=>$preloader("hide"))).then((e=>{s.remove(),osNote(e.message,"success")}))}))}))})),$class(e,"add","d-none"),$class(t,"del","d-none")}))}(n)),500,"search-far-wide-query")}))}();