let aiAgentCache=[];const getAiAgents=(e=!1)=>{("compose"!==$lay.page.routeArray[1]||$sel(".gen-with-ai")&&(0===aiAgentCache.length||e))&&$curl(genApi("/ai/agent/list/1"),{catch:!0}).then((e=>aiAgentCache=e))},getAgentSelect=(e=null)=>{let t="";return aiAgentCache.$loop((n=>t+=`<option ${e===n.id?"selected":""} value="${n.id}">${n.name}</option>`)),`<select name="agent" class="form-select lay-ai-agent" required>\n            <option value="">Select an AI Agent</option>\n            ${t}\n        </select>`};getAiAgents();