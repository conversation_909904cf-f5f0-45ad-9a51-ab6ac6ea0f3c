!function(){const e=genApi("/system/");let s=[],n=[];const i=async()=>$curl(e+"roles/list",{headers:apiHeaders()}).then((e=>n=e)),t=(e=null,s=!1)=>{$name("role")[0].$on("change",((i,t)=>{const l=$data(t.options[t.selectedIndex],"index");null!==l&&($sel(".role-resting-place").$html("replace",permissionTemplate({permissions:n[l].permissions,disableSelected:!0,disableAll:s,xPermissions:e?e.xPermissions:null})),selectAllPermissions())}))},l=(e=null)=>{let s="";return 0===n.length&&i(),$loop(n,((n,i)=>s+=`<option ${e===n.roleId?"selected":""} value="${n.roleId}" data-index="${i}">${n.name}</option>`)),`<select name="role" class="form-select" required><option value="">Select a Role</option>${s}</select>`},a=async()=>$curl(e+"user-position/list",{headers:apiHeaders()}).then((e=>s=e)),o=(e=null)=>{let n="";return 0===s.length&&a(),$loop(s,(s=>n+=`<option ${e===s.id?"selected":""} value="${s.id}">${s.name}</option>`)),`<select name="position" class="form-select" required><option value="">Select a Position</option>${n}</select>`};i(),a(),simpleTablePage({api:{add:"system/users/new",list:"system/all-but-me",delete:"system/users/delete"},form:{head:"Invite User To Dashboard",body:()=>`<div class="mb-10 form-floating">\n                    <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="first_name" placeholder="Name" required />\n                    <label for="form-b-name" class="form-label mb-3 required">First Name</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="last_name" placeholder="Name" required />\n                    <label for="form-b-name" class="form-label mb-3 required">Last Name</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="form-b-email" class="form-control form-control-lg form-control-solid" type="email" name="email" placeholder="Email" required />\n                    <label for="form-b-email" class="form-label mb-3 required">Email</label>\n                </div>\n                <div class="mb-10">\n                    <label for="c-post" class="form-label mb-3 required">Staff Position</label>\n                    ${o()}                            \n                </div>\n                <div class="mb-10">\n                    <div class="d-flex align-items-center mb-3">\n                        <label for="c-title" class="form-label mb-3 required">Admin Role</label>\n                        \n                        <a href="system/roles" target="_blank" class="btn btn-light-primary btn-sm ms-auto me-3" title="Add a new category">\n                            <i class="ki-outline ki-plus fs-2 p-0"></i>\n                        </a>\n                        <button type="button" class="btn btn-light-facebook btn-sm refresh-sys-roles">\n                            <span class="indicator-label"><i class="ki-outline ki-arrows-circle fs-2"></i></span>\n                            <span class="indicator-progress">\n                                Please wait...\n                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>\n                            </span>\n                        </button>\n                    </div>\n                    ${l()}                            \n                </div>\n                ${permissionTemplate({disableAll:!0})}`,then:()=>{((e=!1)=>{$sel(".refresh-sys-roles").$on("click",(s=>{s.preventDefault(),i().then((s=>{$name("role")[0].$html("replace",l()),t({},e)}))}))})(!0),selectAllPermissions(),t({},!0)}},deleteMsg:e=>`You are about to delete user [${e}], This process is irreversible!`,entry:{row:(e,s)=>{const n=e.isActive?'<i class="ki-outline ki-emoji-happy fs-1"></i> Active':'<i class="ki-outline ki-time fs-1"></i> Dormant',i=e.isActive?"success":"info";return`<td>${s}</td>\n                    <td>${e.name}</td>\n                    <td>${e.email}</td>\n                    <td>${e.position}</td>\n                    <td><div class="btn btn-${i}">${n}</div></td>\n                    <td>${e.role}</td>`},anchor:{edit:!1,delete:!1,id:"userId",name:"admin",actionsFn:({info:e})=>{const s=e.isActive?{}:{name:"Resend Registration Request",act:"resendRegister"};return[{name:"Send an Email",href:`mailto:${e.email}`},{name:"Change System Role",act:"role"},{name:"Special Permission",act:"permission"},{name:"Change Staff Position",act:"staffPost"},s,{separator:!0},{name:"Terminate Account",act:"delete",wrap:!0,className:"btn btn-danger btn-sm"}]}}},entryActionFn:({loadEntries:s})=>{const n=n=>$on($sel(".submit-form"),"click",((i,t)=>{i.preventDefault(),ajax(t,(()=>$curl(e+"users/update-data/"+n,{preload:()=>preloadBtn(t),data:t,headers:apiHeaders()}).finally((()=>preloadBtn(t,!1))).then((e=>serverResponse(e.code,e.message,s,!1)))))}));return{role:({name:e,id:s,info:i})=>{osModal({head:"Change Role For "+e,foot:"",closeOnBlur:!1,size:"md",body:`<form>\n                                <div class="mb-10">\n                                    <label for="c-title" class="form-label mb-3 required">Role</label>\n                                    ${l(i.roleId)}                            \n                                </div>\n                                <div class="text-center">\n                                    <input type="hidden" name="id" value="${s}">\n                                    <div class="text-center">${$facades.submitBtn()}</div>\n                                </div>\n                            </form>`,then:()=>n(s)})},staffPost:({name:e,id:s,info:i})=>{osModal({head:"Change Staff Position For "+e,foot:"",closeOnBlur:!1,size:"md",body:`<form>\n                            <div class="mb-10">\n                                <label for="c-title" class="form-label mb-3 required">Position</label>\n                                ${o(i.positionId)}                            \n                            </div>\n                            <div class="text-center">\n                                <input type="hidden" name="id" value="${s}">\n                                <div class="text-center">${$facades.submitBtn()}</div>\n                            </div>\n                        </form>`,then:()=>n(s)})},permission:({name:e,id:s,info:i})=>{osModal({head:"Assign Special Permissions To "+e,foot:"",closeOnBlur:!1,size:"lg",body:`<form>\n                            <div class="mb-10 p-3">${permissionTemplate({permissions:i.permissions,xPermissions:i.xPermissions,disableSelected:!0,isSuperAdmin:i.isSuper})}\n                                <input type="hidden" name="modules_set" value="true">                            \n                            </div>\n                            <div class="text-center">\n                                <input type="hidden" name="id" value="${s}">\n                                <div class="text-center">${$facades.submitBtn()}</div>\n                            </div>\n                        </form>`,then:()=>n(s)})},resendRegister:({name:s,id:n})=>{Swal.fire({text:`Are you sure you want to resend the registration link to ${s}?, \n                    This will resend the registration email with a new password, rendering the old one void! \n                    Is this what you want to do?`,icon:"warning",buttonsStyling:!1,confirmButtonText:"Yeah, do it!",cancelButtonText:"Cancel",customClass:{confirmButton:"btn btn-primary"}}).then((s=>{s.isConfirmed&&$curl(e+"users/resend-reg",{preload:$preloader,data:{id:n},headers:apiHeaders()}).finally((()=>$preloader("hide"))).then((e=>osNote(e.message,"success")))}))}}}})}();