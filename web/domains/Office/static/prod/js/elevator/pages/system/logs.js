simpleTablePage({api:{list:"system/logs",dateRange:"system/logs"},entry:{row:l=>`<td>${l.date}</td>\n            <td>${l.message}</td>\n            <td>${l.user}</td>`,anchor:{id:"logId",name:"logId",edit:!1,actions:[{name:"More Info",act:"view"}]}},entryAction:{view:({name:l,info:o})=>{const n=o.dataset;osModal({head:"More Info on "+l,foot:"",size:"lg",body:`<div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${o.date}" readonly />\n                        <label class="form-label mb-3">Date</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${n.activity}" readonly />\n                        <label class="form-label mb-3">Activity</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${n.route}" readonly />\n                        <label class="form-label mb-3">Route</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${n.referer}" readonly />\n                        <label class="form-label mb-3">Referrer URL</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <textarea class="form-control form-control-solid" id="form-b-desc" name="desc" style="height: 200px">${o.message}</textarea>\n                        <label class="form-label mb-3">Message</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${o.user}" readonly />\n                        <label class="form-label mb-3">User</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${o.userId}" readonly />\n                        <label class="form-label mb-3">User ID</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${n.ip}" readonly />\n                        <label class="form-label mb-3">User IP</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${n.device.platform}" readonly />\n                        <label class="form-label mb-3">Device</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${n.device.browser}" readonly />\n                        <label class="form-label mb-3">Browser</label>\n                    </div>\n                    <div class="mb-10 form-floating">\n                        <input class="form-control form-control-lg form-control-solid" value="${n.device.agent}" readonly />\n                        <label class="form-label mb-3">Agent</label>\n                    </div>\n                    \n                    \n                    `})}},enableDelete:!1});