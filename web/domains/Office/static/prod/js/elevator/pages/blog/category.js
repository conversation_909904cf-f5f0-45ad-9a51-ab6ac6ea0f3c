!function(){let e=[];$curl(genApi("/blog/collection/list"),{headers:apiHeaders()}).then((o=>e=o)),simpleTablePage({api:{add:"blog/category/new",edit:"blog/category/edit",list:"blog/category/list",delete:"blog/category/delete"},form:{head:"Add New Blog Category",body:(o={})=>`<div class="mb-10 form-floating">\n                    <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="name" value="${o.name??""}" placeholder="Category Name" required />\n                    <label for="form-b-name" class="form-label mb-3 required">Name</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    ${((o=null)=>{let l="";return $loop(e,(e=>l+=`<option ${o===e.id?"selected":""} value="${e.id}">${e.name}</option>`)),`<select name="collection" id="form-e-section" class="form-select form-control form-control-lg form-control-solid" required><option value="">Select a Collection</option>${l}</select>`})(o.collectionId)}\n                    <label for="form-e-section" class="form-label mb-3 required">Collection</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <textarea class="form-control form-control-solid" placeholder="Category Description" id="form-b-desc" name="description" style="height: 200px" required>${o.desc??""}</textarea>\n                    <label for="form-b-desc" class="form-label required">Description</label>\n                </div>`},entry:{row:(e,o)=>`<td>${o}</td>\n                <td>${e.name}</td>\n                <td>${e.collection}</td>\n                <td>${e.desc}</td>`,anchor:{id:"id",name:"name"}},deleteMsg:e=>`Are you sure you want to delete [${e}] category? If it has been used in other blog posts, it will remain there, but you won't find it in subsequent lists`})}();