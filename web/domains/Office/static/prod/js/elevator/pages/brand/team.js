simpleTablePage({api:{add:"brand/team/",batch:"brand/team/bulk",edit:"brand/team/update",list:"brand/team/{page}",delete:"brand/team/"},form:{head:"Add a New Team Member",body:(e={})=>{const a=e.dp??DP_PLACEHOLDER;return`<div class="mb-10 d-flex justify-content-center align-items-center flex-column">\n                    <h5 class="mb-5">Team Member's DP</h5>\n                    <div class="text-center">\n                        <div class="image-input image-input-outline" data-kt-image-input="true" style="background-image: url('${a}')">\n                            <div class="image-input-wrapper w-125px h-125px" style="background-image: url(${a})"></div>\n                    \n                            <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" title="Change avatar">\n                                <i class="ki-outline ki-pencil fs-7"></i>\n                                <input type="file" name="dp" data-max-size="3000000" accept=".png, .jpg, .jpeg, .webp" />\n                                <input type="hidden" name="avatar_remove" />\n                            </label>\n                    \n                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title="Cancel avatar">\n                                <i class="ki-outline ki-cross fs-2"></i>\n                            </span>\n                    \n                            <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="remove" data-bs-toggle="tooltip" title="Remove avatar">\n                                <i class="ki-outline ki-cross fs-2"></i>\n                            </span>\n                        </div>\n                    \n                        <div class="form-text">Allowed file types: png, jpg, jpeg.</div>\n                    </div>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-name" class="form-control form-control-lg form-control-solid" name="name" value="${e.name??""}" placeholder="Client's Name" required />\n                    <label for="c-name" class="form-label mb-3 required">Name</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-post" class="form-control form-control-lg form-control-solid" name="position" value="${e.position??""}" placeholder="Client's Position" required />\n                    <label for="c-post" class="form-label mb-3 required">Position</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <textarea class="form-control form-control-solid" placeholder="about" id="form-b-int" name="intro" style="height: 100px">${e.intro??""}</textarea>\n                    <label for="form-b-int" class="form-label">Short Intro</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <textarea class="form-control form-control-solid" placeholder="about" id="form-b-desc" name="about" style="height: 200px">${e.about??""}</textarea>\n                    <label for="form-b-desc" class="form-label">About</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-lk" type="url" class="form-control form-control-lg form-control-solid" name="social[linkedin]" value="${e.social?.linkedin??""}" placeholder="LinkedIn" />\n                    <label for="c-lk" class="form-label mb-3">LinkedIn Link</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-fb" type="url" class="form-control form-control-lg form-control-solid" name="social[instagram]" value="${e.social?.instagram??""}" placeholder="Instagram" />\n                    <label for="c-fb" class="form-label mb-3">Instagram Link</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="c-tw" type="url" class="form-control form-control-lg form-control-solid" name="social[x]" value="${e.social?.x??""}" placeholder="Twitter" />\n                    <label for="c-tw" class="form-label mb-3">Twitter (X) Link</label>\n                </div>`},then:()=>KTImageInput.init()},batch:{csv:"teams.csv",note:"Please note that if you have any existing team member on the csv file, their existing information will be overwritten on the database."},entry:{row:(e,a)=>(e.dp=e.dp??DP_PLACEHOLDER,`<td>${a}</td>\n                <td><img src="${e.dp}" alt="dp" style="max-width: 50px" /></td>\n                <td>${e.name}</td>\n                <td>${e.position}</td>\n                <td>${e.intro}</td>`),anchor:{id:"id",name:"name",edit:!1,delete:!1,actionsFn:({info:e})=>[{name:"Edit Member",act:"edit"},{name:"Change Order",act:"updateOrder"},{separator:!0},{name:"Delete Member Profile",act:"delete",wrap:!0,className:"btn btn-danger btn-sm"}]}},entryActionFn:({loadEntries:e})=>({updateOrder:({name:a,id:n,info:t})=>{osModal({head:"Change Order of "+a,foot:"",closeOnBlur:!1,size:"md",body:`<form>\n                            <div class="mb-10">\n                                <label for="c-title" class="form-label mb-3 required">Current Order</label>\n                                <h3>${t.order}</h3>                                \n                            </div>\n                            <div class="mb-10 form-floating">\n                                <input id="form-b-name" type="number" class="form-control form-control-lg form-control-solid" name="order" placeholder="order" required />\n                                <label for="form-b-name" class="form-label mb-3 required">New Order</label>\n                            </div>\n                            <div class="text-center">\n                                <input type="hidden" name="id" value="${n}">\n                                <div class="text-center">${$facades.submitBtn()}</div>\n                            </div>\n                        </form>`,then:()=>{$on($sel(".submit-form"),"click",((a,n)=>{a.preventDefault(),ajax(n,(()=>$curl(genApi("brand/team/change-order"),{preload:()=>preloadBtn(n),data:n,headers:apiHeaders(),method:"PUT"}).finally((()=>preloadBtn(n,!1))).then((a=>serverResponse(a.code,a.message,e,!1)))))}))}})}}),deleteMsg:e=>`You are about to delete [${e}], are you sure you want to do that?`});