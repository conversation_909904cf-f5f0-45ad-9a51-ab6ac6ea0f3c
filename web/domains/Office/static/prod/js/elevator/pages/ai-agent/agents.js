!function(){let e=[],n=[];const a=async()=>$curl(genApi("/system/roles/list"),{headers:apiHeaders()}).then((e=>n=e)),l=(e=null)=>{$name("role")[0].$on("change",((a,l)=>{const t=$data(l.options[l.selectedIndex],"index");null!==t&&($sel(".role-resting-place").$html("replace",permissionTemplate({permissions:n[t].permissions,disableSelected:!0,xPermissions:e?e.xPermissions:null})),selectAllPermissions())}))},t=()=>{$sel(".refresh-sys-roles").$on("click",(e=>{e.preventDefault(),a().then((e=>{$name("role")[0].$html("replace",o()),l()}))}))},s=()=>{$sel(".auth-cred-select-field").$on("change",((e,n)=>{const a=n.value,l=n.nextElementSibling,t=l.$sel(".new-auth-cred");if("create-new"!==a)return t&&t.remove(),void n.$attr("name","credential");t||(n.$attr("name","del"),l.$html("beforeend",'<div class="new-auth-cred">\n                    <input class="form-control form-control-lg form-control-solid" name="credential" placeholder="New Credential" required />\n                    <input type="hidden" name="credential_new" value="true" />\n                </div>'))}))},r=async()=>$curl(genApi("/ai/auth/list"),{headers:apiHeaders()}).then((n=>e=n)),i=(n=null)=>{let a="";return 0===e.length&&r(),$loop(e,(e=>a+=`<option ${n===e.authId?"selected":""} value="${e.authId}">${e.name}</option>`)),`<select name="credential" class="form-select auth-cred-select-field" required>\n                <option value="">Select a System Credential</option>\n                <option value="create-new">Create A New One</option>\n                ${a}\n            </select>\n            <div class="new-cred-div">\n                <p class="text-muted text-center pt-2">This is the credential we will use agent to know this agent internally</p>\n            </div>`},o=(e=null)=>{let l="";return 0===n.length&&a(),$loop(n,((n,a)=>l+=`<option ${e===n.roleId?"selected":""} value="${n.roleId}" data-index="${a}">${n.name}</option>`)),`<select name="role" class="form-select" required><option value="">Select a Role</option>${l}</select>`};simpleTablePage({api:{add:"ai/agent/new",list:"ai/agent/list/{page}",delete:"ai/agent/delete"},form:{head:"AI Agent",body:()=>`<div class="mb-10">\n                    <label class="form-label required mb-0">\n                        System Credential\n                    </label>\n                    ${i()}                            \n                </div>\n                <div class="mb-10 form-floating">\n                    <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="name" placeholder="Name" required />\n                    <label for="form-b-name" class="form-label mb-3 required">Agent Name</label>\n                </div>\n                <div class="mb-10 form-floating">\n                    <textarea class="form-control form-control-solid" placeholder="" id="form-b-desc" name="personality" style="height: 80px" required></textarea>\n                    <label for="form-b-desc" class="form-label required">Agent Personality</label>\n                </div>\n                 <div class="mb-10">\n                     <div class="d-flex align-items-center mb-3">\n                        <label for="c-title" class="form-label mb-3 required">Agent Role</label>\n                        \n                        <a href="system/roles" target="_blank" class="btn btn-light-primary btn-sm ms-auto me-3" title="Add a new role">\n                            <i class="ki-outline ki-plus fs-2 p-0"></i>\n                        </a>\n                        <button type="button" class="btn btn-light-facebook btn-sm refresh-sys-roles">\n                            <span class="indicator-label"><i class="ki-outline ki-arrows-circle fs-2"></i></span>\n                            <span class="indicator-progress">\n                                Please wait...\n                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>\n                            </span>\n                        </button>\n                    </div>\n                    \n                    ${o()}                            \n                </div>\n                ${permissionTemplate({})}\n                `,then:()=>{s(),t(),selectAllPermissions(),l()}},deleteMsg:e=>`You are about to delete AI Agent [${e}], This process is irreversible!`,entry:{row:(e,n)=>`<td>${n}</td>\n                    <td>${e.name}</td>\n                    <td>${e.personality}</td>\n                    <td>${e.auth}</td>\n                    <td>${e.date}</td>`,anchor:{edit:!1,delete:!1,id:"agentId",name:"name",actionsFn:({info:e})=>[{name:"Change Credentials",act:"updateCred"},{name:"Update Name",act:"updateName"},{name:"Update Personality",act:"updatePersonality"},{name:"Modify Role & Permissions",act:"permission"},{separator:!0},{name:"Terminate Agent",act:"delete",wrap:!0,className:"btn btn-danger btn-sm"}]},response:{prefetch:()=>{r(),a()}}},entryActionFn:({loadEntries:e})=>({permission:({name:n,id:a,info:s})=>{osModal({head:"Modify Agent: "+n+" Role & Permissions",foot:"",closeOnBlur:!1,size:"lg",body:`<form>\n                                <div class="mb-10">\n                                     <div class="d-flex align-items-center mb-3">\n                                        <label for="c-title" class="form-label mb-3 required">Agent Role</label>\n                                        \n                                        <a href="system/roles" target="_blank" class="btn btn-light-primary btn-sm ms-auto me-3" title="Add a new role">\n                                            <i class="ki-outline ki-plus fs-2 p-0"></i>\n                                        </a>\n                                        <button type="submit" class="btn btn-light-facebook btn-sm refresh-sys-roles">\n                                            <span class="indicator-label"><i class="ki-outline ki-arrows-circle fs-2"></i></span>\n                                            <span class="indicator-progress">\n                                                Please wait...\n                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>\n                                            </span>\n                                        </button>\n                                    </div>\n                                    \n                                    ${o(s.roleId)}                            \n                                </div>\n                \n                                <div class="mb-10 p-3">${permissionTemplate({permissions:s.permissions,xPermissions:s.xPermissions,disableSelected:!0})}                            \n                                </div>\n                                <div class="text-center">\n                                    <input type="hidden" name="id" value="${a}">\n                                    <div class="text-center">${$facades.submitBtn()}</div>\n                                </div>\n                            </form>`,then:()=>{selectAllPermissions(),l(),t(),$on($sel(".submit-form"),"click",((n,l)=>{n.preventDefault(),ajax(l,(()=>$curl(genApi("/ai/agent/update-perm/"+a),{preload:()=>preloadBtn(l),data:l,headers:apiHeaders()}).finally((()=>preloadBtn(l,!1))).then((n=>serverResponse(n.code,n.message,e,!1)))))}))}})},updateCred:({name:n,id:a,info:l})=>{osModal({head:"Update Credentials For Agent: "+n,foot:"",closeOnBlur:!1,size:"lg",body:`<form>\n                                <div class="mb-10">\n                                    <label class="form-label required mb-0">\n                                        System Credential\n                                    </label>\n                                    ${i(l.authId)}                            \n                                </div>\n                                <div class="text-center">\n                                    <input type="hidden" name="id" value="${a}">\n                                    <div class="text-center">${$facades.submitBtn()}</div>\n                                </div>\n                            </form>`,then:()=>{s(),$on($sel(".submit-form"),"click",((n,l)=>{n.preventDefault(),ajax(l,(()=>$curl(genApi("/ai/agent/update-credential/"+a),{preload:()=>preloadBtn(l),data:l,headers:apiHeaders()}).finally((()=>preloadBtn(l,!1))).then((n=>serverResponse(n.code,n.message,e,!1)))))}))}})},updateName:({name:n,id:a,info:l})=>{osModal({head:"Modify Agent: "+n+" Name",foot:"",closeOnBlur:!1,size:"lg",body:`<form>\n                                <div class="mb-10 form-floating">\n                                    <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="name" value="${l.name}" placeholder="Name" required />\n                                    <label for="form-b-name" class="form-label mb-3 required">Name</label>\n                                </div>\n                                <div class="text-center">\n                                    <input type="hidden" name="id" value="${a}">\n                                    <div class="text-center">${$facades.submitBtn()}</div>\n                                </div>\n                            </form>`,then:()=>$on($sel(".submit-form"),"click",((n,l)=>{n.preventDefault(),ajax(l,(()=>$curl(genApi("/ai/agent/update-name/"+a),{preload:()=>preloadBtn(l),data:l,headers:apiHeaders()}).finally((()=>preloadBtn(l,!1))).then((n=>serverResponse(n.code,n.message,e,!1)))))}))})},updatePersonality:({name:n,id:a,info:l})=>{osModal({head:"Modify Agent: "+n+" Personality",foot:"",closeOnBlur:!1,size:"lg",body:`<form>\n                                <div class="mb-10 form-floating">\n                                    <textarea class="form-control form-control-solid" placeholder="" id="form-b-desc" name="personality" style="height: 80px" required>${l.personality??""}</textarea>\n                                    <label for="form-b-desc" class="form-label required">Personality</label>\n                                </div>\n                                <div class="text-center">\n                                    <input type="hidden" name="id" value="${a}">\n                                    <div class="text-center">${$facades.submitBtn()}</div>\n                                </div>\n                            </form>`,then:()=>$on($sel(".submit-form"),"click",((n,l)=>{n.preventDefault(),ajax(l,(()=>$curl(genApi("/ai/agent/update-personality/"+a),{preload:()=>preloadBtn(l),data:l,headers:apiHeaders()}).finally((()=>preloadBtn(l,!1))).then((n=>serverResponse(n.code,n.message,e,!1)))))}))})}})})}();