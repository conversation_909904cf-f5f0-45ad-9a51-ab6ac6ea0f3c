simpleTablePage({api:{add:"system/roles/new",edit:"system/roles/edit",list:"system/roles/list"},form:{head:"Add New User Roles",body:(e={})=>`<div class="mb-10 form-floating">\n                <input id="form-b-name" class="form-control form-control-lg form-control-solid" name="name" value="${e.name??""}" placeholder="Name" required />\n                <label for="form-b-name" class="form-label mb-3 required">Role Name</label>\n            </div>\n            <div class="mb-10 form-floating">\n                <input class="form-control form-control-solid" id="form-b-desc" name="note" value="${e.note??""}" >\n                <label for="form-b-desc" class="form-label">Role Note</label>\n            </div>\n            \n            ${permissionTemplate({permissions:e.permissions,isSuperAdmin:e.isSuper})}`,then:()=>selectAllPermissions()},entry:{row:(e,o)=>`<td>${o}</td>\n            <td>${e.name} </td>\n            <td>${e.note??"No description provided"}</td>\n            <td>${e.user}</td>`,anchor:{id:"roleId",name:"name"}},enableDelete:!1});