<?php
use <PERSON><PERSON><PERSON>er\Lay\Libs\Cron\LayCron;
use BrickLayer\Lay\Core\Exception;
use BrickLayer\Lay\Core\LayConfig;

const SAFE_TO_INIT_LAY = true;

include_once __DIR__ . DIRECTORY_SEPARATOR . ".." . DIRECTORY_SEPARATOR . ".." . DIRECTORY_SEPARATOR . ".." . DIRECTORY_SEPARATOR . "foundation.php";

include_once "foundation.php";

$main_branch = "main";

// Replace [PRIMARY_DOMAIN] with your actual primary domain. 
// Create a subdomain entry on your dns. 
// Finally, paste the link below to github or your CI/CD platform
// https://gitad.[PRIMARY_DOMAIN]/0c5728c1d8fc99b82a630b28402487e0 
// You can decide to create an environment variable since leaving the uuid like this in the project is risky.
// Generate a new UUID from your DB or echo this function Gen::uuid(); and user the value in your .env
// Then you can use LayFn::env("MY_ENV")

// Verify webhook from GitHub
if(!isset($_SERVER['REQUEST_METHOD']))
    Exception::throw_exception("Wrong mode of contact", "GitADMismatched");

if($_SERVER['REQUEST_METHOD'] !== 'POST' or @$_GET['brick'] !== "0c5728c1d8fc99b82a630b28402487e0")
    Exception::throw_exception("Invalid endpoint met! please check your uuid and try again", "GitADMismatched");

$post = json_decode($_POST['payload'] ?? null);

if(!isset($post->pull_request)) {
    x_hook_logger($post?->action?->zen);
    return;
}

if($post->pull_request->state != "closed") {
    x_hook_logger("Pull Request: " . $post->pull_request->state);
    return;
}

$log = "-- Stash Old Changes: " . shell_exec("git stash 2>&1 &") . " \n";
$log .= "-- Git Checkout: " . shell_exec("git checkout $main_branch 2>&1 &") . "\n";
$log .= "-- Submodule Init: " . shell_exec("git submodule init 2>&1 &") . " \n";
$log .= "-- Submodule Reset: " . shell_exec('git submodule foreach --recursive  "git fetch origin && git reset --hard" 2>&1 &') . " \n";
$log .= "-- Submodule Pull: " . shell_exec('git pull --recurse-submodules 2>&1 &') . "\n";
$log .= "-- Git Fetch: " . shell_exec('git fetch --all 2>&1 &') . "\n";
$log .= "-- Git Reset: " . shell_exec("git reset --hard origin/$main_branch 2>&1 &") . "\n";

$log .= "\n";
$log .= "--++ Pre Invalidate Hooks Actions\n";

pre_invalidate_hooks($log);

$log .= "\n";
$log .= "-- Invalidating Hooks\n";
(new \Web\Api\Plaster())->invalidate_hooks();

$log .= "\n";
$log .= "--++ Post Invalidate Hooks Actions\n";
post_invalidate_hooks($log);

$log .= "\n";
$log .= "-- Symlinks are being refreshed\n";

$bob = LayConfig::server_data()->root . "bob";
$log .= "-- Link Refresh: " . shell_exec("php $bob link:refresh 2>&1 &") . "\n";

// push composer deployment for later execution to avoid 504 (timeout error)
$log .= "-- Cronjob: " . LayCron::new()
    ->job_id("update-composer-pkgs")
    ->every_minute()
    ->just_once()
    ->new_job("bob up_composer")['msg']
;

x_hook_logger($log);
print "Done!";