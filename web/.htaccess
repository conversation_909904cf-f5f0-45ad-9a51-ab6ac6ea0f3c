ServerSignature Off

# Disable directory browsing
Options -Indexes

<IfModule mod_deflate.c>
  # Compress HTML, CSS, JavaScript, Text, XML and fonts
  AddOutputFilterByType DEFLATE image/svg+xml
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
  AddOutputFilterByType DEFLATE application/x-font
  AddOutputFilterByType DEFLATE application/x-font-opentype
  AddOutputFilterByType DEFLATE application/x-font-otf
  AddOutputFilterByType DEFLATE application/x-font-truetype
  AddOutputFilterByType DEFLATE application/x-font-ttf
  AddOutputFilterByType DEFLATE application/x-javascript
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE font/opentype
  AddOutputFilterByType DEFLATE font/otf
  AddOutputFilterByType DEFLATE font/ttf
  AddOutputFilterByType DEFLATE image/x-icon
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/javascript
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/xml
</IfModule>

<IfModule mod_headers.c>
  # Font Packages CORS Issues resolution
  <FilesMatch "\.(ttf|ttc|otf|eot|woff|woff2|font.css|css)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Cache-Control "max-age=31536000, public"
    Header unset Pragma
  </FilesMatch>

  # Cache static assets for 1 year
  <FilesMatch "\.(js|css|jpg|png|webp|jpeg|)$">
    Header set Cache-Control "max-age=31536000, public"
    Header unset Pragma
  </FilesMatch>

  # Hide PHP X-powered header
  Header unset X-Powered-By
  Header always unset X-Powered-By
  Header unset Server
  Header always unset Server
</IfModule>

<IfModule mod_expires.c>
  ExpiresActive On

 # Images
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType image/x-icon "access plus 1 year"

  # Video
  ExpiresByType video/webm "access plus 1 year"
  ExpiresByType video/mp4 "access plus 1 year"
  ExpiresByType video/mpeg "access plus 1 year"

  # Fonts
  ExpiresByType font/ttf "access plus 1 year"
  ExpiresByType font/otf "access plus 1 year"
  ExpiresByType font/woff "access plus 1 year"
  ExpiresByType font/woff2 "access plus 1 year"
  ExpiresByType application/font-woff "access plus 1 year"
  ExpiresByType application/font-woff2 "access plus 1 year"

  # CSS, JavaScript
  ExpiresByType text/css "access plus 1 year"
  ExpiresByType text/javascript "access plus 1 year"
  ExpiresByType application/javascript "access plus 1 year"

  # Others
  ExpiresByType application/pdf "access plus 1 year"
  ExpiresByType image/vnd.microsoft.icon "access plus 1 year"
</IfModule>

Options +FollowSymLinks -MultiViews
RewriteEngine On

# Deny access to htaccess
<Files .htaccess>
RewriteRule '' / [R=301,L]
Order allow,deny
Deny from all
</Files>

# Deny access to filenames starting with dot(.)
<FilesMatch "^\.">
RewriteRule '' / [R=301,L]
Order allow,deny
Deny from all
</FilesMatch>

# Deny access to .git
RewriteRule ^\.git - [R=403,L]

# Deny access to foundation.php
RewriteRule ^foundation.php web/$1 [L,QSA]

# Deny access to files with outlined extensions
<FilesMatch "\.(env|ini|psd|log|sh|sql|view|inc|lock|lenv)$">
RewriteRule '' / [R=301,L]
Order allow,deny
Deny from all
</FilesMatch>

# Deny access to specified files
<FilesMatch "(composer|package|package-lock)\.json">
RewriteRule '' / [R=301,L]
Order allow,deny
Deny from all
</FilesMatch>

## Force website to redirect to www.domain.tld
# Make condition active on production server
RewriteCond %{REMOTE_HOST} !127.0.0.1
RewriteCond %{REMOTE_HOST} !^192.168
RewriteCond %{REMOTE_HOST} !::1
# Make condition active if not a subdomain already
RewriteCond %{HTTP_HOST} !^([^.]+)\.([^.]+)\.([^.]+)
RewriteRule ^(.*)$ https://www.%{HTTP_HOST}/$1 [R=301,L]

# Force open with HTTPS on production server
RewriteCond %{REMOTE_HOST} !127.0.0.1
RewriteCond %{REMOTE_HOST} !^192.168
RewriteCond %{REMOTE_HOST} !::1
RewriteCond %{SERVER_PORT} 80
# Check if the request has already been forwarded to https to avoid rewrite loop
RewriteCond %{HTTP:X-Forwarded-Proto} !https
# Finally forward to https
RewriteRule ^(.*)$ https://%{HTTP_HOST}/$1 [R=301,L]

# Allow any files or directories like [jpg,css,js,etc] that exist to be displayed correctly
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d [OR]
RewriteCond %{REQUEST_FILENAME} -l
RewriteRule ^ - [L]

# [HIDE_.PHP_EXTENSION]
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^ %{REQUEST_URI}.php [L]

# Default file for handling any other request uri
RewriteRule ^(.*)$ index.php?brick=%{REQUEST_URI} [QSA]
RewriteRule ^index\.php/?brick=$ /$1 [L,R=301,NC]
