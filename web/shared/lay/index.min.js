/**!
 * @fileOverview OsAi Minified JS Syntax (OMJ$)
 * <AUTHOR>
 * @version 2.0.0
 * @since 23/11/2019
 * @modified 13/02/2025
 * @license
 * Copyright (c) 2019 Osai LLC | osaitech.dev/about.
 *
 */
"use strict";const $win=window,$doc=document,$obj=Object,$web=navigator,$loc=$win.location;let $store;try{$store=$win.localStorage}catch(e){}const $isInt=e=>isNaN(e)?e:parseInt(e),$end=e=>e[e.length-1],$omjsError=(e,t,o=!1,...n)=>{if(console.info("%cLayJsError: "+e,"color: #e00; font-weight: 600; font-size: 16px"),console.info("%c"+t,"background: #fff3cd; color: #1d2124; padding: 2px; margin-bottom: 3px",...n),console.trace("LayJsTrace"),o)throw"LayJsError Thrown"},$omjsElSub=(e,t)=>("String"===$type(e)&&(e=$sel(e)),null===e&&$omjsError(t,"You are trying to access a null element",!0),e),$sela=(e,t=$doc)=>{try{return t.querySelectorAll(e)}catch(t){$omjsError("$sela","Selector: ["+e+"] is invalid.\nDetails: "+t,!0)}},$sel=(e,t=$doc)=>$sela(e,t)[0],$id=(e,t=$doc)=>$sela("#"+e,t)[0],$name=(e,t=$doc)=>{try{return t.getElementsByName(e)}catch(e){$omjsError("$name",e,!0)}},$field=(e,t=$doc)=>$name(e,t)[0],$on=(e,t,o,...n)=>{e=$omjsElSub(e,"$on");const r=n[0]??"on",a=e.length&&!("select-one"===e.type||"select-multiple"===e.type);try{const i=(i,s)=>{let l=t=>o(t,a?e[s]:e,s,...n);if("on"===r){let e=t.split(",");return e.length>1?$loop(e,(e=>i["on"+e]=l)):i["on"+t]=l}if("remove"===r||"del"===r)return i.removeEventListener(t,l,!1),void(i["on"+t]=()=>null);let d=t.split(",");if(d.length>1)return $loop(d,(e=>i.addEventListener(e,l,r)));i.addEventListener(t,l,r)};if(a)return $loop(e,((e,t)=>i(e,t)));i(e)}catch(e){$omjsError("$on",e,!0)}},$set=e=>$on($doc,"DOMContentLoaded",e),$load=e=>$on($win,"load",e),$attr=(e,t,o=null)=>{e=$omjsElSub(e,"$attr");try{return o?"remove"===o||"del"===o?e.removeAttribute(t):e.setAttribute(t,o):e.getAttribute(t)}catch(e){$omjsError("$attr",e,!0)}},$data=(e,t,o)=>(e=$omjsElSub(e,"$data"),o?$attr(e,"data-"+t,o):$attr(e,"data-"+t)),$class=(e,t=null,...o)=>{if(e=$omjsElSub(e,"$class"),!t)return e.classList;if("contains"===t||"contain"===t||"has"===t)return e.classList.contains(o);if("index"===t||"key"===t){let t=0;return $loop($sela("."+e.classList.toString().replace(" ",".")),((o,n)=>{if(o===e)return t=n,"break"})),t}return"add"===t?(e.classList.add(...o),e):"remove"===t||"del"===t?(e.classList.remove(...o),e):(e.classList.toggle(o,t),e)},$style=(e,t=null,o=null)=>{e=$omjsElSub(e,"$style");try{return"css"===t?$win.getComputedStyle(e,o):null!==t?$attr(e,"style",t):e.style}catch(e){$omjsError("$style",e,!0,"%cThe selected element doesn't exist","color:#e0a800")}},$html=(e,t=null,o=null)=>{if(e=$omjsElSub(e,"$html"),t&&!o&&"in"!==t&&"del"!==t&&"move"!==t&&"wrap"!==t&&"replace"!==t&&(o=t,t="in"),!t)return e.innerHTML;if("inner"!==t&&"in"!==t)if("del"!==t&&"remove"!==t)if("move"!==t)if("wrap"!==t)if("replace"!==t)try{return e.insertAdjacentHTML(t,o)}catch(e){$omjsError("$html",e,!0)}else try{const t=Math.floor(1e6*Math.random());$sel("body").insertAdjacentHTML("beforeend",`<div style="display: none" data-omjs-t-${t}="t">${o}</div>`);const n=$sel(`[data-omjs-t-${t}=t]`),r=e.replaceWith(n.childNodes[0]);return n.remove(),r}catch(e){$omjsError("$html",e,!0,"Failed when trying to replace one element with another")}else try{return e.parentNode.insertBefore($doc.createElement(o),e),e.previousElementSibling.appendChild(e).parentElement}catch(e){$omjsError("$html",e,!0,"%cEnsure the first parameter is a valid node\nEnsure a valid tag name was supplied to the third parameter` === Parent Element receiving `param 1`","color: #e0a800")}else try{return o.appendChild(e)}catch(e){$omjsError("$html",e,!0,"%cEnsure `param 1` === Element being moved to `param 3`\nEnsure `param 3` === Parent Element receiving `param 1`","color:#e0a800")}else try{return e.innerHTML=""}catch(e){$omjsError("$html",e,!0,"%cThe selected element doesn't exist","color:#e0a800")}else try{return e.innerHTML=o}catch(e){$omjsError("$html",e,!0,"%cThe selected element doesn't exist","color:#e0a800")}},$type=(e,t=!0)=>{let o=$obj.prototype.toString.call(e).replace("[object ","").replace("]","");return!1===t&&(console.log("%cOMJ$ VIEW: $type","background: #fff3cd; color: #1d2124; padding: 5px"),console.info(e),console.log("%cObject Type: "+o,"background: #14242f; color: #fffffa; padding: 5px;")),o},$loop=(e,t=(()=>null))=>{let o={length:0,first:{key:"",value:""},last:{key:"",value:""}};const n=t.infinite??!1,r=t.then??t;let a="";for(let t in e){if(!e.hasOwnProperty(t))continue;i=t,t=isNaN(i)?i:parseInt(i),o.length++,o.last.key=t,o.last.value=e[t],1===o.length&&(o.first.key=t,o.first.value=e[t]);const s=r(e[t],t,a);if("continue"!==s){if("break"===s)break;o.output=s??null,a=o.output,o.outputType=$type(o.output),!1===n&&o.length>999&&$omjsError("$loop","Infinite loop detected, process was ended prematurely to save resources. Please pass `infinite: true` if you intend for the loop to go beyond '1000' iterations",!0)}}var i;return o};Object.defineProperties(HTMLElement.prototype,{$sela:{value:function(e){return $sela(e,this)}},$sel:{value:function(e){return $sel(e,this)}},$html:{value:function(e=null,t=null){return $html(this,e,t)}},$on:{value:function(e,t,o={}){return $on(this,e,t,o)}},$attr:{value:function(e=null,t=null){return $attr(this,e,t)}},$data:{value:function(e=null,t=null){return $data(this,e,t)}},$class:{value:function(e=null,...t){return $class(this,e,...t)}},$style:{value:function(e=null,t=null){return $style(this,e,t)}},$type:{value:function(e=!1){return $type(this,e)}}}),$loop([NodeList.prototype,HTMLCollection.prototype,Array.prototype,Object.prototype],(e=>{Object.defineProperties(e,{$loop:{value:function(e){return $loop(this,e)}}})}));
/**!
 * @fileOverview Helpful Plugins Developed With OMJ$
 * <AUTHOR> Aigbogun
 * @version 2.0.3
 * @since 23/11/2019
 * @modified 09/01/2022
 * @license Copyright (c) 2019 Osai LLC | loshq.net/about.
 */
const $in=(e,t=$doc,o="down")=>{if("parent"===o||"top"===o){if(parent===$doc)return!1;try{if("String"===$type(t))return e.closest(t);let o=e.parentNode;for(;o&&o!==$sel("body");){if(o===t)return o;o=o.parentElement}}catch(e){$omjsError("$in",e,!0)}return!1}return t.contains(e)},$get=(e,t=!0)=>{if(t)return new URLSearchParams($loc.search).get(e);let o=$loc.origin,n=$loc.pathname,r=$end(n.split("/")),a=$loc.hash?$loc.hash.split("#")[1]:"",i=o+n;switch(n=n.replace("/"+r,""),e){case"origin":return o;case"path":case"directory":return n;case"file":case"script":return r;case"hash":return a;default:return i}},$ucFirst=e=>{let t="";return e.split(" ").forEach((e=>{let o=e.charAt(0);t+=" "+e.replace(o,o.toUpperCase())})),t.trim()},$mirror=(e,...t)=>{$on(e,"input",(()=>{t.forEach((t=>{void 0===e.value?t.value=e.innerHTML:t.value=e.value}))}))},$img2blob=e=>{const t=$doc.createElement("canvas"),o=t.getContext("2d");return t.width=e.width,t.height=e.height,o.drawImage(e,0,0),t.toDataURL("image/png")},$exceeds=(e,t)=>{if("file"===e.type)return 0!==e.files.length&&e.files[0].size>t},$media=({srcElement:e,previewElement:t,then:o=null,on:n="change",useReader:r=!0,useOn:a=!0})=>{const i=t.src;$id("lay-media-previewer-loader")||$sel("head").$html("beforeend",'<style id="lay-media-previewer-loader">.lay-media-previewer-loader{font-size: 1rem; position: absolute; left: 0; right: 0; top: 5px; bottom: 5px; background: rgba(0,0,0,.5); margin: auto; width: 150px; display: flex;justify-content: center;align-items: center;font-weight: bold;color: #fff;letter-spacing: .3rem; border-radius: 15px; animation: pulse 2s infinite linear; transition: .6s ease-in-out</style>');let s=null,l=!1,d=n=>{if((n=n??e)?.multiple)return osNote("Media preview doesn't support preview for multiple files");const a=s??n.files[0],c=t.parentElement;if(c.$sel(".lay-media-previewer-loader")?c.$sel(".lay-media-previewer-loader").style.display="flex":(c.style.position="relative",c.$html("afterbegin",'<div class="lay-media-previewer-loader">Loading...</div>')),"image/heic"===a?.type&&!l)return(async e=>{const t=async()=>{s=await heic2any({blob:e,toType:"image/jpeg",quality:.8}),l=!0,d()};if($id("lay-heic-converter"))t();else{const e=$doc.createElement("script");e.src="https://cdn.jsdelivr.net/npm/heic2any@0.0.4/dist/heic2any.min.js",e.id="lay-heic-converter",e.onerror=()=>console.log("An error occured while trying to add heic2any previewer"),e.onload=()=>t(),$doc.head.appendChild(e)}})(a);if("file"===n.type){if(r){const e=new FileReader;if($on(e,"load",(()=>{if(c.$sel(".lay-media-previewer-loader").style.display="none",""===n.value)return t.src=i;t.src=e.result,o&&o(a,e.result)}),"on"),a)return e.readAsDataURL(a);t.src=i}if(c.$sel(".lay-media-previewer-loader").style.display="none",""===n.value)return t.src=i;const e=URL.createObjectURL(a);t.src=e,o&&o(a,e)}else c.$sel(".lay-media-previewer-loader").style.display="none",t.src=""!==n.value?n.value:i};return n&&""!==n?"Array"===$type(e)?$loop(e,(e=>$on(e,n,(()=>d(e)),a?"on":"add"))):$on(e,n,(()=>d(e)),a?"on":""):d(e)},$mediaPreview=(e,t,o={})=>$media({srcElement:e,previewElement:t,useReader:1===o.type,then:o.operation??o.then??null,on:o.event??"change",useOn:o.useOn??!0}),$showPassword=(e=(e=>e),t=!0)=>{$sela(".lay-show-password").$loop((o=>{const n="HTMLInputElement"===$type(o),r=$data(o,"event")??null;o.$on(r??(n?"change":"click"),(()=>{let n=$data(o,"field");n&&n.split(",").$loop((n=>{const r=$sel(n);if(!r){if(!t)return;return $omjsError("$showPassword","Selector ["+n+"] does not exist as declared by $showPassword element",!1,o)}r.type="password"===r.type?"text":"password",e&&e(r.type,o)}))}),"on")}))},$rand=(e,t,o=0,n=!0)=>{let r;return e=Math.ceil(e),t=Math.floor(t),1===o?(r=Math.floor(Math.random()*(t-e))+e,!1===n&&console.log("Rand (x => r < y):",r),r):(r=Math.floor(Math.random()*(t-e+1))+e,!1===n&&console.log("Rand (x => r <= y):",r),r)},$view=e=>{let t=(e=$omjsElSub(e,"$view")).getBoundingClientRect(),o=t.top,n=t.left,r=t.right,a=t.bottom,i=$win.innerHeight,s=$win.innerWidth,l=!0;return(o<0&&a<0||o>i&&a>i||n<0&&r<0||n>s&&r>s)&&(l=!1),{top:o,left:n,bottom:a,right:r,inView:l}},$hasFocus=e=>{let t=$doc.activeElement;return!!$in(t,e,"top")||t===e},$overflow=e=>e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth,$check=(e,t)=>{if("String"!==$type(e))return!1;switch(t){case"name":return!!new RegExp("^[a-z ,.'-]+/i$",e);case"username":return!!new RegExp("^w+$",e);case"mail":return/^([a-zA-Z0-9_.\-+])+@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(e);default:return!0}},$cookie=(e="*",t=null,o=null,n="/",r="")=>{if("*"===e)return $doc.cookie.split(";");if(e=e.trim(),n&&(n="Path="+n+";"),r&&(r="Domain="+r+";"),"del"===t)return t=$cookie(e),$doc.cookie=`${e}=${t}; Expires=Thu, 01 Jan 1970 00:00:00 UTC;${n}${r}`;if(t){const a=new Date,i=new Date(a),s=(e=30)=>i.setDate(a.getDate()+e);return"Number"===$type(o)&&(o=s(o)),o=o??new Date(s()).toUTCString(),$doc.cookie=`${e}=${t};Expires=${o};${n}${r}"`}let a=e+"=";return(t=$doc.cookie.split(";").filter((e=>e.includes(a)))).length?(t[0]=t[0].trim(),t[0].substring(a.length,t[0].length)):""},$form=(e,t={})=>{let o=t.message??"Please fill all required fields!";const n=t.inForm??!0;"FORM"!==e.nodeName&&(e=n?e.closest("FORM").elements:$sela("input, select, textarea",e)),$id("osai-form-error")||$sel("head").$html("beforeend",'<style id="osai-form-error">.osai-form-error{background: #e66507 none padding-box !important; color: #ffff !important;}.osai-form-error::placeholder{color: #ffff !important;}</style>');let r=()=>{$sela("input[data-osai-tested='true']").forEach((e=>{$data(e,"osai-tested","del")}))},a=(e,t=o)=>($id("osai-form-error-notify")||osNote(t,"danger",{id:"osai-form-error-notify"}),setTimeout((()=>{e.$class("add","osai-form-error"),e.focus()}),100),$on(e,"input,change",(()=>e.$class("del","osai-form-error")),"addEvent"),r(),!1),i=!0;for(let t=0;t<e.length;t++){let n=e[t],r=n.name&&n.required&&!1===n.disabled;if(!r||""!==n.value.trim()&&void 0!==n.value&&null!==n.value){if(r&&"email"===n.type&&!$check(n.value,"mail"))i=a(n,"Invalid email format, should be <div style='font-weight: bold; text-align: center'>\"[A-Za-Z_.-]@[A-Za-Z.-].[A-Za-Z_.-].[A-Za-Z]\"</div>");else if(r&&"file"===n.type&&$data(n,"max-size")){let e=parseFloat($data(n,"max-size"));$loop(n.files,(t=>{if(t.size<e)return"continue";const o=e/1e6;e=(o+"").toLocaleString(void 0,{minimumFractionDigits:2}),e=o>1?e+"mb":e+"bytes",name=n.name.replaceAll("_"," ").replaceAll("-"," "),i=a(n,`File cannot exceed max size limit of ${e}, please check ${name} and update it`)}))}else if(r&&("radio"===n.type||"checkbox"===n.type)&&!$data(n,"osai-tested")){let e=0;$name(n.name).forEach((t=>{$data(t,"osai-tested","true"),1!==e&&t.checked&&(e=1)})),0===e&&(i=a(n,"Please select the required number of options from the required checklist"))}}else i=a(n,o)}return r(),i},$getForm=(e,t=!1,o={})=>{let n="",r={},a=!1,i=!0;"Object"===$type(t)&&(t=(o=t).validate??!0);let s=o.inForm??!0,l=o.message??"Please fill all required fields!";$id("osai-form-error")||$sel("head").$html("beforeend",'<style id="osai-form-error">.osai-form-error{background: #e66507 none padding-box !important; color: #ffff !important;}.osai-form-error::placeholder{color: #ffff !important;}</style>');let d=()=>{if(e)return"FORM"===e.nodeName?e:e.closest("FORM")},c=()=>{$sela("input[data-osai-tested='true']").$loop((e=>{$data(e,"osai-tested","del")}))},p=(e,t=l)=>{$id("osai-form-error-notify")||osNote(t,"danger",{id:"osai-form-error-notify"}),setTimeout((()=>{e.$class("add","osai-form-error"),e.checkVisibility()&&!$sel(".osai-form-error-focused")&&(e.$class("add","osai-form-error-focused"),e.focus())}),100),$on(e,"input,change",(()=>{e.$class("del","osai-form-error"),e.$class("del","osai-form-error-focused")}),"addEvent"),c(),i=!1},u=e=>{if(t){if(""===e.value.trim()||void 0===e.value||null===e.value)return e.required?p(e,l):void 0;if("email"===e.type&&!$check(e.value,"mail"))return p(e,"Invalid email format, should be like: <b><EMAIL></b>");if(e.required&&("radio"===e.type||"checkbox"===e.type)&&!$data(e,"osai-tested")){let t=0;if($name(e.name).$loop((e=>{if($data(e,"osai-tested","true"),1===t)return"continue";e.checked&&(t=1)})),0===t)return p(e,"Please select the required number of options from the required checklist")}}},f=(e,t)=>{if(n+=encodeURIComponent(e)+"="+encodeURIComponent(t)+"&","]"===$end(e)){let o=e.replace("[]","");r[o]||(r[o]=[]),r[o].push(t)}else r[e]=t};const b=s?d().elements:$sela("input, select, textarea",e);let g;for(let e=0;e<b.length;e++){let o=b[e];if(o.name&&"file"===o.type&&!1===o.disabled){if(t&&o.required&&0===o.files.length){p(o,"File "+(o.dataset.name??o.name)+" is required. But no file was uploaded");continue}if(0===o.files.length)continue;if(a=!0,$data(o,"max-size")){let e=parseFloat($data(o,"max-size"));$loop(o.files,(t=>{if(t.size<e)return"continue";const n=e/1e6;e=(n+"").toLocaleString(void 0,{minimumFractionDigits:2}),e=n>1?e+"mb":e+"bytes";const r=o.name.replaceAll("_"," ").replaceAll("-"," ");p(o,`File cannot exceed max size limit of ${e}, please check ${r} and update it`)}))}}if(o.name&&!o.disabled&&"file"!==o.type&&"reset"!==o.type&&"submit"!==o.type&&"button"!==o.type)if(u(o),"select-multiple"!==o.type)if("checkbox"!==o.type&&"radio"!==o.type)f(o.name,o.value);else{let e=$name(o.name);if(g===e)continue;if(g=e,e.length<2){o.checked&&""!==o.value?f(o.name,o.value):f(o.name,o.checked);continue}$loop(e,(e=>e.disabled?"continue":"radio"===o.type&&e.checked?(f(o.name,""!==e.value?e.value:e.checked),"break"):void("checkbox"===o.type&&e.checked&&f(o.name,""!==e.value?e.value:e.checked))))}else $loop(o.options,(e=>{e.selected&&f(o.name,e.value)}))}if(t&&!i)throw c(),Error("Your form has not satisfied all required validation!");return{string:n.slice(0,-1),object:r,file:s?new FormData(d()):null,hasFile:a}},$drag=(e,t)=>{let o=0,n=0,r=0,a=0;function i(t){let i=t.touches?t.touches[0]:t;r=i.clientX,a=i.clientY,$on($doc,"mouseup,touchend",(()=>{$doc.onmouseup=null,$doc.onmousemove=null,$doc.ontouchend=null,$doc.ontouchmove=null})),$on($doc,"mousemove,touchmove",(t=>{i=t.touches?t.touches[0]:t,o=r-i.clientX,n=a-i.clientY,r=i.clientX,a=i.clientY,e.style.top=e.offsetTop-n+"px",e.style.left=e.offsetLeft-o+"px"}))}t?(t.onmousedown=i,t.ontouchstart=i):(e.onmousedown=i,e.ontouchstart=i)},$numFormat=(e,t)=>{const o=[{value:1,symbol:""},{value:1e3,symbol:"k"},{value:1e6,symbol:"M"},{value:1e9,symbol:"G"},{value:1e12,symbol:"T"},{value:1e15,symbol:"P"},{value:1e18,symbol:"E"}].findLast((t=>e>=t.value));return o?(e/o.value).toFixed(t).replace(/\.0+$|(?<=\.[0-9]*[1-9])0+$/,"").concat(o.symbol):"0"},_$_$debounceStore={},$debounce=(e,t,o=null)=>{let n;const r=o??e.toString();_$_$debounceStore.$loop(((e,t)=>{if(r===e)return n=t,"break";n=!1})),n&&(clearTimeout(n),delete _$_$debounceStore[n]);const a=setTimeout((()=>{delete _$_$debounceStore[a],e()}),t);_$_$debounceStore[a]=r},$copyToClipboard=(e,t="Copied to clipboard")=>{if(navigator.clipboard)return navigator.clipboard.writeText(e).catch((()=>osNote("Cannot copy, enable clipboard permission from your setting and try again","warn"))).then((e=>osNote(t,"success"))),!0;try{if("DEV"!==$id("LAY-ENVIRONMENT").content)return console.warn(`You tried to use a deprecated way to copy a string in a non dev environment. String: \n ${e}`),!0}catch(t){return console.warn("Using a function that depends on core Lay features outside Lay framework"),console.warn(`You tried to use a deprecated way to copy a string in a non dev environment. String: \n ${e}`),!0}const o=document.createElement("textarea");o.value=e,o.setAttribute("readonly",""),o.style.position="absolute",o.style.left="-9999px",document.body.appendChild(o),o.select();try{document.execCommand("copy"),osNote(t,"success")}catch(e){console.warn(e)}finally{document.body.removeChild(o)}return!0},$preloader=(e=!0)=>($sel(".osai-preloader")||$html($sel("body"),"beforeend",'<div class="osai-preloader" style="display:none"><svg width="110" height="110" viewBox="0 0 110 110" fill="none" xmlns="http://www.w3.org/2000/svg">\n<path d="M33.7 0.419922C32.6768 0.428607 31.6906 0.803566 30.9201 1.47684C30.1496 2.15011 29.6458 3.07715 29.5 4.08992C28.5259 10.0187 25.4776 15.4087 20.8988 19.2989C16.32 23.1891 10.5083 25.3265 4.5 25.3299C3.37445 25.3352 2.2965 25.7846 1.50061 26.5805C0.704714 27.3764 0.25526 28.4544 0.25 29.5799V86.0499C0.25 89.2017 0.87078 92.3225 2.07689 95.2343C3.28301 98.1461 5.05083 100.792 7.27944 103.02C9.50804 105.249 12.1538 107.017 15.0656 108.223C17.9774 109.429 21.0983 110.05 24.25 110.05H75.6C76.6302 110.034 77.6202 109.647 78.388 108.96C79.1559 108.273 79.6501 107.332 79.78 106.31C80.584 100.96 83.0765 96.007 86.8938 92.1735C90.7112 88.34 95.6536 85.8266 101 84.9999C103.562 84.6132 106.168 84.6132 108.73 84.9999H109.73V24.4499C109.73 18.0847 107.201 11.9802 102.701 7.47936C98.1997 2.97849 92.0952 0.449923 85.73 0.449923L33.7 0.419922ZM61.57 79.9099H47.73C45.379 79.9099 43.051 79.4465 40.8792 78.5462C38.7074 77.6459 36.7343 76.3264 35.0728 74.663C33.4113 72.9996 32.0939 71.0251 31.1961 68.8523C30.2982 66.6794 29.8374 64.351 29.84 61.9999V46.7499C29.8387 44.6348 30.2542 42.5401 31.0627 40.5856C31.8712 38.6312 33.0569 36.8551 34.552 35.359C36.0472 33.863 37.8225 32.6761 39.7765 31.8664C41.7305 31.0567 43.8249 30.6399 45.94 30.6399H63.36C67.6326 30.6399 71.7303 32.3372 74.7515 35.3584C77.7727 38.3796 79.47 42.4773 79.47 46.7499V61.9999C79.4713 64.3514 79.0093 66.6801 78.1103 68.853C77.2113 71.0259 75.8931 73.0004 74.2308 74.6636C72.5685 76.3268 70.5947 77.6462 68.4223 78.5464C66.25 79.4466 63.9215 79.9099 61.57 79.9099Z" fill="url(#paint0_linear_29_21)"/>\n<defs>\n<linearGradient id="paint0_linear_29_21" x1="8.59" y1="74.8799" x2="109.29" y2="31.9699" gradientUnits="userSpaceOnUse">\n<stop stop-color="#53C3BD"/>\n<stop offset="0.47" stop-color="#739CD2"/>\n<stop offset="1" stop-color="#4C64AF"/>\n</linearGradient>\n</defs>\n</svg>\n<span>Loading...please wait</span></div></div>'),$sel(".osai-preloader-css")||$html($sel("head"),"beforeend",'<style type="text/css" class="osai-preloader-css">.osai-preloader{display: flex;position: fixed; flex-direction:column;width: 101vw;height: 101vh;justify-content: center;align-items: center;background: rgba(8,11,31,0.8);left: -5px;right: -5px;top: -5px;bottom: -5px;z-index:9993}.osai-preloader__container{display: table; text-align: center;margin:0;padding:0;}.osai-preloader svg{width: 80px;padding: 1px;border-radius: 5px;animation: pulse 2s infinite linear;transition: .6s ease-in-out}.osai-preloader span{color: #fff;text-align: center;margin-top: 10px;display: block}@keyframes pulse {0% {transform: scale(0.6);opacity: 0}33% {transform: scale(1);opacity: 1}100%{transform: scale(1.4);opacity: 0}}</style>'),$style($sel(".osai-preloader"),!0===e||"show"===e?"del":"display:none")),$curl=(e,t={},o=null)=>new Promise(((n,r)=>{"Object"===$type(e)&&(e=(t=e).action),"Function"===$type(t)&&(t={preload:t,type:"json"}),"String"===$type(t)&&(t={type:t}),"Boolean"===$type(o)&&(t.displayError=o,o=null);let a,i=!1,s=t.credential??t.credentials??!1,l=t.headers??{};try{l["Lay-Domain"]=$lay.page.domain,l["Lay-Domain-ID"]=$lay.page.domain_id}catch(e){console.warn("Using a Lay attribute outside a Lay Framework")}let d=t.content??"text/plain",c=t.method??"get",p=t.type??"text",u=t.return??t.type??null,f=t.alert??!1,b=t.displayError??!0,g=t.preload??(()=>"preload"),h=t.progress??(()=>"progress"),m=t.error??(()=>"error"),$=t.addError??(()=>"error"),x=t.debounce??0;x=x||0,o=t.data??t.form??o??null,t.timeout=t.timeout??{value:0};let y={value:t.timeout.value??t.timeout,then:(e,o)=>{clearTimeout(C),_("Request timed out, please try again later!",o),t.timeout.then&&t.timeout.then(e)}},v=t.loaded??(()=>"loaded"),w=t.abort??(()=>osNote("Request aborted!","warn")),_=(e,t,o=null)=>{if($omjsError("$curl",t.e??t.statusText),"error"===m(t.status,t,o)&&b){const t=e;let n="fail";try{e=o.message??e}catch(o){e=t}try{n=o.status.toLowerCase()}catch(e){n="fail"}f?alert(e):osNote(e,n,{duration:-1,showCopy:!0}),"error"!==$&&$()}r({statusText:t.e??t.statusText,xhr:t,status:t.status,response:o})};if(c=c.toUpperCase(),p=p.toLowerCase(),i=new XMLHttpRequest,!i)return;c="GET"===c&&o?"POST":c,i.withCredentials=s,i.timeout=y.value;let k=0,C=setInterval((()=>k++),1e3);const E=()=>{if(i.open(c,e,!0),$on(i.upload,"progress",(e=>h(e))),$on(i,"error",(()=>_("An error occurred "+i.statusText,i))),$on(i,"abort",w),$on(i,"timeout",(e=>y.then(e,i)),"on"),$on(i,"readystatechange",(e=>{let t=i.status;if(4===i.readyState)if(p=u??"json",0===t)k!==i.timeout/1e3&&_("Request failed! Ensure your network is stable enough then try again.",i);else{const o=t>199&&t<300,r=t>499;if(a="HEAD"===c?i:i.responseText??i.response,"HEAD"!==c&&("json"===p||"{"!==a.trim().substring(0,1)&&"["!==a.trim().substring(0,1)||(p="json"),"xml"===p&&(a=i.responseXML),"json"===p))try{a=JSON.parse(a)}catch(e){let n="The server sent a response that could not be parsed but returned a code: "+t;return o||(i.e=e,r&&(n="Server error, please contact support if problem persists")),_(n,i,a)}if(!o)return _(`Request Failed! Code: ${t}; Message: ${i.statusText}`,i,a);"loaded"!==v&&v(a,i,e),n(a,i,e)}})),o)switch($type(o)){case"String":case"Object":case"FormData":break;case"File":p="file";let e=o;(o=new FormData).append("file",e);break;default:(o=$getForm(o,!0)).hasFile?(o=o.file,p="file"):o="json"===p?o.object:o.string}t.xhrSetup&&t.xhrSetup(i);let r="application/x-www-form-urlencoded";switch(p){default:break;case"file":r=null;break;case"json":r="get"===c?r:"application/json",o=JSON.stringify(o);break;case"text":let e=o;"Object"===$type(o)&&(e="",$loop(o,((t,o)=>e+=o+"="+t+"&"))),o=e?.replace(/&+$/,"");break;case"xml":r="GET"!==c?"text/xml":r;break;case"custom":r="GET"!==c?d:r}r&&i.setRequestHeader("Content-Type",r),$loop(l,((e,t)=>i.setRequestHeader(t,e))),i.send(o),g()};if(x)return $debounce((()=>E()),x,e);E()})),$ajax=$curl,$freezeBtn=(e,t=!0,o=!0)=>{!0===t?($class(e,"add","disabled"),o&&$attr(e,"disabled","true")):($class(e,"del","disabled"),o&&$attr(e,"disabled","del"))},$frozen=e=>!(!$class(e,"has","disabled")&&!$attr(e,"disabled")),$freeze=(e,t,o=!0)=>{$frozen(e)||($freezeBtn(e,!0,o),t())},$osaiBox=(e="all")=>{$in($sel(".osai-gg-icon-abstract"))||$html($sel("head"),"beforeend",'<style class="osai-gg-icon-abstract">.osai-dialogbox,.osai-notifier {\n\t\t/*normal variant*/\n\t\t--text: #fffffa;\n\t\t--bg: #1d2124;\n\t\t--link: #009edc;\n\t\t--info: #445ede;\n\t\t--warn: #ffde5c;\n\t\t--fail: #f40204;\n\t\t--fade: #e2e2e2;\n\t\t--success: #0ead69;\n\t\t/*dark variant*/\n\t\t--dark-text: #f5f7fb;\n\t\t--dark-link: #00506e;\n\t\t--dark-info: #3247ac;\n\t\t--dark-warn: #626200;\n\t\t--dark-fail: #a20002;\n\t\t--dark-success: #104e00;\n\t -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; box-sizing: border-box; scroll-behavior: smooth;} .gg-copy{box-sizing: border-box;position: relative;display: block;transform: scale(var(--ggs, 1));width: 14px;height: 18px;border: 2px solid;margin-left: -5px;margin-top: -4px}.gg-copy::after,.gg-copy::before{content: "";display: block;box-sizing: border-box;position: absolute}.gg-copy::before{background: linear-gradient(to left, currentColor 5px, transparent 0) no-repeat right top/5px 2px, linear-gradient(to left, currentColor 5px, transparent 0) no-repeat left bottom/ 2px 5px;box-shadow: inset -4px -4px 0 -2px;bottom: -6px;right: -6px;width: 14px;height: 18px}.gg-copy::after{ width: 6px; height: 2px; background: currentColor; left: 2px; top: 2px; box-shadow: 0 4px 0, 0 8px 0}\n        .gg-bell,.gg-bell::before{border-top-left-radius:100px;border-top-right-radius:100px}.gg-bell{box-sizing:border-box;position:relative;display:block;transform:scale(var(--ggs,1));border:2px solid;border-bottom:0;width:14px;height:14px}.gg-bell::after,.gg-bell::before{content:"";display:block;box-sizing:border-box;position:absolute}.gg-bell::before{background:currentColor;width:4px;height:4px;top:-4px;left:3px}.gg-bell::after{border-radius:3px;width:16px;height:10px;border:6px solid transparent;border-top:1px solid transparent;box-shadow:inset 0 0 0 4px,0 -2px 0 0;top:14px;left:-3px;border-bottom-left-radius:100px;border-bottom-right-radius:100px}.gg-check{box-sizing:border-box;position:relative;display:block;transform:scale(var(--ggs,1));width:22px;height:22px;border:2px solid transparent;border-radius:100px}.gg-check::after{content:"";display:block;box-sizing:border-box;position:absolute;left:3px;top:-1px;width:6px;height:10px;border-width:0 2px 2px 0;border-style:solid;transform-origin:bottom left;transform:rotate(45deg)}.gg-check-o{box-sizing:border-box;position:relative;display:block;transform:scale(var(--ggs,1));width:22px;height:22px;border:2px solid;border-radius:100px}.gg-check-o::after{content:"";display:block;box-sizing:border-box;position:absolute;left:3px;top:-1px;width:6px;height:10px;border-color:currentColor;border-width:0 2px 2px 0;border-style:solid;transform-origin:bottom left;transform:rotate(45deg)}.gg-bulb{box-sizing:border-box;position:relative;display:block;transform:scale(var(--ggs,1));width:16px;height:16px;border:2px solid;border-bottom-color:transparent;border-radius:100px}.gg-bulb::after,.gg-bulb::before{content:"";display:block;box-sizing:border-box;position:absolute}.gg-bulb::before{border-top:0;border-bottom-left-radius:18px;border-bottom-right-radius:18px;top:10px;border-bottom:2px solid transparent;box-shadow:0 5px 0 -2px,inset 2px 0 0 0,inset -2px 0 0 0,inset 0 -4px 0 -2px;width:8px;height:8px;left:2px}.gg-bulb::after{width:12px;height:2px;border-left:3px solid;border-right:3px solid;border-radius:2px;bottom:0;left:0}.gg-danger{box-sizing:border-box;position:relative;display:block;transform:scale(var(--ggs,1));width:20px;height:20px;border:2px solid;border-radius:40px}.gg-danger::after,.gg-danger::before{content:"";display:block;box-sizing:border-box;position:absolute;border-radius:3px;width:2px;background:currentColor;left:7px}.gg-danger::after{top:2px;height:8px}.gg-danger::before{height:2px;bottom:2px}.gg-dark-mode{box-sizing:border-box;position:relative;display:block;transform:scale(var(--ggs,1));border:2px solid;border-radius:100px;width:20px;height:20px}\n        .gg-close-o{box-sizing:border-box;position:relative;display:block;transform:scale(var(--ggs,.9));width:22px;height:22px;border:2px solid;border-radius:40px}.gg-close-o::after,.gg-close-o::before{content:"";display:block;box-sizing:border-box;position:absolute;width:12px;height:2px;background:currentColor;transform:rotate(45deg);border-radius:5px;top:8px;left:3px}.gg-close-o::after{transform:rotate(-45deg)}\n        .gg-close{box-sizing:border-box;position:relative;display:block;transform:scale(var(--ggs,1));width:22px;height:22px;border:2px solid transparent;border-radius:40px}.gg-close::after,.gg-close::before{content:"";display:block;box-sizing:border-box;position:absolute;width:16px;height:2px;background:currentColor;transform:rotate(45deg);border-radius:5px;top:8px;left:1px}.gg-close::after{transform:rotate(-45deg)}.gg-add-r{box-sizing:border-box;position:relative;display:block;width:22px;height:22px;border:2px solid;transform:scale(var(--ggs,1));border-radius:4px}.gg-add-r::after,.gg-add-r::before{content:"";display:block;box-sizing:border-box;position:absolute;width:10px;height:2px;background:currentColor;border-radius:5px;top:8px;left:4px}.gg-add-r::after{width:2px;height:10px;top:4px;left:8px}.gg-add{box-sizing:border-box;position:relative;display:block;width:22px;height:22px;border:2px solid;transform:scale(var(--ggs,1));border-radius:22px}.gg-add::after,.gg-add::before{content:"";display:block;box-sizing:border-box;position:absolute;width:10px;height:2px;background:currentColor;border-radius:5px;top:8px;left:4px}.gg-add::after{width:2px;height:10px;top:4px;left:8px}.gg-adidas{position:relative;box-sizing:border-box;display:block;width:23px;height:15px;transform:scale(var(--ggs,1));overflow:hidden}\n        </style>');let t={},o={};if("all"===e||"dialog"===e||"modal"===e){$in($sel(".osai-dialogbox__present"))||$html($sel("body"),"beforeend",'\n\t\t\t\t<div class="osai-dialogbox"><span style="display: none" class="osai-dialogbox__present"></span><div class="osai-dialogbox__overlay"></div><div class="osai-dialogbox__wrapper">\n                    <div class="osai-dialogbox__header"><button class="osai-dialogbox__close-btn"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor"></rect><rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor"></rect></svg></button></div>\n                    <div class="osai-dialogbox__head"></div>\n                    <div class="osai-dialogbox__inner-wrapper"><div class="osai-dialogbox__body"></div></div>\n                    <div class="osai-dialogbox__foot"></div>\n                </div></div>'),$in($sel(".osai-dialogbox__stylesheet"))||$html($sel("head"),"beforeend",'<style class="osai-dialogbox__stylesheet" rel="stylesheet" media="all">\n.osai-dialogbox{\nposition: fixed;\nright: 0; left: 0; top: 0; bottom: 0;\ndisplay: block;\nvisibility: hidden;\nopacity: 0;\nz-index: -9990;\n}\n.osai-dialogbox__appear{\n\tvisibility: visible;\n\tz-index: 9990;\n\topacity: 1;\n}\n.osai-dialogbox__overlay{\n\topacity: .5;\n\tposition: fixed;\n\ttop: 0;bottom: 0;left: 0;right: 0;\n\tbackground: var(--bg);\n\tz-index: 1;\n}\n.osai-dialogbox__wrapper{\n\tdisplay: flex;\n\topacity: 0;\n\tjustify-content: center;\n\talign-items: center;\n\tmax-width: 97vw;\n\tmax-height: 97vh;\n\ttransform: translate(-50%,0);\n\ttop: 50%; left: 50%;\n\tposition: absolute;\n\tz-index: 2;\n\tmargin: auto;\n    background: var(--dark-text);\n\tcolor: var(--bg);\n\tborder-radius: 10px;\n\tflex-flow: column;\n\ttransition: ease-in-out .8s all;\n\tpadding: 1.5rem;\n\tpadding-top: 0;\n\toverflow: hidden;\n}\n.osai-dialogbox__header{\n\twidth: 100%;\n\tpadding: 0;\n\tpadding-top: 1.5rem;\n\tcursor: move;\n}\n.osai-dialogbox__close-btn{\n    display: table;\n    width: auto;\n\tbackground: transparent;\n\tborder: none;\n\tcolor: var(--dark-info);\n\tfont-weight: 500;\n\tcursor: pointer;\n\toutline: none;\n\tmargin-left: auto;\n    position: relative;\n    z-index: 5;\n}\n.osai-dialogbox__close-btn:hover{\n\tcolor: var(--fail);\n}\n.osai-dialogbox__head{\n\tfont-size: 1.15em;\n\tline-height: 1.15em;\n\tpadding: 0;\n\tmargin-bottom: 1rem;\n\tfont-weight: 600;\n\twidth: 100%;\n}\n.osai-dialogbox__inner-wrapper{\n\toverflow: auto;\n\tmax-width: 100vw;\n\tpadding: 1.75rem 0;\n}\n.osai-dialogbox__body{\n\tfont-size: 1em;\n}\n.osai-dialogbox__foot{\n    padding: 0;\n}\n.osai-dialogbox__foot button.success{\n\tbackground: var(--success);\n\tcolor: var(--bg);\n} .osai-dialogbox__foot button.success:hover{\n\tbackground: var(--dark-success);\n\tcolor: var(--dark-text);}\n.osai-dialogbox__foot button.fail{\n\tbackground: var(--fail);\n\tcolor: var(--text);\n}.osai-dialogbox__foot button.fail:hover{\n\tbackground: var(--dark-fail);\n\tcolor: var(--text);}\n.osai-dialogbox__foot button.warn{\n\tbackground: var(--warn);\n\tcolor: var(--text);\n} .osai-dialogbox__foot button.warn:hover{\n\tbackground: var(--dark-warn);\n\tcolor: var(--text);}\n.osai-dialogbox__foot button.info{\n\tbackground: var(--info);\n\tcolor: var(--dark-text);\n} .osai-dialogbox__foot button.info:hover{\n\tbackground: var(--dark-info);\n\tcolor: var(--text);}\n.osai-dialogbox__foot button.link{\n\tbackground: var(--link);\n\tcolor: var(--dark-text);\n} .osai-dialogbox__foot button.link:hover{\n\tbackground: var(--dark-link);\n\tcolor: var(--text);}\n\t.osai-dialogbox__foot button.success i,.osai-dialogbox__foot button.fail i, .osai-dialogbox__foot button.warn i, .osai-dialogbox__foot button.info i,.osai-dialogbox__foot button.link i{\n    color: var(--dark-text)\n}\n/* disable scrolling when modal is opened */\n.osai-modal__open{\n\toverflow-y: hidden;\n\tscroll-behavior: smooth;\n}\n.osai-modal__appear{\n\topacity: 1;\n\ttransform: translate(-50%,-50%);\n}\n.osai-modal__btn{\n\tborder-radius: .755rem;\n\tborder: solid 1px transparent;\n\tpadding: 0.65rem 1.73rem;\n\tcursor: pointer;\n\toutline: none;\n\ttransition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n\tbackground-color: var(--bg);\n\tcolor: var(--text);\n\tdisplay: inline-flex;\n\tjustify-content: center;\n\talign-items: center;\n}\n@media screen and (max-width: 600px){\n\t.osai-dialogbox__wrapper{\n\t\tmin-width: 90vw;\n\t\tmax-width: 95vw;\n\t\tmax-height: 90vh;\n\t}\n}\n</style>');const e=$sel(".osai-dialogbox"),o=$sel(".osai-dialogbox__overlay"),n=$sel(".osai-dialogbox__wrapper"),r=$sel(".osai-dialogbox__header"),a=$sel(".osai-dialogbox__close-btn"),i=$sel(".osai-dialogbox__inner-wrapper"),s=$sel(".osai-dialogbox__head"),l=$sel(".osai-dialogbox__body"),d=$sel(".osai-dialogbox__foot"),c=$sel(".osai-dialogbox__present"),p=(t="close")=>{"close"===t&&($class(n,"del","osai-modal__appear"),setTimeout((()=>{$class(e,"del","osai-dialogbox__appear"),$class($sel("html"),"del","osai-modal__open")}),200)),"open"===t&&($class($sel("html"),"add","osai-modal__open"),$class(e,"add","osai-dialogbox__appear"),setTimeout((()=>$class(n,"add","osai-modal__appear")),100))},u=e=>{switch(e){case"xs":i.style.minWidth="30vw";break;case"sm":i.style.minWidth="45vw";break;case"md":i.style.minWidth="60vw";break;case"lg":i.style.minWidth="75vw";break;case"xl":i.style.minWidth="90vw";break;case"xxl":i.style.minWidth="99vw";break;default:let e=e=>$sel("input[data-config='"+e+"'].osai-dialogbox__config");e("box-size")&&"undefined"!==$data(e("box-size"),"value")?u($data(e("box-size"),"value")):i.style.minWidth="60vw"}},f=(t,i,c,f,b)=>{let g=e=>$sel("input[data-config='"+e+"'].osai-dialogbox__config");if(p("open"),u(i),g("main-wrapper")&&$style(n,$data(g("main-wrapper"),"value")),g("box-z-index")&&($style(e).zIndex=$data(g("box-z-index"),"value")),g("head")&&$style(s,$data(g("head"),"value")),g("header")&&$style(r,$data(g("header"),"value")),g("close")&&$style(a,$data(g("close"),"value")),g("foot")&&$style(d,$data(g("foot"),"value")),c?$style(l).textAlign=c:g("box-body-align")?$style(l).textAlign=$data(g("box-body-align"),"value"):$style(l).textAlign="inherit",g("body")&&$style(l,$data(g("body"),"value")),!t){let e=g("close-on-blur")?$data(g("close-on-blur"),"value"):void 0;"String"===$type(e)?t=JSON.parse(e):"Boolean"===$type(e)&&(t=e)}let m=()=>h(f);""===$html(d).trim()&&$style(d,"display:none"),""===$html(s).trim()&&$style(s,"display:none"),!1===t?$on(o,"click",m,"del"):$on(o,"click",m),$on(a,"click",m,"on"),$on(n,"click",((e,t)=>{($class(e.target,"has","osai-close-box")||$class(e.target?.parentNode,"has","osai-close-box"))&&(e.preventDefault(),m())}),"on"),$on($doc,"keydown",(e=>{27===e.keyCode&&(e.preventDefault(),m())}),"on"),$drag(e,r),b&&b()},b=(e="*")=>{switch($style(r,"del"),e){case"head":$html(s,"in",""),$style(s,"del");break;case"body":$html(l,"in",""),$style(l,"del");break;case"foot":$html(d,"in",""),$style(d,"del");break;default:$html(s,"in",""),$html(l,"in",""),$html(d,"in",""),$style(n,"del"),$style(s,"del"),$style(r,"del"),$style(l,"del"),$style(d,"del")}return this},g=(e,t="")=>{switch(e){case"head":e=s,$style(s,"del");break;case"body":e=l;break;case"foot":e=d;break;case"head+":e=s,$style(s,"del"),t=$html(s)+t;break;case"body+":e=l,t=$html(l)+t;break;case"foot+":e=d,t=$html(d)+t;break;default:return}$html(e,"in",t)},h=e=>(p("close"),setTimeout((()=>b()),250),"Function"===$type(e)&&e(),!1),m=(e,t=!0,o=(()=>null))=>{e(),t&&h(o)};t={render:(...e)=>(f(...e),t),flush:(e="*")=>(b(e),t),get:{box:e,head:s,close:a,header:r,foot:d,wrapper:i,wrap:n,body:l},config:({align:e,size:t,closeOnBlur:o,wrapper:n,head:r,header:a,foot:i,body:s,close:l,zIndex:d})=>{let p=(e,t)=>{let o=e=>$sel("input[data-config='"+e+"'].osai-dialogbox__config");o(e)?$data(o(e),"value",t):$html(c,"beforeend",`<input type="hidden" class="osai-dialogbox__config" data-config="${e}" data-value="${t}">`)};e&&p("box-body-align",e),t&&p("box-size",t),n&&p("main-wrapper",n),r&&p("head",r),a&&p("header",a),s&&p("body",s),i&&p("foot",i),l&&p("close",l),d&&p("box-z-index",d),"String"!==$type(o)&&"Boolean"!==$type(o)||p("close-on-blur",o)},insert:(e,o="")=>(g(e,o),t),closeBox:(e=(()=>null))=>(h(e),t),action:(e,t=!0)=>m(e,t)}}if("all"===e||"notifier"===e||"notify"===e){$in($sel(".osai-simple-notifier"))||$html($sel("body"),"beforeend",'<div class="osai-simple-notifier"><div style="display: none" class="osai-notifier__config_wrapper"></div></div>'),$in($sel(".osai-notifier__stylesheet"))||$html($sel("head"),"beforeend",'<style class="osai-notifier__stylesheet" rel="stylesheet" media="all">\n\t\t\t.osai-notifier{\n\t\t\t    line-height: normal;\n\t\t\t\tscroll-behavior: smooth;\n\t\t\t\tposition: fixed;\n\t\t\t\ttop: 10px;\n\t\t\t\tright: 10px;\n\t\t\t\tborder-radius: 0 10px 10px 0;\n\t\t\t\tpadding: 10px;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #000000;\n\t\t\t\tbackground-color: var(--text);\n\t\t\t\tborder-left: solid .5rem var(--bg);\n\t\t\t\tbox-shadow: 0 1px 2px rgba(0, 0, 0, .3);\n\t\t\t\tdisplay: flex;\n\t\t\t\topacity: 0;\n\t\t\t\ttransform: translate(50%, 0);\n\t\t\t\tz-index: 9993;\n\t\t\t\tmin-height: 50px;\n\t\t\t\tmin-width: 150px;\n\t\t\t\tjustify-content: center;\n\t\t\t\talign-items: center;\n                transition: ease-in-out all .5s;\n\t\t\t}\n\t\t\t.osai-notifier__dialog{\n\t\t\tpadding-left:10px;\n\t\t\t}\n\t\t\t.osai-notifier__copy{\n                border-radius: 5px;\n                padding: 8px 15px;\n                font-size: .8rem;\n                background: rgba(229,234,246,0.5);\n                transform: scale(.9);\n                margin-top: 10px;\n                display: flex;\n                gap: 10px;\n                justify-content: center;\n                align-items: center;\n                transition: all 0.55s ease-in-out;\n                border: outset;\n                color: #000000;\n\t\t\t}\n\t\t\t.osai-notifier__copy:hover{\n\t\t\t    border-color: transparent;\n\t\t\t}\n\t\t\t.osai-notifier__display{\n\t\t\t\topacity: 1;\n\t\t\t\ttransform: translate(0,0);\n\t\t\t\tmax-width: 50vw;\n\t\t\t}\n\t\t\t.osai-notifier__display-center{\n\t\t\t\ttop: 50%; \n\t\t\t\tleft: 50%;\n                right: auto;\n\t\t\t\ttransform: translate(-50%,-50%);\n\t\t\t} @media (max-width: 767px){\n                .osai-notifier__display-center{\n                    max-width: 60vw;\n                }\n            }\n            @media (max-width: 426px){\n            \t.osai-notifier__display-center{\n                    max-width: 93vw;\n                }\n                .osai-notifier__display{\n\t\t\t\t\tmax-width: 93vw;\n\t\t\t\t}\n            }\n\t\t\t.osai-notifier__close{\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 10px;\n\t\t\t\ttop: 10px;\n\t\t\t\tcursor: pointer;\n\t\t\t\topacity: .8;\n\t\t\t}\n\t\t\t.osai-notifier__close:hover{\n\t\t\t\topacity: 1;\n\t\t\t\tcolor: var(--fail);\n\t\t\t}\n\t\t\t.osai-notifier.success{\n\t\t\t\tborder-color: var(--success);\n\t\t\t}\n\t\t\t.osai-notifier.fail{\n\t\t\t\tborder-color: var(--fail);\n\t\t\t}\n\t\t\t.osai-notifier.warn{\n\t\t\t\tborder-color: var(--warn);\n\t\t\t}\n\t\t\t.osai-notifier.info{\n\t\t\t\tborder-color: var(--info);\n\t\t\t}\n\t\t\t.osai-notifier__body{\n\t\t\t\tpadding: 5px 26px 5px 36px;\n\t\t\t\tpadding-left: 0;\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t</style>');let e=".osai-simple-notifier",t=".osai-notifier-entry:not(.osai-notifier__display-center)";const n=(o,n,r)=>{if(!$in($sel(e)))return console.error("Omj$ Notifier could not be found, you probably didn't draw it's box"),!1;let a=t=>{let o=$sel("input.osai-notifier__config[data-config='"+t+"']",$sel(e));return o?$data(o,"value"):null};o=o??a("message")??"Simple notifier by Lay's Omj$",n=n??a("type");let i="",s="",l=(r=r??{}).position??a("position")??"side",d=r.id?`id="${r.id}"`:"",c=parseInt(r.duration??a("duration")??5e3),p=r.showCopy??a("showCopy")??!1,u=a("margin")??10,f=0,b=r.then??(()=>null),g=r.onClose??(()=>null),h=t=>{let o=t.previousElementSibling;return o===$sel(".osai-notifier__config_wrapper",$sel(e))?null:$class(o,"has","osai-notifier__display-center")?h(o):$class(o,"has","osai-notifier-entry")?o:null},m=e=>{try{let t=e.nextElementSibling;if($class(t,"has","osai-notifier__display-center"))return m(t);if($class(t,"has","osai-notifier-entry"))return t}catch(e){}return null},$=(e,t,o=!1)=>{if(!e||!t||e===t||!$class(t,"has","osai-notifier__display"))return;let n=h(e);!n&&$class(t,"has","osai-notifier__display-center")&&(t=m(t)),o&&n&&(e=n,o=!1);let r=o?0:parseInt($style(e).top.replace("px",""))+e.offsetHeight;y(t,null,r+u),$(t,t.nextElementSibling)},x=(e,t=!0,o=!1)=>{const n=(o,n=!1)=>setTimeout((()=>{t&&!n&&(setTimeout((()=>$class(e,"del","osai-notifier__display")),o),o+=50),$(e,e.nextElementSibling,!0),e.$class("del","osai-notifier__display"),setTimeout((()=>{e.remove();try{g()}catch(e){}}),150)}),o);if(o&&n(0,!0),"pin"===c||"fixed"===c||-1===c||!1===c)return;let r=n(c);$on(e,"mouseover",(()=>clearTimeout(r))),$on(e,"mouseout",(()=>{clearTimeout(r),r=n(c)}))},y=(e,t,o=null)=>{$on($sel(".osai-notifier__close",e),"click",(t=>{t.preventDefault(),x(e,!1,!0)})),$on($sel(".osai-notifier__copy",e),"click",(t=>{t.preventDefault(),$copyToClipboard($sel(".osai-notifier__dialog",e).textContent)}));try{b()}catch(e){}if(x(e),!o&&$class(e,"has","osai-notifier__display-center"))return;if(o)return $style(e,`top:${o}px`);let n=(t=parseInt(t))+u;if(0===t)return $style(e,"top:10px");$style(e,"top:"+n+"px")};switch("center"===l&&(s="osai-notifier__display-center"),$sel(t)&&(f=(()=>{let e=0;return $loop($sela(t),(t=>e+=Math.floor(t.offsetHeight+u))),e})()),n){case"success":case"good":i="success";break;case"fail":case"danger":case"error":i="fail";break;case"info":i="info";break;case"warn":case"warning":i="warn"}$html($sel(e),"beforeend",`<div class="osai-notifier osai-notifier-entry ${s} ${i}" ${d}><div class="osai-notifier__body"><div class="osai-notifier__dialog">${o}</div><div style="${p?"":"display:none"}"><button class="osai-notifier__copy"><i class="gg-copy"></i> Copy Text</button></div></div><div class="osai-notifier__close"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor"></rect><rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor"></rect></svg></div></div>`);let v=$sela(".osai-notifier-entry"),w=$end(v);return y(w,f),setTimeout((()=>{$class(w,"add","osai-notifier__display")}),200),w},r=$sel(".osai-notifier__config_wrapper");o={notify:(e,t,o)=>n(e,t,o),notifyConfig:({duration:e,type:t,position:o,message:n,margin:a,showCopy:i})=>{let s=(e,t)=>{if(!t)return;let o=e=>$sel("input[data-config='"+e+"'].osai-notifier__config_wrapper");o(e)?$data(o(e),"value",t):$html(r,"beforeend",`<input type="hidden" class="osai-notifier__config" data-config="${e}" data-value="${t}">`)};s("duration",e),s("type",t),s("position",o),s("margin",a),s("message",n?.replaceAll('"',"'")),s("showCopy",i)}}}return{...t,...o}},CusWind=$osaiBox();function aMsg(e,t={head:"Alert Box",showButton:!0,closeOnBlur:null,size:"sm",align:null,onClose:null}){CusWind.insert("body",e),!1===t.showButton?CusWind.flush("head").flush("foot"):CusWind.insert("head",t.head).insert("foot",'<button type="button" class="success osai-modal__btn osai-close-box"><i class=\'gg-check\'></i></button>'),CusWind.render(t.closeOnBlur,t.size,t.align,t.onClose)}function cMsg(e,t,o={closeOnDone:!0,closeOnBlur:null,size:"sm",align:null,onClose:()=>null}){CusWind.insert("head","Confirmation Box").insert("body",e).insert("foot",'<div style="display: flex; gap: 5px"><button type="button" class="success osai-modal__btn osai-confirm-success"><i class="gg-check"></i></button>\n\t\t<button type="button" class="fail osai-modal__btn osai-close-box"><i class="gg-close"></i></button></div>').render(o.closeOnBlur,o.size,o.align,o.onClose),$on($sel(".osai-confirm-success"),"click",(e=>{e.preventDefault(),CusWind.action(t,o.closeOnDone)}))}function pMsg(e="Prompt Box",t=(e=>e),o={body:null,operation:null,closeOnDone:!0,closeOnBlur:null,size:null,align:null,onClose:()=>null}){CusWind.insert("head",e).insert("body",o.body||"<textarea class='osai-prompt-input-box form-control' style='width: 100%; height: 50px; text-align: center' placeholder='Type in...'></textarea>").insert("foot",'<div style="display: flex; gap: 5px"><button type="button" class="success osai-modal__btn osai-confirm-success"><i class="gg-check"></i></button><button type="button" class="fail osai-close-box osai-modal__btn"><i class="gg-close"></i></button></div>').render(o.closeOnBlur,o.size,o.align,o.onClose),$on($sel(".osai-confirm-success"),"click",(e=>{e.preventDefault(),$sel(".osai-prompt-input-box")?$sel(".osai-prompt-input-box").value&&CusWind.action((()=>t($sel(".osai-prompt-input-box").value)),o.closeOnDone):CusWind.action(o.operation,o.closeOnDone)}))}function osModal(e={}){return e={head:e.head??"osModal",body:e.body??"Osai Modal Box Built With OMJ$",foot:e.foot??null,operation:e.operation?e.operation:()=>null,onClose:e.onClose?e.onClose:()=>null,then:e.then?e.then:()=>null,append:e.append??!1,closeOnBlur:e.closeOnBlur,size:e.size,align:e.align},CusWind.insert("head",e.head).insert(!0===e.append?"body+":"body",e.body).insert("foot",null!==e.foot?e.foot:'<button type="button" class="fail osai-close-box osai-modal__btn"><i class="gg-close-o"></i></button>').render(e.closeOnBlur,e.size,e.align,e.onClose),e.operation()??e.then()}function osNote(e,t,o){CusWind.notify(e,t,o)}