<?php

use <PERSON><PERSON><PERSON>er\Lay\Core\LayConfig;
use BrickLayer\Lay\Libs\LayFn;

include_once __DIR__ . DIRECTORY_SEPARATOR . "vendor" .  DIRECTORY_SEPARATOR . "autoload.php";

LayConfig::validate_lay();

$sess = [
    "http_only" => true,
//    "only_cookies" => true,
    "secure" => true,
    "samesite" => 'None',
];

if(LayConfig::$ENV_IS_PROD) {
    $sess['lifetime'] = 0;
    $sess['path'] = "/";
    $sess['domain'] = "localhost";
}

LayConfig::session_start($sess);

LayConfig::set_cors(
    // Specify where to allow requests from
    allowed_origins: [
        "http://localhost",
    ],

    // Allow requests from all origins
    allow_all: true,

    // Headers to add to the response
    fun: function () {
        LayFn::header("Access-Control-Allow-Credentials: true");
        LayFn::header("Access-Control-Allow-Headers: *");
        LayFn::header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
        LayFn::header('Access-Control-Max-Age: 86400');    // cache for 1 day
    }
);

///// Project Configuration

$site_name = "Inside Edge Consulting Limited";

LayConfig::new()
    ->init_name($site_name, "$site_name | Empowering Education, Enriching Futures")
    ->init_color("#92A0BE", "#747BAF")
    ->init_mail("<EMAIL>", "<EMAIL>", "<EMAIL>")
    ->init_tel(
        "+2349114932332", "+234 ************",
    )
    ->init_author($site_name)
    ->init_copyright("&copy; " . date('Y') . "; All rights reserved <a href='https://www.inside-edgeconsulting.com'>$site_name</a>")

    // Store non-sensitive data ands access it anywhere in the project by calling the `LayConfig::site_data()->others` method.
    // If you have a value that persists both on local environment and production, use this
    ->init_others([
        "desc" => (
            "Inside Edge Consulting Limited is an educational consultancy that empowers students, supports educators, and partners with institutions to create global education success."
        ),
        "bucket_domain" => "https://static.inside-edgeconsulting.com/",
        "founder" => "Mrs. Lebari Ukpong",
        "addr" => "Suite D26, Dolphin Plaza Corporation Drive, Dolphin Estate, Lagos, Nigeria",
        "map_link" => "https://maps.app.goo.gl/uWXCiXaHvvJaSU5H9",
        "yt_video" => "https://www.youtube.com/watch?v=MLpWrANjFbI&amp;ab_channel=eidelchteinadvogados",
        "whatsapp" => "https://api.whatsapp.com/send/?phone=%2B2349114932332&text=Hello+I+want+to+make+an+enquiry&type=phone_number&app_absent=0",
        "social" => [
            "ig" => [
                "url" => "http://instagram.com/insideedgeconsulting",
                "img" => "fa-instagram"
            ],
            "lk" => [
                "url" => "https://ng.linkedin.com/company/inside-edge-consulting-nigeria",
                "img" => "fa-linkedin-in"
            ],
            "x" => [
                "url" => "http://x.com/insideedge_ng",
                "img" => "fa-x-twitter"
            ],
        ]
    ])
    ->init_orm(false)
    ->ignore_file_extensions("xml")
->init_end();

\BrickLayer\Lay\Core\LayException::error_as_html();